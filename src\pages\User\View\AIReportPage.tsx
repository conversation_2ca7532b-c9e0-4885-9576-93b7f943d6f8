import React, { useEffect, useState, useRef } from "react";
import ReviewOrderModal, { ReviewSection } from "./ReviewOrderModal";
import { useNavigate } from "react-router-dom";
import MkdSDK from "../../../utils/MkdSDK";
import logo from "../../../assets/images/deanna_logo.png";

// Job status types
type JobStatus = 0 | 1 | 2 | 3; // pending, running, processing, completed

interface JobStatusResponse {
  job_id: string;
  status: JobStatus;
  status_text: string;
  created_at: string;
  updated_at: string;
  arguments: {
    project_id: number;
    user_id: number;
    patient_id: string;
  };
  report?: {
    id: string;
    [key: string]: any;
  };
}

// Processing step type
interface ProcessingStep {
  status: "complete" | "in-progress" | "pending";
  label: string;
}

const ProcessingDocuments: React.FC<{
  jobStatus?: JobStatusResponse | null;
}> = ({ jobStatus }) => {
  const processingSteps: ProcessingStep[] = [
    { status: "complete", label: "Document preprocessing complete" },
    { status: "complete", label: "Test identification complete" },
    {
      status:
        jobStatus?.status === 1
          ? "in-progress"
          : jobStatus?.status === 3
            ? "complete"
            : "pending",
      label: "Generating comprehensive report",
    },
  ];

  const renderStatusIcon = (status: ProcessingStep["status"]) => {
    if (status === "complete") {
      return (
        <svg
          width={17}
          height={16}
          viewBox="0 0 17 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <g clipPath="url(#clip0_1_538)">
            <path
              d="M8.76562 16C10.8874 16 12.9222 15.1571 14.4225 13.6569C15.9228 12.1566 16.7656 10.1217 16.7656 8C16.7656 5.87827 15.9228 3.84344 14.4225 2.34315C12.9222 0.842855 10.8874 0 8.76562 0C6.64389 0 4.60906 0.842855 3.10877 2.34315C1.60848 3.84344 0.765625 5.87827 0.765625 8C0.765625 10.1217 1.60848 12.1566 3.10877 13.6569C4.60906 15.1571 6.64389 16 8.76562 16ZM12.2969 6.53125L8.29688 10.5312C8.00313 10.825 7.52813 10.825 7.2375 10.5312L5.2375 8.53125C4.94375 8.2375 4.94375 7.7625 5.2375 7.47188C5.53125 7.18125 6.00625 7.17813 6.29688 7.47188L7.76562 8.94063L11.2344 5.46875C11.5281 5.175 12.0031 5.175 12.2937 5.46875C12.5844 5.7625 12.5875 6.2375 12.2937 6.52812L12.2969 6.53125Z"
              fill="#059669"
            />
          </g>
          <defs>
            <clipPath id="clip0_1_538">
              <path d="M0.765625 0H16.7656V16H0.765625V0Z" fill="white" />
            </clipPath>
          </defs>
        </svg>
      );
    }
    if (status === "in-progress") {
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="21"
          viewBox="0 0 23 24"
          fill="none"
        >
          <path
            d="M13.8406 3.00773C13.8406 2.45737 13.622 1.92956 13.2328 1.5404C12.8437 1.15124 12.3159 0.932617 11.7655 0.932617C11.2151 0.932617 10.6873 1.15124 10.2982 1.5404C9.90902 1.92956 9.69039 2.45737 9.69039 3.00773C9.69039 3.55808 9.90902 4.0859 10.2982 4.47505C10.6873 4.86421 11.2151 5.08284 11.7655 5.08284C12.3159 5.08284 12.8437 4.86421 13.2328 4.47505C13.622 4.0859 13.8406 3.55808 13.8406 3.00773ZM13.8406 20.992C13.8406 20.4417 13.622 19.9139 13.2328 19.5247C12.8437 19.1355 12.3159 18.9169 11.7655 18.9169C11.2151 18.9169 10.6873 19.1355 10.2982 19.5247C9.90902 19.9139 9.69039 20.4417 9.69039 20.992C9.69039 21.5424 9.90902 22.0702 10.2982 22.4594C10.6873 22.8485 11.2151 23.0671 11.7655 23.0671C12.3159 23.0671 12.8437 22.8485 13.2328 22.4594C13.622 22.0702 13.8406 21.5424 13.8406 20.992ZM2.77335 14.075C3.32371 14.075 3.85152 13.8564 4.24068 13.4672C4.62984 13.078 4.84846 12.5502 4.84846 11.9999C4.84846 11.4495 4.62984 10.9217 4.24068 10.5326C3.85152 10.1434 3.32371 9.92477 2.77335 9.92477C2.223 9.92477 1.69519 10.1434 1.30603 10.5326C0.916869 10.9217 0.698242 11.4495 0.698242 11.9999C0.698242 12.5502 0.916869 13.078 1.30603 13.4672C1.69519 13.8564 2.223 14.075 2.77335 14.075ZM22.8328 11.9999C22.8328 11.4495 22.6141 10.9217 22.225 10.5326C21.8358 10.1434 21.308 9.92477 20.7577 9.92477C20.2073 9.92477 19.6795 10.1434 19.2903 10.5326C18.9012 10.9217 18.6825 11.4495 18.6825 11.9999C18.6825 12.5502 18.9012 13.078 19.2903 13.4672C19.6795 13.8564 20.2073 14.075 20.7577 14.075C21.308 14.075 21.8358 13.8564 22.225 13.4672C22.6141 13.078 22.8328 12.5502 22.8328 11.9999ZM6.87602 19.8248C7.06876 19.632 7.22166 19.4032 7.32597 19.1514C7.43028 18.8996 7.48397 18.6296 7.48397 18.3571C7.48397 18.0845 7.43028 17.8146 7.32597 17.5627C7.22166 17.3109 7.06876 17.0821 6.87602 16.8894C6.68328 16.6966 6.45446 16.5437 6.20263 16.4394C5.9508 16.3351 5.68089 16.2814 5.40831 16.2814C4.85781 16.2814 4.32986 16.5001 3.9406 16.8894C3.55134 17.2786 3.33266 17.8066 3.33266 18.3571C3.33266 18.9076 3.55134 19.4355 3.9406 19.8248C4.32986 20.214 4.85781 20.4327 5.40831 20.4327C5.95881 20.4327 6.48676 20.214 6.87602 19.8248ZM6.87602 7.10607C7.07834 6.91558 7.24034 6.68638 7.3524 6.43209C7.46446 6.17779 7.5243 5.90358 7.52838 5.62573C7.53246 5.34787 7.48068 5.07202 7.37613 4.81455C7.27158 4.55708 7.11638 4.32323 6.91974 4.12687C6.72309 3.93052 6.48902 3.77567 6.23139 3.67149C5.97377 3.56732 5.69784 3.51595 5.41999 3.52044C5.14214 3.52493 4.86801 3.58517 4.61389 3.69761C4.35976 3.81005 4.1308 3.97238 3.9406 4.17498C3.55134 4.56424 3.33266 5.09219 3.33266 5.64269C3.33266 6.19319 3.55134 6.72114 3.9406 7.1104C4.32986 7.49966 4.85781 7.71834 5.40831 7.71834C5.95881 7.71834 6.48676 7.49966 6.87602 7.1104V7.10607ZM16.655 19.8248C16.8477 20.0175 17.0765 20.1704 17.3284 20.2747C17.5802 20.379 17.8501 20.4327 18.1227 20.4327C18.3953 20.4327 18.6652 20.379 18.917 20.2747C19.1688 20.1704 19.3977 20.0175 19.5904 19.8248C19.7831 19.632 19.936 19.4032 20.0403 19.1514C20.1447 18.8996 20.1983 18.6296 20.1983 18.3571C20.1983 18.0845 20.1447 17.8146 20.0403 17.5627C19.936 17.3109 19.7831 17.0821 19.5904 16.8894C19.3977 16.6966 19.1688 16.5437 18.917 16.4394C18.6652 16.3351 18.3953 16.2814 18.1227 16.2814C17.8501 16.2814 17.5802 16.3351 17.3284 16.4394C17.0765 16.5437 16.8477 16.6966 16.655 16.8894C16.4622 17.0821 16.3094 17.3109 16.205 17.5627C16.1007 17.8146 16.047 18.0845 16.047 18.3571C16.047 18.6296 16.1007 18.8996 16.205 19.1514C16.3094 19.4032 16.4622 19.632 16.655 19.8248Z"
            fill="#1F2937"
          />
        </svg>
      );
    }
    return null;
  };

  // Get progress percentage based on job status
  const getProgressPercentage = (status?: JobStatus): number => {
    if (!status) return 25;
    switch (status as JobStatus) {
      case 0:
        return 10;
      case 1:
        return 60;
      case 2:
        return 100;
      case 3:
        return 100;
      default:
        return 25;
    }
  };

  const progressPercentage = getProgressPercentage(jobStatus?.status);

  return (
    <div className="flex flex-col flex-shrink-0 items-center pb-[404px] w-full h-full border-0 border-gray-200 bg-gray-50">
      <div className="flex flex-shrink-0 justify-center items-center p-6 w-full  border-0 border-gray-200 bg-black/0">
        <div className="flex flex-col flex-shrink-0 justify-center items-start gap-6  border-0 border-gray-200 bg-black/0">
          {/* Main Content */}
          <div className="flex flex-shrink-0 justify-center items-center p-6 min-w-[1136px] h-[900px] rounded-lg border-0 border-gray-200 bg-white">
            <div className="flex flex-col items-center gap-8 w-[1088px] h-[852px] border-0 border-gray-200 bg-black/0">
              {/* Loading Spinner */}
              <div className="w-[10.0625rem] h-[10.0625rem] rounded-full border-b-4 border-b-[#1f2937] animate-spin" />

              {/* Title and Description */}
              <div className="flex flex-col items-center gap-4 w-[448px] h-[7.5rem] border-0 border-gray-200 bg-black/0">
                <div className="flex-shrink-0 w-[16.8125rem] h-8 text-gray-800 text-center font-['Inter'] text-2xl font-semibold leading-8">
                  Processing Documents
                </div>
                <div className="flex-shrink-0 w-[430px] h-[4.5rem] text-gray-600 text-center font-['Inter'] leading-6">
                  Our AI is analyzing the uploaded documents and generating a
                  comprehensive report. This may take a few minutes.
                </div>
              </div>

              {/* Progress Bar */}
              <div className="w-full max-w-[600px] h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gray-800 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>

              {/* Estimated Time */}
              <div className="flex flex-col items-center gap-2">
                <div className="flex items-center gap-2 opacity-50">
                  <svg
                    width={17}
                    height={16}
                    viewBox="0 0 17 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden="true"
                  >
                    <g clipPath="url(#clip0_1_526)">
                      <path
                        d="M5.95312 0.75C5.95312 0.334375 5.61875 0 5.20312 0C4.7875 0 4.45312 0.334375 4.45312 0.75V2C3.35 2 2.45312 2.89687 2.45312 4H1.20312C0.7875 4 0.453125 4.33437 0.453125 4.75C0.453125 5.16563 0.7875 5.5 1.20312 5.5H2.45312V7.25H1.20312C0.7875 7.25 0.453125 7.58437 0.453125 8C0.453125 8.41562 0.7875 8.75 1.20312 8.75H2.45312V10.5H1.20312C0.7875 10.5 0.453125 10.8344 0.453125 11.25C0.453125 11.6656 0.7875 12 1.20312 12H2.45312C2.45312 13.1031 3.35 14 4.45312 14V15.25C4.45312 15.6656 4.7875 16 5.20312 16C5.61875 16 5.95312 15.6656 5.95312 15.25V14H7.70312V15.25C7.70312 15.6656 8.0375 16 8.45312 16C8.86875 16 9.20312 15.6656 9.20312 15.25V14H10.9531V15.25C10.9531 15.6656 11.2875 16 11.7031 16C12.1187 16 12.4531 15.6656 12.4531 15.25V14C13.5562 14 14.4531 13.1031 14.4531 12H15.7031C16.1187 12 16.4531 11.6656 16.4531 11.25C16.4531 10.8344 16.1187 10.5 15.7031 10.5H14.4531V8.75H15.7031C16.1187 8.75 16.4531 8.41562 16.4531 8C16.4531 7.58437 16.1187 7.25 15.7031 7.25H14.4531V5.5H15.7031C16.1187 5.5 16.4531 5.16563 16.4531 4.75C16.4531 4.33437 16.1187 4 15.7031 4H14.4531C14.4531 2.89687 13.5562 2 12.4531 2V0.75C12.4531 0.334375 12.1187 0 11.7031 0C11.2875 0 10.9531 0.334375 10.9531 0.75V2H9.20312V0.75C9.20312 0.334375 8.86875 0 8.45312 0C8.0375 0 7.70312 0.334375 7.70312 0.75V2H5.95312V0.75Z"
                        fill="#4B5563"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_1_526">
                        <path
                          d="M0.453125 0H16.4531V16H0.453125V0Z"
                          fill="white"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                  <span className="text-gray-600 font-['Inter'] leading-6">
                    AI Analysis in Progress
                  </span>
                </div>
                <div className="text-gray-500 font-['Inter'] text-sm">
                  Estimated time remaining:{" "}
                  {jobStatus?.status === 0
                    ? "3-5 minutes"
                    : jobStatus?.status === 1
                      ? "1-2 minutes"
                      : "Almost done"}
                </div>
              </div>

              {/* Processing Steps */}
              <div className="flex flex-col gap-4">
                <div className="text-center text-gray-500 font-['Inter'] text-sm">
                  Processing steps:
                </div>
                <div className="flex flex-col gap-2">
                  {processingSteps.map((step, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="flex justify-center items-center h-4">
                        {renderStatusIcon(step.status)}
                      </div>
                      <div
                        className={`font-['Inter'] leading-6 ${step.status === "complete" ? "text-emerald-600" : "text-gray-800"}`}
                      >
                        {step.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// --- AI Generated Report ---

interface ReportSection {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  body: string;
  report_id: string;
  order?: number;
}

// Utility to extract client details from HTML string
function extractClientDetails(html: string) {
  if (typeof window === "undefined" || !html) return {};
  const parser = new window.DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  
  const getText = (label: string) => {
    // Find table row containing the label
    const rows = Array.from(doc.querySelectorAll("tr"));
    const targetRow = rows.find((row) => {
      const cells = row.querySelectorAll("td");
      if (cells.length >= 2) {
        const labelCell = cells[0];
        return labelCell.textContent
          ?.replace(/\s/g, "")
          .toLowerCase()
          .includes(label.replace(/\s/g, "").toLowerCase());
      }
      return false;
    });
    
    if (!targetRow) return "";
    
    // Get the value from the second cell
    const cells = targetRow.querySelectorAll("td");
    if (cells.length >= 2) {
      return cells[1].textContent?.trim() || "";
    }
    
    return "";
  };
  
  return {
    name: getText("Name"),
    dob: getText("D.O.B"),
    parents: getText("Parents"),
    address: getText("Address"),
    phone: getText("Phone"),
    school: getText("School"),
    schoolBoard: getText("School Board"),
    gradeProgram: getText("Grade/Program"),
    assessmentBy: getText("Assessment by"),
    assessmentDates: getText("Assessment Dates"),
    ageAtTesting: getText("Age at testing"),
  };
}

const AIGeneratedReport: React.FC<{
  onProceedReview: () => void;
  sections: ReportSection[] | null;
  isLoading: boolean;
  error: string | null;
}> = ({ onProceedReview, sections, isLoading, error }) => {
  // Find the client details section by title
  const findClientDetailsSection = (sections: ReportSection[]): ReportSection | null => {
    return sections.find(section => 
      section.title.toUpperCase().includes('PSYCHOLOGICAL REPORT')
    ) || null;
  };

  // Extract client details from the psychological report section if available
  const clientDetailsSection = sections ? findClientDetailsSection(sections) : null;
  const clientDetails = clientDetailsSection 
    ? extractClientDetails(clientDetailsSection.body)
    : {};

  // Filter out the client details section from the report sections to display
  const reportSections = sections 
    ? sections.filter(section => !section.title.toUpperCase().includes('PSYCHOLOGICAL REPORT'))
    : [];

  const ReportSection: React.FC<{
    title: string;
    content: string;
    styles?: string;
  }> = ({ title, content }) => (
    <div className="mb-6">
      <div className="section-title font-bold !text-lg mb-4 text-gray-800">
        {title}
      </div>
      <div
        className="custom mb-0 [&_table]:w-full [&_table]:border-collapse [&_table]:mb-4 [&_th]:border [&_th]:border-gray-200 [&_th]:p-2 [&_th]:text-left [&_th]:bg-gray-50 [&_th]:font-semibold [&_td]:border [&_td]:border-gray-200 [&_td]:p-2 [&_td]:text-left"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-600">Error loading report: {error}</div>
      </div>
    );
  }

  if (!sections || sections.length === 0) {
    return (
      <div className="flex min-h-screen items-center justify-center h-full">
        <div className="text-gray-600">No report data available</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-shrink-0 items-center pb-[396px] border-0 border-gray-200">
      <div className="flex flex-shrink-0 justify-center items-center p-6 border-0 border-gray-200 bg-black/0">
        <div className="flex flex-col flex-shrink-0 justify-center items-start gap-6 border-0 border-gray-200 bg-black/0">
          {/* Header */}
          <header className="flex flex-shrink-0 justify-between items-center p-4 w-full h-[4.5rem] rounded-lg border-0 border-gray-200 bg-white">
            <h1 className="flex justify-center items-center pr-[0.109px] w-[14.5625rem] h-8 text-gray-800 font-['Inter'] text-2xl leading-[normal]">
              AI Generated Report
            </h1>
            <button
              type="button"
              className="flex items-center gap-2 pb-[0.4375rem] pr-[1.125rem] pt-2 pl-4 h-10 rounded-md border-0 border-gray-200 bg-gray-800 text-white"
              onClick={onProceedReview}
            >
              <svg
                width={13}
                height={16}
                viewBox="0 0 13 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
              >
                <g clipPath="url(#clip0_1_619)">
                  <path
                    d="M6.875 0C5.56875 0 4.45625 0.834375 4.04688 2H2.875C1.77188 2 0.875 2.89687 0.875 4V14C0.875 15.1031 1.77188 16 2.875 16H10.875C11.9781 16 12.875 15.1031 12.875 14V4C12.875 2.89687 11.9781 2 10.875 2H9.70312C9.29375 0.834375 8.18125 0 6.875 0Z"
                    fill="white"
                  />
                </g>
              </svg>
              Proceed to Psychometrist Review
            </button>
          </header>

          {/* Main Content */}
          <main className="flex flex-shrink-0 justify-center items-center p-6 rounded-lg border-0 border-gray-200 bg-white overflow-y-auto w-full">
            <div className="w-full max-w-[1200px] border-0 border-gray-200 bg-black/0">
              {/* Report Template Structure */}
              <div className="">
                {/* Header Section */}
                <div className="flex border-b border-gray-300 p-5">
                  <div className="w-32">
                    <div className="w-24 h-16 bg-gray-300 flex items-center justify-center text-xs text-gray-600 rounded">
                      <img src={logo} alt="logo" />
                    </div>
                  </div>
                  <div className="flex-1 text-right">
                    <h1 className="text-[#1ca3a3] m-0 text-3xl font-bold">
                      Gilmour Psychological Services®
                    </h1>
                    <div className="text-gray-700">
                      421 Gilmour Street, Ottawa, ON K2P 0R5
                    </div>
                    <div className="text-gray-700">
                      Tel: ************ &nbsp; Fax: ************
                    </div>
                    <div className="text-gray-700">
                      <a
                        href="http://www.ottawa-psychologists.com"
                        className="text-blue-600 hover:underline"
                      >
                        www.ottawa-psychologists.com
                      </a>
                    </div>
                  </div>
                </div>

                {/* Sidebar and Main Content for the first section */}
                <div className="flex">
                  {/* Sidebar */}
                  <div className="w-56 border-r border-gray-300 p-5 bg-gray-50">
                    <div className="font-bold text-gray-800 mb-3">
                      Psychologists
                    </div>
                    <ul className="m-0 p-0 list-none">
                      <li className="mb-2 text-sm">
                        Dr. Iris Jackson &nbsp; Ext. 124
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Karen Davies &nbsp; Ext. 126
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Alex Weinberger &nbsp; Ext. 136
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Qadeer Ahmad &nbsp; Ext. 129
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Peter Judge &nbsp; Ext. 132
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Deanna Drahovzal &nbsp; Ext. 146
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Sarah Pantin &nbsp; Ext. 150
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Marc Zahradnik &nbsp; Ext. 142
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Caroline Ostiguy &nbsp; Ext. 140
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Jessica Henry &nbsp; Ext. 155
                      </li>
                      <li className="mb-2 text-sm">
                        Dr. Delyana Miller &nbsp; Ext. 143
                      </li>
                    </ul>
                  </div>
                  {/* Main Content */}
                  <div className="flex-1 p-8">
                    <div className="text-center text-xl font-bold mt-0 mb-2">
                      PSYCHOLOGICAL REPORT
                    </div>
                    <div className="text-center font-bold mb-6">
                      (Private and Confidential)
                    </div>
                    {/* Client Details Section */}
                    <div className="mb-8">
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          Name:
                        </span>
                        <span className="text-gray-700">
                          {clientDetails.name || "ClientName ClientLastName"}
                        </span>
                      </div>
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          D.O.B.:
                        </span>
                        <span className="text-gray-700">
                          {clientDetails.dob || "January 25, 1968"}
                        </span>
                      </div>
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          Address:
                        </span>
                        <span className="text-gray-700">
                          {clientDetails.address || "x Ottawa, ON Kx"}
                        </span>
                      </div>
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          Telephone:
                        </span>
                        <span className="text-gray-700">
                          {clientDetails.phone || "613-x"}
                        </span>
                      </div>
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          Assessment by:
                        </span>
                        <span className="text-gray-700">
                          {clientDetails.assessmentBy ||
                            "Dr. Deanna Drahovzal (Psychologist), Ms. Ex (Psychometrist)"}
                        </span>
                      </div>
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          Assessment dates:
                        </span>
                        <div className="text-gray-700 ml-40">
                          {clientDetails.assessmentDates || (
                            <>
                              April 5, 2022 (intake interview)
                              <br />
                              July 12 and 19, 2022 (testing)
                              <br />
                              October 27, 2022 (clinical interview)
                            </>
                          )}
                        </div>
                      </div>
                      <div className="mb-2">
                        <span className="font-bold inline-block w-40">
                          Age at testing:
                        </span>
                        <span className="text-gray-700">
                          {clientDetails.ageAtTesting ||
                            "59 years and x months"}
                        </span>
                      </div>
                    </div>
                    {/* First Report Section */}
                    {reportSections.length > 0 && (
                      <ReportSection
                        key={reportSections[0].id}
                        title={reportSections[0].title}
                        content={reportSections[0].body}
                      />
                    )}
                  </div>
                </div>

                {/* Render the rest of the sections below, without sidebar */}
                {reportSections.length > 1 &&
                  reportSections.slice(1).map((section) => (
                    <div
                      key={section.id}
                      className=""
                      style={{ pageBreakBefore: "always" }}
                    >
                      <ReportSection
                        title={section.title}
                        content={section.body}
                      />
                    </div>
                  ))}
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

// --- Main Page Component ---

const AIReportPage: React.FC = () => {
  const [showReport, setShowReport] = useState(false);
  const [showReviewOrder, setShowReviewOrder] = useState(false);
  const [sections, setSections] = useState<ReportSection[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<JobStatusResponse | null>(null);
  const pollingIntervalRef = useRef<number | null>(null);
  const navigate = useNavigate();

  // Poll for job status
  const pollJobStatus = async () => {
    try {
      const patientId = localStorage.getItem("patientId");

      if (!patientId) {
        return;
      }

      const sdk = new MkdSDK();
      const response = await sdk.getDSJobStatus(patientId);

      if (!response || response.error) {
        return;
      }

      setJobStatus(response.data);

      // If job is completed, stop polling and fetch report
      if (response.data.status === 3) {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }

        // Fetch the report using the new endpoint
        await fetchDSReport();
      }

      // Note: Status 2 means processing, so we continue polling
      // Only stop polling on completion (status 3) or if there's an actual error
    } catch (err) {
      console.error("Error polling job status:", err);
    }
  };

  // Fetch report data using new DS endpoint
  const fetchDSReport = async () => {
    try {
      const projectId = localStorage.getItem("projectId");
      const userId = localStorage.getItem("user");
      const patientId = localStorage.getItem("patientId");

      if (!projectId || !userId || !patientId) {
        throw new Error("Missing required parameters");
      }

      const sdk = new MkdSDK();
      const response = await sdk.fetchDSReport({
        project_id: projectId,
        user_id: userId,
        patient_id: patientId,
      });

      localStorage.setItem("reportId", response.data.report_id);

      if (response.error) {
        throw new Error(response.message || "Failed to fetch report data");
      }

      // After fetchDSReport succeeds, get the report sections
      // The report sections should now be available for this patient/project
      const sectionsResponse = await sdk.getReportSections(
        response.data.report_id
      );

      if (sectionsResponse.error) {
        throw new Error(
          sectionsResponse.message || "Failed to fetch report sections"
        );
      }

      if (
        !sectionsResponse.sections ||
        !Array.isArray(sectionsResponse.sections)
      ) {
        throw new Error("Invalid sections response format");
      }

      setSections(sectionsResponse.sections);
      setShowReport(true);
      setError(null);
      setIsLoading(false);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while fetching the report"
      );
      setSections(null);
      setIsLoading(false);
    }
  };

  const handleProceedReview = () => setShowReviewOrder(true);
  const handleCloseReviewOrder = () => setShowReviewOrder(false);

  const handleBeginReview = async (reorderedSections: ReviewSection[]) => {
    try {
      const reportId = localStorage.getItem("reportId");
      if (!reportId) {
        throw new Error("No report ID found");
      }

      // Convert ReviewSection to ReportSection format
      const reportSections = reorderedSections.map((section) => ({
        id: parseInt(section.id),
        title: section.title,
        order: section.order,
        created_at: new Date().toISOString(), // These fields will be ignored by the backend
        updated_at: new Date().toISOString(), // These fields will be ignored by the backend
        body: "", // This field will be ignored by the backend
        report_id: reportId,
      }));

      // Extract section IDs in their new order
      const sectionOrder = reportSections.map((section) =>
        section.id.toString()
      );

      // Call the update endpoint
      const sdk = new MkdSDK();
      const response = await sdk.updateReportSectionOrder({
        report_id: reportId,
        order: sectionOrder,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to update section order");
      }

      // Close the modal and navigate to the editor
      setShowReviewOrder(false);
      navigate("/member/document-section-editor");
    } catch (err) {
      console.error("Error updating section order:", err);
      // You might want to show an error message to the user here
    }
  };

  useEffect(() => {
    const initializePage = async () => {
      try {
        let reportId = localStorage.getItem("reportId");


        // If we have a reportId, fetch the report directly
        if (reportId !== "null" && reportId) {

          const sdk = new MkdSDK();
          const response = await sdk.getReportSections(reportId);
          if (response.error) {
            throw new Error(
              response.message || "Failed to fetch report sections"
            );
          }
          setSections(response.sections);
          setShowReport(true);
          setIsLoading(false);
          return;
        }

        // If no reportId, check if there's a patientId and start polling
        const patientId = localStorage.getItem("patientId");
        const projectId = localStorage.getItem("projectId");

        if (patientId) {
          // Start polling for job status using DS service
          await pollJobStatus();
          const interval = setInterval(pollJobStatus, 20000);
          pollingIntervalRef.current = interval;
          return;
        }
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while initializing the page"
        );
        setSections(null);
        setIsLoading(false);
      }
    };

    initializePage();

    // Cleanup polling when component unmounts
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, []);

  if (!showReport) return <ProcessingDocuments jobStatus={jobStatus} />;

  return (
    <>
      <AIGeneratedReport
        onProceedReview={handleProceedReview}
        sections={sections}
        isLoading={isLoading}
        error={error}
      />

      <ReviewOrderModal
        open={showReviewOrder}
        onClose={handleCloseReviewOrder}
        onBeginReview={handleBeginReview}
        sections={
          sections?.map((section) => ({
            id: section.id.toString(),
            title: section.title,
            order: section.order || 0,
          })) || []
        }
      />
    </>
  );
};

export default AIReportPage;
