import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{I as g}from"./index-ec6e151a.js";import{u as F,T as x,L as h}from"./index-95f0e460.js";import{r as j}from"./vendor-489b60f1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const S=()=>{const{projectConfig:{updateSettings:s,updateModels:l,updateRoutes:o,updateRoles:a},showToast:r,globalState:{settings:n,models:i,routes:m,roles:d}}=F(),C=j.useMemo(()=>({settings:n,models:i,routes:m,roles:d}),[n,i,m,d]),R=c=>{try{const e=JSON.parse(c);e.settings&&s(e.settings),e.models&&l(e.models),e.routes&&o(e.routes),e.roles&&a(e.roles),r("Configuration imported successfully",500,x.SUCCESS)}catch(e){console.error("Error importing configuration:",e),r("Failed to import configuration. Invalid file format.",5e3,x.ERROR)}};return{handleFileChange:c=>{var p;const e=(p=c.target.files)==null?void 0:p[0];if(e){const f=new FileReader;f.onload=N=>{var u;const y=(u=N.target)==null?void 0:u.result;R(y)},f.readAsText(e)}},configuration:C,handleDeleteConfig:()=>{s({}),l([]),o([]),a([])}}},G=({})=>{var r,n;const s=j.useRef(null),{handleFileChange:l,configuration:o,handleDeleteConfig:a}=S();return t.jsxs(t.Fragment,{children:[t.jsx("input",{type:"file",accept:".json",ref:s,onChange:l,className:"hidden mt-4 w-full"}),t.jsx("div",{className:"flex flex-col gap-4 items-center justify-center",children:(r=o==null?void 0:o.settings)!=null&&r.model_namespace?t.jsxs("div",{className:"flex flex-col gap-4 items-center justify-center",children:[t.jsx("div",{className:"text-2xl font-bold",children:(n=o.settings)==null?void 0:n.model_namespace}),t.jsx(h,{children:t.jsx(g,{className:"!px-5 !h-[3rem] !text-[1.5rem] !bg-red-500 !shadow-red-500",onClick:()=>{a()},children:"Delete Config"})})]}):t.jsx("div",{className:"flex flex-col gap-4 items-center justify-center",children:t.jsx(h,{children:t.jsx(g,{className:"!px-5 !h-[3rem] !text-[1.5rem]",onClick:()=>{var i;return(i=s.current)==null?void 0:i.click()},children:"Upload Config"})})})})]})};export{G as default};
