import React, { Suspense, memo } from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/DoctorHeader/DoctorHeader";
import { Spinner } from "@/assets/svgs";
import { LazyLoad } from "@/components/LazyLoad";
import { colors } from "@/utils/config";

interface DoctorWrapperProps {
  children: React.ReactNode;
}

const DoctorWrapper = ({ children }: DoctorWrapperProps) => {
  return (
    <LazyLoad>
      <div className="relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden">
        <DoctorHeader />
        <div className="grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden">
          {/* No TopHeader for doctor, matching MemberWrapper */}
          <Suspense
            fallback={
              <div className="flex h-full max-h-full min-h-full w-full items-center justify-center">
                <Spinner size={40} color={colors.primary} />
              </div>
            }
          >
            <div className="h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden">
              {children}
            </div>
          </Suspense>
        </div>
      </div>
    </LazyLoad>
  );
};

export default memo(DoctorWrapper);
