import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as p}from"./vendor-489b60f1.js";import{M as r}from"./AdminLoginPage-95257026.js";import{L as i}from"./index-95f0e460.js";import{M as b}from"./index-235b3e94.js";import"./yup-5d8330af.js";import"./react-hook-form-7e42b371.js";import"./yup-fe85ba88.js";import"./@hookform/resolvers-6b9dee20.js";import"./index-ec6e151a.js";import"./html2pdf.js-82514bbc.js";import"./index-c6183aa1.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const H=()=>{const[a,c]=p.useState({name:"",email:"",message:"",subscribe:!1,category:""}),[o,d]=p.useState({}),n=s=>{const{name:t,value:g,type:j}=s.target;if(j==="checkbox"){const l=s.target.checked;c(m=>({...m,[t]:l}))}else c(l=>({...l,[t]:g}));o[t]&&d(l=>{const m={...l};return delete m[t],m})},x=s=>{c(t=>({...t,subscribe:s.target.checked}))},h=()=>{const s={};return a.name||(s.name={message:"Name is required"}),a.email?/\S+@\S+\.\S+/.test(a.email)||(s.email={message:"Email is invalid"}):s.email={message:"Email is required"},a.message||(s.message={message:"Message is required"}),a.category||(s.category={message:"Please select a category"}),d(s),Object.keys(s).length===0},u=s=>{s.preventDefault(),h()&&console.log("Form submitted:",a)};return e.jsxs("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Contact Form Example"}),e.jsxs("form",{onSubmit:u,className:"space-y-6",children:[e.jsx(i,{children:e.jsx(r,{name:"name",type:"text",value:a.name,onChange:n,errors:o,required:!0,children:e.jsxs(r.Container,{children:[e.jsx(r.Label,{children:"Your Name"}),e.jsx(r.Field,{placeholder:"Enter your name"}),e.jsx(r.Error,{})]})})})," ",e.jsx(i,{children:e.jsx(r,{name:"email",type:"email",value:a.email,onChange:n,errors:o,required:!0,children:e.jsxs(r.Container,{children:[e.jsx(r.Label,{className:"text-blue-600",children:"Email Address"}),e.jsx(r.Field,{placeholder:"Enter your email",className:"border-blue-200 focus:border-blue-500"}),e.jsx(r.Error,{})]})})}),e.jsx(i,{children:e.jsx(r,{name:"message",type:"textarea",value:a.message,onChange:n,errors:o,required:!0,placeholder:"Type your message here...",children:e.jsxs(r.Container,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(r.Label,{children:"Your Message"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Max 500 characters"})]}),e.jsx(r.Field,{rows:"4",placeholder:"Type your message here..."}),e.jsx(r.Error,{})]})})}),e.jsx(i,{children:e.jsx(r,{name:"subscribe",type:"toggle",value:a.subscribe,onChange:x,children:e.jsxs(r.Container,{className:"flex items-center gap-3",children:[e.jsx(r.Field,{}),e.jsx(r.Label,{children:"Subscribe to newsletter"})]})})}),e.jsx(i,{children:e.jsx(r,{name:"category",type:"dropdown",value:a.category,onChange:n,errors:o,options:["Support","Feedback","Partnership","Other"],required:!0,children:e.jsxs(r.Container,{children:[e.jsx(r.Label,{children:"Category"}),e.jsx(r.Field,{placeholder:"Select a category"}),e.jsx(r.Error,{})]})})}),e.jsx("div",{className:"pt-4",children:e.jsx(i,{children:e.jsx(b,{type:"submit",className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Submit"})})})]})]})};export{H as MkdInputV2Example,H as default};
