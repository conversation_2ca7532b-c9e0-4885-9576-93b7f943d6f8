import{o as h,r as v}from"./@hookform/resolvers-6b9dee20.js";import{a as m}from"./react-hook-form-7e42b371.js";function g(d,s,i){return s===void 0&&(s={}),i===void 0&&(i={}),function(u,l,n){try{return Promise.resolve(function(o,a){try{var t=(s.context,Promise.resolve(d[i.mode==="sync"?"validateSync":"validate"](u,Object.assign({abortEarly:!1},s,{context:l}))).then(function(r){return n.shouldUseNativeValidation&&h({},n),{values:i.raw?u:r,errors:{}}}))}catch(r){return a(r)}return t&&t.then?t.then(void 0,a):t}(0,function(o){if(!o.inner)throw o;return{values:{},errors:v((a=o,t=!n.shouldUseNativeValidation&&n.criteriaMode==="all",(a.inner||[]).reduce(function(r,e){if(r[e.path]||(r[e.path]={message:e.message,type:e.type}),t){var c=r[e.path].types,p=c&&c[e.type];r[e.path]=m(e.path,t,r,e.type,p?[].concat(p,e.message):e.message)}return r},{})),n)};var a,t}))}catch(o){return Promise.reject(o)}}}export{g as o};
