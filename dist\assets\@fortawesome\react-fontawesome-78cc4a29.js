import{g as c}from"../vendor-489b60f1.js";import"./fontawesome-svg-core-254ba2e2.js";var p={exports:{}},i="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",y=i,m=y;function a(){}function n(){}n.resetWarningCache=a;var T=function(){function e(f,h,l,P,g,s){if(s!==m){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}e.isRequired=e;function r(){return e}var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,elementType:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:n,resetWarningCache:a};return t.PropTypes=t,t};p.exports=T();var u=p.exports;const _=c(u);export{_ as P,u as p};
