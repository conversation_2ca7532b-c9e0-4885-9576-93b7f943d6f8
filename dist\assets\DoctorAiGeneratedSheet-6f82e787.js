import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as i}from"./vendor-489b60f1.js";import{a as j,u as b}from"./index-95f0e460.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const y=({isExpanded:o})=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",className:`transform transition-transform ${o?"rotate-180":""}`,xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.29289 7.29289C5.68342 6.90237 6.31658 6.90237 6.70711 7.29289L10 10.5858L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L10.7071 12.7071C10.3166 13.0976 9.68342 13.0976 9.29289 12.7071L5.29289 8.70711C4.90237 8.31658 4.90237 7.68342 5.29289 7.29289Z",fill:"#1F2937"})}),H=()=>{const{sdk:o}=j(),{tokenExpireError:h}=b(),[p,c]=i.useState(!0),[m,x]=i.useState(null),[u,n]=i.useState([]),l=async()=>{var t,a;try{c(!0),x(null);const s=await o.getDoctorCheatsheet();if(s.error)throw new Error(s.message||"Failed to fetch cheatsheet");if(!s.data||!Array.isArray(s.data.sections))throw new Error("Invalid response format from server");const r=s.data.sections.map((d,f)=>({...d,isExpanded:f===0,subsections:Array.isArray(d.subsections)?d.subsections:[]}));n(r)}catch(s){const r=((a=(t=s==null?void 0:s.response)==null?void 0:t.data)==null?void 0:a.message)||s.message||"An error occurred";x(r),h(r),n([{title:"Background Information",isExpanded:!0,subsections:[{subtitle:"Error Loading Data",content:"Unable to load cheatsheet data. Please try again later."}]}])}finally{c(!1)}};i.useEffect(()=>{l()},[]);const g=t=>{n(a=>a.map((s,r)=>r===t?{...s,isExpanded:!s.isExpanded}:s))};return p?e.jsx("div",{className:"flex items-center h-screen w-full justify-center",children:e.jsx("div",{className:"w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):m?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8 px-4",children:e.jsx("div",{className:"max-w-[95%] mx-auto bg-white rounded-lg shadow-sm",children:e.jsxs("div",{className:"flex flex-col items-center justify-center h-64",children:[e.jsx("p",{className:"text-red-500 mb-4",children:m}),e.jsx("button",{onClick:l,className:"bg-violet-600 text-white px-4 py-2 rounded-lg hover:bg-violet-700 transition-colors",children:"Retry"})]})})}):e.jsx("div",{className:"min-h-screen bg-gray-50 py-8 px-4",children:e.jsxs("div",{className:"max-w-[95%] mx-auto bg-white rounded-lg shadow-sm",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-900 mb-1",children:"AI-Generated Cheatsheet"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Auto-generated guidelines from psychologist notes"})]}),e.jsx("div",{className:"flex items-center text-sm text-blue-600",children:e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"w-2 h-2 bg-blue-600 rounded-full"}),"Generated: ",new Date().toLocaleDateString()]})})]})}),e.jsx("div",{className:"divide-y divide-gray-200",children:u.map((t,a)=>e.jsxs("div",{className:"border-gray-200",children:[e.jsxs("button",{onClick:()=>g(a),className:"w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-50 transition-colors",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{className:"text-base font-medium text-gray-900",children:t.title})}),e.jsx(y,{isExpanded:t.isExpanded})]}),t.isExpanded&&e.jsx("div",{className:"px-6 pb-4",children:e.jsx("div",{className:"space-y-4",children:t.subsections.map((s,r)=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:s.subtitle}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:e.jsx("p",{className:"text-sm text-gray-900",children:s.content})})]},r))})})]},a))}),e.jsxs("div",{className:"p-6 border-t border-gray-200 flex justify-between",children:[e.jsxs("button",{className:"text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2",onClick:()=>window.history.back(),children:[e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M10 16L4 10L10 4",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M4 10H16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Back to Report"]}),e.jsxs("button",{onClick:l,className:"bg-violet-600 text-white px-4 py-2 rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2",children:["Refresh",e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M4 10H16M16 10L10 4M16 10L10 16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})]})]})]})})};export{H as default};
