import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as w,L as b}from"./vendor-489b60f1.js";import{u as v}from"./react-hook-form-7e42b371.js";import{o as N}from"./yup-fe85ba88.js";import{c as V,a as S}from"./yup-5d8330af.js";import{a as L,u as k}from"./index-95f0e460.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Q=()=>{const{sdk:p}=L(),{showToast:h,tokenExpireError:g}=k(),[t,r]=w.useState(!1),u=V({email:S().email().required()}).required(),{register:f,handleSubmit:j,setError:l,formState:{errors:a}}=v({resolver:N(u)}),y=async C=>{var o,m,n,c;try{r(!0);const s=await p.forgot(C.email);if(!s.error)h("Reset Code Sent");else if(s.validation){const d=Object.keys(s.validation);for(let i=0;i<d.length;i++){const x=d[i];l(x,{type:"manual",message:s.validation[x]})}}r(!1)}catch(s){r(!1),l("email",{type:"manual",message:((m=(o=s.response)==null?void 0:o.data)==null?void 0:m.message)??s.message}),g(((c=(n=s.response)==null?void 0:n.data)==null?void 0:c.message)??s.message)}};return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("header",{className:"text-center",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-800 flex items-center justify-center",children:e.jsxs("svg",{width:30,height:30,viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[e.jsx("g",{clipPath:"url(#clip0_1_28)",children:e.jsx("path",{d:"M10.7812 0C12.5918 0 14.0625 1.4707 14.0625 3.28125V26.7188C14.0625 28.5293 12.5918 30 10.7812 30C9.08789 30 7.69336 28.7168 7.51758 27.0645C7.21289 27.1465 6.89062 27.1875 6.5625 27.1875C4.49414 27.1875 2.8125 25.5059 2.8125 23.4375C2.8125 23.0039 2.88867 22.582 3.02344 22.1953C1.25391 21.5273 0 19.8164 0 17.8125C0 15.9434 1.0957 14.3262 2.68359 13.5762C2.17383 12.9375 1.875 12.1289 1.875 11.25C1.875 9.45117 3.14063 7.95117 4.82812 7.58203C4.73438 7.25977 4.6875 6.91406 4.6875 6.5625C4.6875 4.81055 5.89453 3.33398 7.51758 2.92383C7.69336 1.2832 9.08789 0 10.7812 0ZM19.2188 0C20.9121 0 22.3008 1.2832 22.4824 2.92383C24.1113 3.33398 25.3125 4.80469 25.3125 6.5625C25.3125 6.91406 25.2656 7.25977 25.1719 7.58203C26.8594 7.94531 28.125 9.45117 28.125 11.25C28.125 12.1289 27.8262 12.9375 27.3164 13.5762C28.9043 14.3262 30 15.9434 30 17.8125C30 19.8164 28.7461 21.5273 26.9766 22.1953C27.1113 22.582 27.1875 23.0039 27.1875 23.4375C27.1875 25.5059 25.5059 27.1875 23.4375 27.1875C23.1094 27.1875 22.7871 27.1465 22.4824 27.0645C22.3066 28.7168 20.9121 30 19.2188 30C17.4082 30 15.9375 28.5293 15.9375 26.7188V3.28125C15.9375 1.4707 17.4082 0 19.2188 0Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_28",children:e.jsx("path",{d:"M0 0H30V30H0V0Z",fill:"white"})})})]})})}),e.jsx("h2",{className:"mt-6 text-2xl font-semibold text-gray-800",children:"Forgot Password"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Enter your email to reset your password"})]}),e.jsx("form",{className:"mt-8 bg-white p-8 rounded-xl shadow-sm",onSubmit:j(y),children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),e.jsxs("div",{className:"mt-1",children:[e.jsxs("div",{className:"flex items-center gap-2 relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:e.jsx("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2 3.5C1.725 3.5 1.5 3.725 1.5 4V4.69063L6.89062 9.11563C7.5375 9.64688 8.46562 9.64688 9.1125 9.11563L14.5 4.69063V4C14.5 3.725 14.275 3.5 14 3.5H2ZM1.5 6.63125V12C1.5 12.275 1.725 12.5 2 12.5H14C14.275 12.5 14.5 12.275 14.5 12V6.63125L10.0625 10.275C8.8625 11.2594 7.13438 11.2594 5.9375 10.275L1.5 6.63125ZM0 4C0 2.89688 0.896875 2 2 2H14C15.1031 2 16 2.89688 16 4V12C16 13.1031 15.1031 14 14 14H2C0.896875 14 0 13.1031 0 12V4Z",fill:"#9CA3AF"})})}),e.jsx("input",{...f("email"),type:"email",required:!0,placeholder:"<EMAIL>",className:`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${a.email?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`})]}),a.email&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:a.email.message})]})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("button",{type:"submit",disabled:t,className:`flex-1 flex justify-center items-center py-2 px-4 border border-transparent rounded-lg bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${t?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-white",children:t?"Sending...":"Reset Password"})})}),e.jsx("div",{className:"text-center",children:e.jsx(b,{to:"/user/login",className:"text-sm text-gray-600 hover:text-gray-800",children:"Back to login"})})]})}),e.jsxs("div",{className:"text-center mt-4",children:[e.jsx("a",{href:"#",className:"text-sm text-gray-500 hover:text-gray-700",children:"Need help?"}),e.jsx("div",{className:"mt-4 border-t border-gray-200 pt-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Contact your administrator for support"})})]})]})})};export{Q as default};
