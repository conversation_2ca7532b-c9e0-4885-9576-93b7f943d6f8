import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, useEditor, Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import MkdSDK from "../../../utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import html2pdf from "html2pdf.js";
import { DEANNALOGO } from "../../../assets/images";

// Toolbar component (copied and adapted from ReviewChangesPage)
const Toolbar: React.FC<{ editor: Editor | null }> = ({ editor }) => {
  if (!editor) return null;
  return (
    <div className="w-full">
      <div className="flex items-center gap-2 border-b px-2 py-2 bg-white rounded-t-xl">
        <button
          type="button"
          aria-label="Bold"
          aria-pressed={editor.isActive("bold")}
          className={`px-2 py-1 rounded ${editor.isActive("bold") ? "font-bold text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled
        >
          <b>B</b>
        </button>
        <button
          type="button"
          aria-label="Italic"
          aria-pressed={editor.isActive("italic")}
          className={`px-2 py-1 rounded ${editor.isActive("italic") ? "italic text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled
        >
          <i>I</i>
        </button>
        <button
          type="button"
          aria-label="Underline"
          aria-pressed={editor.isActive("underline")}
          className={`px-2 py-1 rounded ${editor.isActive("underline") ? "underline text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleUnderline?.().run()}
          disabled
        >
          <u>U</u>
        </button>
        <button
          type="button"
          aria-label="Bulleted List"
          aria-pressed={editor.isActive("bulletList")}
          className={`px-2 py-1 rounded ${editor.isActive("bulletList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <circle cx="4" cy="5" r="1.5" fill="currentColor" />
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="9" r="1.5" fill="currentColor" />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="13" r="1.5" fill="currentColor" />
            <rect
              x="7"
              y="12"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Numbered List"
          aria-pressed={editor.isActive("orderedList")}
          className={`px-2 py-1 rounded ${editor.isActive("orderedList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <text x="2" y="7" fontSize="6" fill="currentColor">
              1.
            </text>
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <text x="2" y="13" fontSize="6" fill="currentColor">
              2.
            </text>
            <rect
              x="7"
              y="10"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <span className="mx-2 border-l h-6" />
        <button
          type="button"
          aria-label="Align Left"
          aria-pressed={editor.isActive({ textAlign: "left" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "left" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="3" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Center"
          aria-pressed={editor.isActive({ textAlign: "center" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "center" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="5" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Right"
          aria-pressed={editor.isActive({ textAlign: "right" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "right" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Justify"
          aria-pressed={editor.isActive({ textAlign: "justify" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "justify" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("justify").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="8"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

// Utility to extract client details from HTML string
function extractClientDetails(html: string) {
  if (typeof window === "undefined" || !html) return {};
  const parser = new window.DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  
  const getText = (label: string) => {
    // Find table row containing the label
    const rows = Array.from(doc.querySelectorAll("tr"));
    const targetRow = rows.find((row) => {
      const cells = row.querySelectorAll("td");
      if (cells.length >= 2) {
        const labelCell = cells[0];
        return labelCell.textContent
          ?.replace(/\s/g, "")
          .toLowerCase()
          .includes(label.replace(/\s/g, "").toLowerCase());
      }
      return false;
    });
    
    if (!targetRow) return "";
    
    // Get the value from the second cell
    const cells = targetRow.querySelectorAll("td");
    if (cells.length >= 2) {
      return cells[1].textContent?.trim() || "";
    }
    
    return "";
  };
  
  return {
    name: getText("Name"),
    dob: getText("D.O.B"),
    parents: getText("Parents"),
    address: getText("Address"),
    phone: getText("Phone"),
    school: getText("School"),
    schoolBoard: getText("School Board"),
    gradeProgram: getText("Grade/Program"),
    assessmentBy: getText("Assessment by"),
    assessmentDates: getText("Assessment Dates"),
    ageAtTesting: getText("Age at testing"),
  };
}

// Utility to convert image to base64
async function imageToBase64(imagePath: string): Promise<string> {
  try {
    const response = await fetch(imagePath);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error converting image to base64:', error);
    return '';
  }
}

// Utility to remove numbering from section titles
function removeTitleNumbering(title: string): string {
  // Remove patterns like "1. ", "2. ", "10. ", etc. from the beginning
  return title.replace(/^\d+\.\s*/, '').trim();
}

// Utility to build the report HTML template
async function buildReportHTML({
  sections,
}: {
  clientDetails: {
    name: string;
    dob: string;
    address: string;
    phone: string;
    assessmentBy: string;
    assessmentDates: string;
    ageAtTesting: string;
  };
  sections: { title: string; body: string }[];
}) {
  // First section (with sidebar)
  const firstSection = sections[0];
  const secondSection = sections[1];
  // Remaining sections (no sidebar, each on new page)
  const restSections = sections.slice(2);

  // Convert logo to base64
  const logoBase64 = await imageToBase64(DEANNALOGO);

  return `
  <div style="font-family: 'Times New Roman', Times, serif; font-size: 14px;">
          <style>
 
        h2 { font-size: 16px !important; font-weight: bold; margin: 10px 0 4px 0; text-decoration: underline; font-style: italic; }
        p { font-size: 14px !important; margin: 4px 0; }
        ul, ol { font-size: 14px !important; }
        li { font-size: 14px !important; }
        table { font-size: 14px !important; }
        td, th { font-size: 14px !important; }
        .psychologists-list { font-size: 10px !important; }
        .psychologists-list li { font-size: 10px !important; }
      </style>
    <div style="display: flex; border-bottom: 1px solid #ccc; padding: 20px 0px;">
      <div style="width: 120px;">
        <div style="width:100px; height:60px; display:flex; align-items:center; justify-content:center;">
          <img src="${logoBase64}" alt="Logo" style="max-width: 100%; max-height: 100%; object-fit: contain;" />
        </div>
      </div>
      <div style="flex:1; text-align:right; font-size: 14px;">
        <h1 style="color:#1ca3a3; margin:0; font-size:2em;">Gilmour Psychological Services®</h1>
        <div>421 Gilmour Street, Ottawa, ON  K2P 0R5</div>
        <div>Tel: ************ &nbsp; Fax: ************</div>
        <div><a href="http://www.ottawa-psychologists.com">www.ottawa-psychologists.com</a></div>
      </div>
    </div>
    <div style="display: flex;">
      <div style="width:180px; border-right:1px solid #ccc; padding:10px;">
         <p style="margin:0; padding:0;"><b>Psychologists</b></p>
          <ul class="psychologists-list" style="margin:0; padding:0; list-style:none;">
            <li style="margin:10px 0;">Dr. Iris Jackson &nbsp; Ext. 124</li>
            <li style="margin:10px 0;">Dr. Karen Davies &nbsp; Ext. 126</li>
            <li style="margin:10px 0;">Dr. Alex Weinberger &nbsp; Ext. 136</li>
            <li style="margin:10px 0;">Dr. Qadeer Ahmad &nbsp; Ext. 129</li>
            <li style="margin:10px 0;">Dr. Peter Judge &nbsp; Ext. 132</li>
            <li style="margin:10px 0;">Dr. Deanna Drahovzal &nbsp; Ext. 146</li>
            <li style="margin:10px 0;">Dr. Sarah Pantin &nbsp; Ext. 150</li>
            <li style="margin:10px 0;">Dr. Marc Zahradnik &nbsp; Ext. 142</li>
            <li style="margin:10px 0;">Dr. Caroline Ostiguy &nbsp; Ext. 140</li>
            <li style="margin:10px 0;">Dr. Jessica Henry &nbsp; Ext. 155</li>
            <li style="margin:10px 0;">Dr. Delyana Miller &nbsp; Ext. 143</li>
            <li style="margin:10px 0;">Dr. Angelina Chupetlovska &nbsp; Ext. 152</li>
            <li style="margin:10px 0;">Dr. Douglas Scoular &nbsp; Ext. 148</li>
            <li style="margin:10px 0;">Dr. Elisabeth Melsom &nbsp; Ext. 157</li>
            <li style="margin:10px 0;">Dr. Emma Dargie &nbsp; Ext. 175</li>
            <li style="margin:10px 0;">Dr. Karima Lacène &nbsp; Ext. 188</li>
            <li style="margin:10px 0;">Dr. Amanda Timmers &nbsp; Ext. 153</li>
            <li style="margin:10px 0;">Dr. Rana Pishva &nbsp; Ext. 160</li>
            <li style="margin:10px 0;">Dr. Emily Segal &nbsp; Ext. 164</li>
            <li style="margin:10px 0;">Dr. Emma Murray &nbsp; Ext. 144</li>
            <li style="margin:10px 0;">Dr. Stephenie Davies &nbsp; Ext. 192</li>
          </ul>
      </div>
      <div style="flex:1; padding:10px;">
        <div style="text-align:center; font-weight:bold; margin-top:0;">PSYCHOLOGICAL REPORT</div>
        <div style="text-align:center; font-weight:bold;">(Private and Confidential)</div>
         <div style="margin-bottom:10px;"> ${firstSection.body}</div>
         <div style="margin-bottom:10px;">${secondSection.body}</div>
      </div>
    </div>
        ${
      restSections.length > 0
        ? restSections
            .map(
              (section: { title: string; body: string }) => `<div style="page-break-before: always; padding:0px 30px;">
                  <div style="margin-bottom:0px;"><h1 style="font-size: 18px !important; font-weight: bold; margin: 10px 0 14px 0px !important; text-decoration: underline;"> <br/> ${removeTitleNumbering(section.title)}</h1> ${section.body}</div>
                </div>`
            )
            .join("")
        : ""
    }
    <div style="padding: 30px;">
      <div style="margin-bottom:20px;">
        <div style="border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; width: fit-content;">
          Deanna Drahovzal, Ph.D., C.Psych.
        </div>
        <div style="margin-bottom: 10px;">
            <strong>Printed:</strong> ${new Date().toLocaleDateString()}
        </div>
        <div>
          <strong>Copies to:</strong><br/>
          Clinical chart<br/>
          Dr. BW Wilson and Ms. AW Wilson via OWL secure messenger
        </div>
      </div>
    </div>
  </div>
  `;
}

const DoctorReportOverviewPage: React.FC = () => {
  const [sections, setSections] = useState<
    { id: number; title: string; body: string }[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isPdfLoading, setIsPdfLoading] = useState(false);
  const navigate = useNavigate();

  // Initialize TipTap editor
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
      }),
      Underline,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
    ],
    content: "",
    editable: false,
    editorProps: {
      attributes: {
        class:
          "prose prose-sm max-w-none min-h-[600px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap bg-white rounded-b-xl",
        spellCheck: "true",
        "aria-label": "Doctor Report Overview Document",
      },
    },
    parseOptions: { preserveWhitespace: "full" },
  });

  // Load sections from localStorage and update editor content
  useEffect(() => {
    const savedSections = localStorage.getItem("reportSections");
    if (savedSections) {
      try {
        const parsedSections = JSON.parse(savedSections);
        setSections(parsedSections);
        // Combine all sections into one document
        const combinedContent = parsedSections
          .map(
            (section: { title: string; body: string }) =>
              `<h1 style="font-size: 18px !important; font-weight: bold; margin: 10px 0 14px 0px !important; text-decoration: underline;"> <br/> ${removeTitleNumbering(section.title)}</h1> ${section.body}`
          )
          .join("");
        editor?.commands.setContent(combinedContent);
      } catch (err) {
        setError("Failed to load sections from storage");
        console.error("Error loading sections:", err);
      }
    } else {
      setError("No sections found in storage");
    }
    setIsLoading(false);
  }, [editor]);

  // Save Changes logic (matches DoctorSectionEditorPage)
  const handleSaveChanges = async () => {
    try {
      setIsSaving(true);
      const sdk = new MkdSDK();
      const reportId = localStorage.getItem("reportId");
      if (!reportId) {
        throw new Error("No report ID found");
      }
      for (const section of sections) {
        await sdk.updateReportSection({
          id: section.id.toString(),
          content: section.body,
        });
      }
      // Optionally show a success message
      // e.g. toast.success('Changes saved successfully');
    } catch (err) {
      setError(err instanceof Error ? err.message : "Error saving changes.");
      // Optionally show a toast or error UI
    } finally {
      setIsSaving(false);
    }
  };

  const handleExportPDF = async () => {
    if (!sections || sections.length === 0) return;
    setIsPdfLoading(true);
    try {
      // Extract client details from the first section's body
      const clientDetailsRaw = extractClientDetails(sections[0].body);
      const clientDetails = {
        name: clientDetailsRaw.name || "",
        dob: clientDetailsRaw.dob || "",
        address: clientDetailsRaw.address || "",
        phone: clientDetailsRaw.phone || "",
        assessmentBy: clientDetailsRaw.assessmentBy || "",
        assessmentDates: clientDetailsRaw.assessmentDates || "",
        ageAtTesting: clientDetailsRaw.ageAtTesting || "",
      };
      // Use only the next two sections for the report
      const reportSections = sections;
      const html = await buildReportHTML({ clientDetails, sections: reportSections });
      // Call backend to render PDF
      const sdk = new MkdSDK();
      const result = await sdk.renderPdf({
        html: html,
        include_index: false,
      });
      if (result && result.pdf) {
        // Download the PDF from base64
        const base64Pdf = result?.pdf;
        const byteCharacters = atob(base64Pdf);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${(localStorage.getItem("patientName") || "unknown") + "_report.pdf"}`;
        link.click();
      } else {
        alert("Failed to generate PDF");
      }
    } catch (error) {
      console.error("Error exporting PDF:", error);
      alert("Error exporting PDF");
    } finally {
      setIsPdfLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex max-w-[95%] mx-auto flex-col flex-shrink-0 justify-center items-start gap-6 border-0 border-gray-200 bg-black/0">
        {/* Header */}
        <div className="flex flex-shrink-0 items-center p-4 w-full border h-16 rounded-lg border-0 border-gray-200 bg-white justify-between">
          <h1 className="text-2xl text-gray-800 font-['Inter']">
            Complete Report Overview
          </h1>
          <div className="flex flex-shrink-0 justify-center items-center gap-2">
            <button
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-gray-800 text-white disabled:opacity-50"
              onClick={handleSaveChanges}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <svg
                    width="14"
                    height="16"
                    viewBox="0 0 14 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1.8125 3V13C1.8125 13.275 2.0375 13.5 2.3125 13.5H12.3125C12.5875 13.5 12.8125 13.275 12.8125 13V5.32812C12.8125 5.19688 12.7594 5.06875 12.6656 4.975L13.725 3.91563C14.1 4.29063 14.3094 4.8 14.3094 5.33125V13C14.3094 14.1031 13.4125 15 12.3094 15H2.3125C1.20938 15 0.3125 14.1031 0.3125 13V3C0.3125 1.89688 1.20938 1 2.3125 1H9.98438C10.5156 1 11.025 1.20938 11.4 1.58438L13.7281 3.9125L12.6687 4.97188L10.3375 2.64687C10.3281 2.6375 10.3219 2.63125 10.3125 2.62188V5.75C10.3125 6.16563 9.97812 6.5 9.5625 6.5H3.5625C3.14687 6.5 2.8125 6.16563 2.8125 5.75V2.5H2.3125C2.0375 2.5 1.8125 2.725 1.8125 3Z"
                      fill="white"
                    />
                  </svg>
                  Save Changes
                </>
              )}
            </button>
            <button
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-violet-600 text-white disabled:opacity-50"
              onClick={handleExportPDF}
              disabled={isPdfLoading}
            >
              {isPdfLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Generating PDF...
                </>
              ) : (
                <>
                  <svg
                    width="18"
                    height="16"
                    viewBox="0 0 18 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0.96875 2C0.96875 0.896875 1.86563 0 2.96875 0H7.96875V4C7.96875 4.55312 8.41563 5 8.96875 5H12.9688V9H7.71875C7.30312 9 6.96875 9.33438 6.96875 9.75C6.96875 10.1656 7.30312 10.5 7.71875 10.5H12.9688V14C12.9688 15.1031 12.0719 16 10.9688 16H2.96875C1.86563 16 0.96875 15.1031 0.96875 14V2Z"
                      fill="white"
                    />
                  </svg>
                  Export
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.38201 11.1181C6.7238 11.4599 7.27888 11.4599 7.62068 11.1181L12.8707 5.86807C13.2125 5.52627 13.2125 4.97119 12.8707 4.62939C12.5289 4.2876 11.9738 4.2876 11.632 4.62939L6.99998 9.26143L2.36794 4.63213C2.02615 4.29033 1.47107 4.29033 1.12927 4.63213C0.787476 4.97393 0.787476 5.529 1.12927 5.8708L6.37927 11.1208L6.38201 11.1181Z"
                      fill="white"
                    />
                  </svg>
                </>
              )}
            </button>
          </div>
        </div>
        {/* Main Content Area */}
        <div className="w-full h-full p-4">
          <div className=" mx-auto bg-white rounded-xl shadow">
            {isLoading ? (
              <div className="flex items-center justify-center min-h-[600px]">
                Loading...
              </div>
            ) : error ? (
              <div className="flex items-center justify-center min-h-[600px] text-red-600">
                {error}
              </div>
            ) : (
              <>
                <Toolbar editor={editor} />
                <EditorContent editor={editor} />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorReportOverviewPage;
