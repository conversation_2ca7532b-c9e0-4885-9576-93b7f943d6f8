import{j as s}from"./@react-google-maps/api-5b2d83cc.js";import{R as N,r as g}from"./vendor-489b60f1.js";import{u as K}from"./react-hook-form-7e42b371.js";import{o as G}from"./yup-fe85ba88.js";import{c as J,a as c}from"./yup-5d8330af.js";import{I as Q}from"./index-ec6e151a.js";import{a as W,h as X,u as Y,L as Z}from"./index-95f0e460.js";import{M as x}from"./index-e9605eb4.js";import{a as V}from"./index-32ecee74.js";import{C as F,T as ee,P as oe}from"./index-fe4acb22.js";import{M as ae}from"./index-235b3e94.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Ee=()=>{var E;const{sdk:b}=W(),[r,I]=N.useState({}),[j,O]=g.useState(""),[y,w]=g.useState(!1),[m,_]=g.useState({email:"",first_name:"",last_name:"",phone:"",photo:""}),[P,L]=g.useState({showModal:!1,modal:""});X();const{authDispatch:C,globalDispatch:T,tokenExpireError:S,showToast:U}=Y(),q=J({email:c().email().required(),first_name:c().nullable(),last_name:c().nullable(),phone:c().nullable(),photo:c().nullable()}).required(),{register:f,handleSubmit:A,setError:R,setValue:p,watch:B,formState:{errors:h}}=K({resolver:G(q)}),{photo:$}=B(),z=(a,t,n=!1)=>{var l,i;let o=r;n?o[a]?o[a]=[...o[a],{file:t.files[0],tempFile:{url:URL.createObjectURL(t.files[0]),name:((l=t.files[0])==null?void 0:l.name)??"",type:((i=t.files[0])==null?void 0:i.type)??""}}]:o[a]=[{file:t.files[0],tempFile:{url:URL.createObjectURL(t.files[0]),name:t.files[0].name,type:t.files[0].type}}]:o[a]={file:t.files[0],name:t.files[0].name,type:t.files[0].type,tempURL:URL.createObjectURL(t.files[0])},I({...o})};async function v(){var a,t,n,o,l,i,d,D;try{const e=await b.getProfile();_(()=>{var k,M;return{...e==null?void 0:e.model,role:((k=e==null?void 0:e.model)==null?void 0:k.role)??((M=e==null?void 0:e.model)==null?void 0:M.role_id)}}),p("email",(a=e==null?void 0:e.model)==null?void 0:a.email),p("first_name",(t=e==null?void 0:e.model)==null?void 0:t.first_name),p("last_name",(n=e==null?void 0:e.model)==null?void 0:n.last_name),p("phone",(o=e==null?void 0:e.model)==null?void 0:o.phone),p("photo",(l=e==null?void 0:e.model)==null?void 0:l.photo),O((i=e==null?void 0:e.model)==null?void 0:i.photo),C({type:"UPDATE_PROFILE",payload:{...e==null?void 0:e.model,role:((d=e==null?void 0:e.model)==null?void 0:d.role)??((D=e==null?void 0:e.model)==null?void 0:D.role_id)}})}catch(e){console.log("Error",e),S(e.response.data.message?e.response.data.message:e.message)}}const H=async a=>{var t,n;_(a);try{if(w(!0),r&&r.photo&&((t=r.photo)!=null&&t.file)){let l=new FormData;l.append("file",(n=r.photo)==null?void 0:n.file);let i=await b.uploadImage(l);a.photo=i.url,U("Profile Photo Updated",1e3)}const o=await b.updateProfile({first_name:a.first_name||(m==null?void 0:m.first_name),last_name:a.last_name||(m==null?void 0:m.last_name),phone:a.phone||(m==null?void 0:m.phone),photo:a.photo||j});if(!o.error)U("Profile Updated",4e3),u();else{if(o.validation){const l=Object.keys(o.validation);for(let i=0;i<l.length;i++){const d=l[i];R(d,{type:"manual",message:o.validation[d]})}}u()}await v(),w(!1)}catch(o){w(!1),console.log("Error",o),R("email",{type:"manual",message:o.response.data.message?o.response.data.message:o.message}),S(o.response.data.message?o.response.data.message:o.message)}};N.useEffect(()=>{T({type:"SETPATH",payload:{path:"profile"}}),v()},[]);const u=()=>{L(a=>({...a,modal:"",showModal:!1}))};return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mx-auto flex h-full max-h-full min-h-full flex-col items-center justify-start gap-5 overflow-auto rounded bg-soft-200 !font-inter leading-snug tracking-wide shadow-md md:p-5",children:s.jsxs("form",{onSubmit:A(H),className:"!px-2 !w-full space-y-5 ",children:[s.jsxs(F,{children:[s.jsx(ee,{className:"!border-0 !p-0 !shadow-none ",children:"Doctor Details"}),s.jsx("div",{children:s.jsx(oe,{image:((E=r==null?void 0:r.photo)==null?void 0:E.tempURL)||$||j,title:"Profile Image",name:"photo",onUpload:z})}),s.jsxs("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-2",children:[s.jsx(x,{label:"First Name",name:"first_name",register:f,errors:h}),s.jsx(x,{label:"Last Name",name:"last_name",register:f,errors:h})]}),s.jsxs("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-2",children:[s.jsx(x,{label:"Phone",name:"phone",register:f,errors:h}),s.jsx(x,{label:"Email",name:"email",register:f,errors:h})]}),s.jsx(Z,{children:s.jsx(ae,{className:"!shadow w-fit !border-0 !bg-white font-[700] !text-black",onClick:()=>{L(a=>({...a,modal:"password",showModal:!0}))},children:"Change Password"})})]}),s.jsx(F,{className:"!bg-transparent !shadow-none !p-0",children:s.jsx(Q,{type:"submit",loading:y,disabled:y,className:"w-full rounded px-4 py-2 font-bold bg-[black] !shadow text-white md:w-[auto]",children:"Save Changes"})})]})}),s.jsx(V,{isOpen:P.showModal&&["password"].includes(P.modal),onClose:u})]})};export{Ee as default};
