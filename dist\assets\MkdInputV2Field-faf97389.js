import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{r as I}from"./vendor-489b60f1.js";import{_ as O}from"./react-toggle-88721710.js";import{S as V,C as H,c as R,L as W}from"./index-95f0e460.js";import{M as A}from"./index-52d51cfb.js";import{u as B}from"./MkdInputV2Context-f6333041.js";import"./@uppy/dashboard-6cf0145e.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";function G(r){const f=new V,p=r.split("_"),c=p.includes("date"),m=p.includes("time");return c&&m?"Pick date & time":c?"Pick date":m?"Select Time":f.Capitalize(r,{separator:"space"})}const J=({className:r})=>t.jsx("div",{className:r,children:"Loading..."}),fe=({className:r="",cols:f="30",rows:p="50",checked:c,step:m,min:y,placeholder:L,...P})=>{var k,v,C,M,S,N,_;const{id:q,type:n,name:o,value:g,onChange:h,register:j,errors:e,disabled:d,required:E,placeholder:T,options:u,mapping:b,customField:z}=B(),[$,F]=I.useState(null),[i,D]=I.useState({modal:null,showModal:!1}),x=(l,a)=>{D(w=>({...w,modal:l,showModal:a}))},s={id:q,name:o,disabled:d,placeholder:L||T,...g!==null?{value:g}:{},...j?j(o,{...E&&z?{required:!0}:null}):{onChange:h},className:`focus:shadow-outline font-inter w-full appearance-none rounded border px-3 py-2 leading-tight text-black shadow focus:outline-none ${r} ${o&&e&&(e!=null&&e[o])&&((k=e==null?void 0:e[o])!=null&&k.message)?"!border-red-500":"border-gray-200"} ${d?"appearance-none bg-gray-200":""}`};return P.loading?t.jsx(J,{count:1,counts:[2],className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-[.625rem] !bg-[#ebebeb] !p-0"}):n==="textarea"?t.jsx("textarea",{...s,cols:f,rows:p}):n==="radio"||n==="checkbox"||n==="color"?t.jsx("div",{className:"flex h-[1.875rem] items-center gap-2 pb-1 pt-3",children:t.jsx("input",{...s,type:n,checked:c,className:`focus:shadow-outline font-inter !h-4 !w-4 cursor-pointer appearance-none rounded border leading-tight text-primary shadow focus:outline-none focus:ring-0 ${r} ${o&&e&&(e!=null&&e[o])&&((v=e==null?void 0:e[o])!=null&&v.message)?"!border-red-500":"border-gray-200"} ${n==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""} ${d?"appearance-none bg-gray-200":""}`,autoComplete:"new-password","aria-autocomplete":"none"})}):n==="toggle"?t.jsx(O,{className:`toggle_class ${r}`,disabled:d,icons:!1,onChange:h,checked:[!0,!1].includes(g)?g:void 0}):n==="dropdown"||n==="select"?u!=null&&u.length?t.jsxs("select",{...s,className:`focus:shadow-outline font-inter h-[3rem] w-full appearance-none truncate rounded-[.625rem] border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0 ${r} ${o&&e&&(e!=null&&e[o])&&((C=e==null?void 0:e[o])!=null&&C.message)?"!border-red-500":"border-gray-200"} ${d?"appearance-none bg-gray-200":""}`,children:[t.jsx("option",{}),u==null?void 0:u.map((l,a)=>t.jsx("option",{value:String(l),children:String(l)},a+1))]}):t.jsxs("div",{className:"text-red-500 p-2 border border-red-300 rounded",children:["Error: options prop is required for ",n," type. Please provide an array of options, e.g. ",'["option1", "option2"]']}):n==="mapping"?b?t.jsxs("select",{...s,className:`focus:shadow-outline font-inter h-[3rem] w-full truncate rounded-[.625rem] border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0 ${r} ${o&&e&&(e!=null&&e[o])&&((M=e==null?void 0:e[o])!=null&&M.message)?"!border-red-500":"border-gray-200"} ${d?"appearance-none bg-gray-200":""}`,children:[t.jsx("option",{}),b&&Object.entries(b).map(([l,a],w)=>t.jsx("option",{value:l,children:a==null?void 0:a.toString()},w+1))]}):t.jsxs("div",{className:"text-red-500 p-2 border border-red-300 rounded",children:["Error: mapping prop is required for mapping type. Please provide a mapping object, e.g. ","{key:value}"]}):n==="number"||n==="decimal"?t.jsx("input",{...s,type:n,step:m||"0.01",min:y||"0.00",onInput:l=>{let a=l.target.value;a.startsWith(".")&&(a=`0${a}`),/^\d+(\.\d{0,2})?$/.test(a)||(a=a.slice(0,-1)),l.target.value=a},className:`focus:shadow-outline font-inter h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0 ${r} ${o&&e&&(e!=null&&e[o])&&((S=e==null?void 0:e[o])!=null&&S.message)?"!border-red-500":"border-gray-200"} ${d?"appearance-none bg-gray-200":""}`,autoComplete:"new-password","aria-autocomplete":"none"}):n==="custom_date"?t.jsxs(t.Fragment,{children:[t.jsxs("div",{onClick:()=>{d||x("custom_date",!0)},className:"relative cursor-pointer",children:[t.jsx("input",{...s,type:n,disabled:!0,className:`focus:shadow-outline bg-brown-main-bg h-[3rem] w-full appearance-none truncate rounded-sm border-[.125rem] border-[#1f1d1a] text-center text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0 ${r} ${o&&e&&(e!=null&&e[o])&&((N=e==null?void 0:e[o])!=null&&N.message)?"!border-red-500":"border-gray-200"}`,autoComplete:"new-password","aria-autocomplete":"none"}),t.jsx(H,{className:"absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer"})]}),i!=null&&i.showModal?t.jsx(R,{modalHeader:!0,title:i.modal?G(i==null?void 0:i.modal):"",isOpen:i.showModal,modalCloseClick:()=>x(null,!1),classes:{modalDialog:"!px-0 !rounded-[.125rem] h-fit min-h-fit max-h-fit !w-full !max-w-full !min-w-full ",modalContent:"!z-10 !mt-0 overflow-hidden !pt-0",modal:"h-full"},children:i.showModal&&["custom_date"].includes(i.modal)?t.jsx(W,{children:t.jsx(A,{selectedDay:$,setSelectedDay:F,onSave:()=>{h({target:{value:$}}),x(null,!1)}})}):null}):null]}):t.jsx("input",{...s,type:n,className:`focus:shadow-outline font-inter h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0 ${r} ${o&&e&&(e!=null&&e[o])&&((_=e==null?void 0:e[o])!=null&&_.message)?"!border-red-500":"border-gray-200"} ${d?"appearance-none bg-gray-200":""}`,autoComplete:"new-password","aria-autocomplete":"none"})};export{fe as default};
