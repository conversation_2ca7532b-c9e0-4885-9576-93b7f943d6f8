import React, { memo, useEffect, lazy } from "react";
import { Navigate } from "react-router";
import metadataJ<PERSON><PERSON> from "@/utils/metadata.json";
import { StringCaser } from "@/utils/utils";
import { useContexts } from "@/hooks/useContexts";

interface DoctorRouteProps {
  path: string;
  children: React.ReactNode;
}

const DoctorRoute: React.FC<DoctorRouteProps> = ({ path, children }) => {
  const { authState } = useContexts();
  const stringCaser = new StringCaser();

  const { isAuthenticated } = authState;

  useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = metadata?.title
        ? stringCaser.Capitalize(metadata?.title, {
            separator: " ",
          })
        : "";
    } else {
      document.title = "";
    }
  }, [path]);

  return (
    <>
      {isAuthenticated ? (
        <>{children}</>
      ) : (
        <Navigate to="/doctor/login" replace />
      )}
    </>
  );
};

export default memo(Doctor<PERSON><PERSON><PERSON>);
