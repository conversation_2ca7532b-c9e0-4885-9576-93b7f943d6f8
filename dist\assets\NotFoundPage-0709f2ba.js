import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as o,R as t}from"./vendor-489b60f1.js";import{a}from"./html2pdf.js-82514bbc.js";const i=o.lazy(()=>a(()=>import("./Loader-5e541db5.js"),["assets/Loader-5e541db5.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js"])),c=()=>{const[r,s]=t.useState(!0);return t.useEffect(()=>{setTimeout(()=>{s(!1)},4e3)},[]),e.jsx(e.Fragment,{children:r?e.jsx(i,{}):e.jsx("div",{className:"flex justify-center items-center w-full h-screen text-7xl text-gray-700",children:"Not Found"})})};export{c as default};
