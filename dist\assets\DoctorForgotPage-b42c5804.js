import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as v,L as b}from"./vendor-489b60f1.js";import{u as N}from"./react-hook-form-7e42b371.js";import{o as w}from"./yup-fe85ba88.js";import{c as S,a as k}from"./yup-5d8330af.js";import{a as E,u as P}from"./index-95f0e460.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const U=()=>{const{sdk:p}=E(),{showToast:u,tokenExpireError:h}=P(),[s,r]=v.useState(!1),f=S({email:k().email().required()}).required(),{register:g,handleSubmit:j,setError:i,formState:{errors:o}}=N({resolver:w(f)}),y=async C=>{var l,m,n,c;try{r(!0);const t=await p.forgot(C.email);if(!t.error)u("Reset Code Sent");else if(t.validation){const d=Object.keys(t.validation);for(let a=0;a<d.length;a++){const x=d[a];i(x,{type:"manual",message:t.validation[x]})}}r(!1)}catch(t){r(!1),i("email",{type:"manual",message:((m=(l=t.response)==null?void 0:l.data)==null?void 0:m.message)??t.message}),h(((c=(n=t.response)==null?void 0:n.data)==null?void 0:c.message)??t.message)}};return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-r from-violet-50 to-blue-50 px-4 py-12 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("header",{className:"text-center",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"w-16 h-16 rounded-full bg-violet-600 flex items-center justify-center",children:e.jsxs("svg",{width:30,height:30,viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{clipPath:"url(#clip0_1_28)",children:e.jsx("path",{d:"M10.7812 0C12.5918 0 14.0625 1.4707 14.0625 3.28125V26.7188C14.0625 28.5293 12.5918 30 10.7812 30C9.08789 30 7.69336 28.7168 7.51758 27.0645C7.21289 27.1465 6.89062 27.1875 6.5625 27.1875C4.49414 27.1875 2.8125 25.5059 2.8125 23.4375C2.8125 23.0039 2.88867 22.582 3.02344 22.1953C1.25391 21.5273 0 19.8164 0 17.8125C0 15.9434 1.0957 14.3262 2.68359 13.5762C2.17383 12.9375 1.875 12.1289 1.875 11.25C1.875 9.45117 3.14063 7.95117 4.82812 7.58203C4.73438 7.25977 4.6875 6.91406 4.6875 6.5625C4.6875 4.81055 5.89453 3.33398 7.51758 2.92383C7.69336 1.2832 9.08789 0 10.7812 0ZM19.2188 0C20.9121 0 22.3008 1.2832 22.4824 2.92383C24.1113 3.33398 25.3125 4.80469 25.3125 6.5625C25.3125 6.91406 25.2656 7.25977 25.1719 7.58203C26.8594 7.94531 28.125 9.45117 28.125 11.25C28.125 12.1289 27.8262 12.9375 27.3164 13.5762C28.9043 14.3262 30 15.9434 30 17.8125C30 19.8164 28.7461 21.5273 26.9766 22.1953C27.1113 22.582 27.1875 23.0039 27.1875 23.4375C27.1875 25.5059 25.5059 27.1875 23.4375 27.1875C23.1094 27.1875 22.7871 27.1465 22.4824 27.0645C22.3066 28.7168 20.9121 30 19.2188 30C17.4082 30 15.9375 28.5293 15.9375 26.7188V3.28125C15.9375 1.4707 17.4082 0 19.2188 0Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_28",children:e.jsx("path",{d:"M0 0H30V30H0V0Z",fill:"white"})})})]})})}),e.jsx("h2",{className:"mt-6 text-2xl font-semibold text-gray-800",children:"Forgot Password"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Enter your email to reset your password"})]}),e.jsx("form",{className:"mt-8 bg-white p-8 rounded-xl shadow-sm",onSubmit:j(y),children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2"}),e.jsx("input",{...g("email"),type:"email",required:!0,placeholder:"<EMAIL>",className:` py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${o.email?"border-red-500 focus:ring-red-500":"focus:ring-violet-500"}`}),o.email&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:o.email.message})]}),e.jsx("button",{type:"submit",disabled:s,className:`flex-1 flex justify-center items-center py-2 px-4 border border-transparent rounded-lg bg-violet-600 hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 ${s?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-white",children:s?"Sending...":"Reset Password"})}),e.jsx("div",{className:"text-center",children:e.jsx(b,{to:"/doctor/login",className:"text-sm text-violet-700 hover:text-violet-900",children:"Back to login"})})]})}),e.jsxs("div",{className:"text-center mt-4",children:[e.jsx("a",{href:"#",className:"text-sm text-gray-500 hover:text-gray-700",children:"Need help?"}),e.jsx("div",{className:"mt-4 border-t border-gray-200 pt-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Contact your administrator for support"})})]})]})})};export{U as default};
