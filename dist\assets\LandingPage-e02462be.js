import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r,L as l}from"./vendor-489b60f1.js";import{M as d}from"./index-95f0e460.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";function x({}){const[s,t]=r.useState(!1),a=()=>{t(!s)};return e.jsx("div",{className:"fixed top-0 z-10 mx-auto w-full bg-[rgb(255,255,255,0.6)] px-0 py-3 backdrop-blur-sm md:px-8",children:e.jsx("div",{className:"m-0 w-full",children:e.jsx("div",{className:"",children:e.jsxs("div",{className:"mx-auto px-5 md:container md:px-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs(l,{to:"/",className:"h-14 min-h-14 max-h-14 flex gap-5 items-center ",children:[e.jsx("img",{className:"h-[70%] object-contain ",src:d}),e.jsx("h4",{className:"cursor-pointer font-bold",children:"MTP - Builder"})]})}),e.jsxs("div",{className:"mx-auto hidden flex-1 sm:items-center md:flex",children:[e.jsx("a",{href:"#",className:"mr-4 text-sm  text-gray-800 hover:text-indigo-600",children:"About"}),e.jsx("a",{href:"#",className:"mr-4 text-sm  text-gray-800 hover:text-indigo-600",children:"How it works"}),e.jsx("a",{href:"#",className:"mr-4 text-sm  text-gray-800 hover:text-indigo-600",children:"Features"}),e.jsx("a",{href:"#",className:"mr-4 text-sm  text-gray-800 hover:text-indigo-600",children:"FAQs"}),e.jsx("a",{href:"#",className:"text-sm  text-gray-800 hover:text-indigo-600",children:"Pricing"})]}),e.jsxs("div",{className:"ml-auto flex justify-end gap-2 md:hidden",children:[e.jsx("button",{className:"rounded-[6px] bg-indigo-600 px-[14px] py-[7px] text-xs text-white shadow-sm",children:"Start FREE Trial 🚀"}),e.jsx("button",{onClick:a,className:"rounded-[6px] bg-gray-100 px-[10px] py-[10px] shadow-sm",children:e.jsx("svg",{width:"16",height:"12",viewBox:"0 0 16 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1.33325 1H14.6666M1.33325 6H14.6666M1.33325 11H14.6666",stroke:"#A8A8A8",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"hidden flex-1 justify-center gap-3 md:flex",children:[e.jsx("button",{className:"rounded-[6px] bg-indigo-600 px-[14px] py-[7px] text-xs text-white shadow-sm",children:"Start FREE Trial 🚀"}),e.jsx(l,{to:"/admin/login",className:"flex items-center gap-2 rounded-[6px] border border-gray-300 bg-transparent px-[14px] py-[7px] text-xs text-gray-700 shadow-sm",children:e.jsx("span",{children:"Sign in"})})]})]})," ",s&&e.jsxs("div",{className:"mt mx-auto mt-4 flex flex-1 flex-col gap-4 sm:items-center md:hidden",children:[e.jsx("a",{href:"#",className:"mr-4 text-base  text-gray-800 hover:text-indigo-600",children:"About"}),e.jsx("a",{href:"#",className:"mr-4 text-base  text-gray-800 hover:text-indigo-600",children:"How it works"}),e.jsx("a",{href:"#",className:"mr-4 text-base  text-gray-800 hover:text-indigo-600",children:"Features"}),e.jsx("a",{href:"#",className:"mr-4 text-base  text-gray-800 hover:text-indigo-600",children:"FAQs"}),e.jsx("a",{href:"#",className:"text-base  text-gray-800 hover:text-indigo-600",children:"Pricing"})]})]})})})})}function c(){return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"my-[6rem] md:px-10",children:[e.jsxs("div",{className:"my-8 text-center",children:[e.jsx("p",{className:"text-indigo-600",children:"How it works"}),e.jsx("h1",{className:"text-3xl text-[#525252] md:text-5xl",children:"One platform for all your dev needs"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"flex flex-col gap-3 rounded-[8px] bg-gray-100 p-5",children:[e.jsx("div",{children:e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.6667 0L20.5759 3.33844e-06C20.9124 -3.65562e-05 21.2401 -7.54073e-05 21.5176 0.0226006C21.8224 0.0475029 22.1821 0.10626 22.544 0.290656C23.0457 0.546318 23.4537 0.954267 23.7094 1.45603C23.8938 1.81793 23.9525 2.17758 23.9774 2.48237C24.0001 2.75985 24.0001 3.08764 24 3.42408V20.576C24.0001 20.9125 24.0001 21.2402 23.9774 21.5176C23.9525 21.8224 23.8938 22.1821 23.7094 22.544C23.4537 23.0457 23.0457 23.4537 22.544 23.7094C22.1821 23.8938 21.8224 23.9525 21.5176 23.9774C21.2402 24.0001 20.9124 24.0001 20.5759 24H3.42396C3.08753 24.0001 2.75985 24.0001 2.48237 23.9774C2.17758 23.9525 1.81793 23.8938 1.45603 23.7094C0.954267 23.4537 0.546318 23.0457 0.290656 22.544C0.10626 22.1821 0.0475029 21.8224 0.0226006 21.5176C-7.54098e-05 21.2401 -3.6554e-05 20.9124 3.34058e-06 20.5759L0 10.6667H8V24H10.6667V10.6667H24V8H10.6667V0Z",fill:"#2563EB"}),e.jsx("path",{d:"M0 8H8V0L3.42414 3.34059e-06C3.08764 -3.6554e-05 2.75991 -7.54098e-05 2.48237 0.0226006C2.17758 0.0475029 1.81793 0.10626 1.45603 0.290656C0.954267 0.546318 0.546318 0.954267 0.290656 1.45603C0.10626 1.81793 0.0475029 2.17758 0.0226006 2.48237C-7.54073e-05 2.75991 -3.65562e-05 3.08762 3.33844e-06 3.42413L0 8Z",fill:"#2563EB"})]})}),e.jsx("h5",{children:"Wireframes"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]}),e.jsxs("div",{className:"flex flex-col gap-3 rounded-[8px]  bg-gray-100 p-5 md:col-span-2",children:[e.jsx("div",{children:e.jsxs("svg",{width:"28",height:"22",viewBox:"0 0 28 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M5.99996 4.33334C5.26358 4.33334 4.66663 4.93029 4.66663 5.66667C4.66663 6.40305 5.26358 7 5.99996 7C6.73634 7 7.33329 6.40305 7.33329 5.66667C7.33329 4.93029 6.73634 4.33334 5.99996 4.33334Z",fill:"#F59E0B"}),e.jsx("path",{d:"M8.66663 5.66667C8.66663 4.93029 9.26358 4.33334 9.99996 4.33334C10.7363 4.33334 11.3333 4.93029 11.3333 5.66667C11.3333 6.40305 10.7363 7 9.99996 7C9.26358 7 8.66663 6.40305 8.66663 5.66667Z",fill:"#F59E0B"}),e.jsx("path",{d:"M14 4.33334C13.2636 4.33334 12.6666 4.93029 12.6666 5.66667C12.6666 6.40305 13.2636 7 14 7C14.7363 7 15.3333 6.40305 15.3333 5.66667C15.3333 4.93029 14.7363 4.33334 14 4.33334Z",fill:"#F59E0B"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.666626 3C0.666626 1.52724 1.86053 0.333336 3.33329 0.333336H24.6666C26.1394 0.333336 27.3333 1.52724 27.3333 3V19C27.3333 20.4728 26.1394 21.6667 24.6666 21.6667H3.33329C1.86053 21.6667 0.666626 20.4728 0.666626 19V3ZM3.33329 3L24.6666 3V8.33334H3.33329V3Z",fill:"#F59E0B"})]})}),e.jsx("h5",{children:"Working prototypes"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]}),e.jsxs("div",{className:"flex flex-col gap-3 rounded-[8px] bg-gray-100 p-5",children:[e.jsx("div",{children:e.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M19.6605 0.333328C20.2128 0.333328 20.6605 0.781043 20.6605 1.33333V3.33333H24.9938C26.2825 3.33333 27.3271 4.378 27.3271 5.66666V22.3333C27.3271 23.622 26.2825 24.6667 24.9938 24.6667H20.6605V26.6667C20.6605 27.2189 20.2128 27.6667 19.6605 27.6667C19.1082 27.6667 18.6605 27.2189 18.6605 26.6667V1.33333C18.6605 0.781043 19.1082 0.333328 19.6605 0.333328Z",fill:"#10B981"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.666626 5.66666C0.666626 4.378 1.7113 3.33333 2.99996 3.33333H16.6666V24.6667H2.99996C1.71129 24.6667 0.666626 23.622 0.666626 22.3333V5.66666ZM8.70707 9.95955C8.31654 9.56903 7.68338 9.56903 7.29285 9.95955C6.90233 10.3501 6.90233 10.9832 7.29285 11.3738L9.91908 14L7.29285 16.6262C6.90233 17.0167 6.90233 17.6499 7.29285 18.0404C7.68338 18.431 8.31654 18.431 8.70707 18.0404L12.0404 14.7071C12.4309 14.3166 12.4309 13.6834 12.0404 13.2929L8.70707 9.95955Z",fill:"#10B981"})]})}),e.jsx("h5",{children:"Code exports"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]}),e.jsxs("div",{className:"flex flex-col gap-3 rounded-[8px] bg-gray-100 p-5",children:[e.jsx("div",{children:e.jsxs("svg",{width:"22",height:"28",viewBox:"0 0 22 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.8001 2.0994C10.9292 2.03489 11.0338 1.93027 11.0983 1.80125L11.736 0.525919C11.9816 0.0345529 12.6828 0.0345531 12.9285 0.525919L13.5662 1.80125C13.6307 1.93027 13.7353 2.03489 13.8643 2.0994L15.1397 2.73706C15.631 2.98275 15.631 3.68395 15.1397 3.92963L13.8643 4.5673C13.7353 4.63181 13.6307 4.73643 13.5662 4.86544L12.9285 6.14078C12.6828 6.63215 11.9816 6.63215 11.736 6.14078L11.0983 4.86544C11.0338 4.73643 10.9292 4.63181 10.8001 4.5673L9.52481 3.92963C9.03345 3.68395 9.03345 2.98275 9.52481 2.73706L10.8001 2.0994Z",fill:"#EF4444"}),e.jsx("path",{d:"M3.68904 4.32162C3.81806 4.25711 3.92267 4.15249 3.98718 4.02348L4.40263 3.19259C4.64831 2.70122 5.34951 2.70122 5.5952 3.19259L6.01064 4.02348C6.07515 4.15249 6.17977 4.25711 6.30878 4.32162L7.13967 4.73706C7.63104 4.98275 7.63104 5.68395 7.13968 5.92963L6.30878 6.34508C6.17977 6.40959 6.07515 6.5142 6.01064 6.64322L5.5952 7.47411C5.34951 7.96548 4.64831 7.96548 4.40263 7.47411L3.98718 6.64322C3.92267 6.5142 3.81806 6.40959 3.68904 6.34508L2.85815 5.92964C2.36678 5.68395 2.36678 4.98275 2.85815 4.73706L3.68904 4.32162Z",fill:"#EF4444"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.5631 3.1736C22.0195 3.48457 22.1374 4.10666 21.8265 4.56308L18.1222 10H20.6667C21.219 10 21.6667 10.4477 21.6667 11V23.6667C21.6667 25.6917 20.0251 27.3334 18 27.3334H4.00004C1.975 27.3334 0.333374 25.6917 0.333374 23.6667V11C0.333374 10.4477 0.781089 10 1.33337 10H15.7021L20.1736 3.43697C20.4846 2.98055 21.1067 2.86263 21.5631 3.1736ZM8.00004 15.3333C7.44776 15.3333 7.00004 15.7811 7.00004 16.3333C7.00004 16.8856 7.44776 17.3333 8.00004 17.3333H14C14.5523 17.3333 15 16.8856 15 16.3333C15 15.7811 14.5523 15.3333 14 15.3333H8.00004Z",fill:"#EF4444"})]})}),e.jsx("h5",{children:"API build (by AI ✨)"}),e.jsx("p",{className:"text-sm  text-gray-600",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]}),e.jsxs("div",{className:"flex flex-col gap-3 rounded-[8px] bg-gray-100 px-10 py-5 text-center",children:[e.jsx("h5",{children:"See first hand 👀"}),e.jsx("p",{className:" text-sm text-gray-600",children:"Try Baas for 7 days - no strings attached."}),e.jsx("button",{className:"rounded-[6px] bg-indigo-600 px-[17px] py-[9px] text-sm text-white shadow-sm",children:"Start FREE Trial 🚀"}),e.jsx("small",{className:"text-xs text-gray-600",children:"(No credit card required)"})]})]})]})})}function o(){return e.jsxs("svg",{width:"154",height:"85",viewBox:"0 0 154 85",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M138.868 41.5597C138.818 41.1242 139.131 40.7308 139.566 40.6811C141.86 40.4193 143.77 39.7583 146.016 38.9293C146.014 38.93 146.013 38.9303 146.013 38.9303C146.013 38.9303 146.022 38.9266 146.049 38.9156L146.164 38.8683C146.259 38.8295 146.387 38.7779 146.532 38.7209C146.821 38.6084 147.195 38.4692 147.544 38.3659C147.717 38.3145 147.899 38.267 148.07 38.2376C148.213 38.213 148.455 38.1799 148.692 38.2357C148.819 38.2656 149.036 38.3419 149.206 38.5508C149.396 38.7835 149.424 39.0518 149.393 39.2541C149.366 39.4345 149.291 39.5796 149.234 39.6742C149.172 39.7762 149.097 39.8726 149.02 39.9616C147.94 41.2008 146.751 42.3321 145.576 43.4287C145.432 43.5636 145.288 43.6979 145.144 43.8319C144.11 44.7946 143.098 45.737 142.153 46.7373C142.042 46.8546 141.512 47.4721 141.01 48.0778C140.762 48.3762 140.532 48.6592 140.373 48.863C140.318 48.9338 140.276 48.9891 140.247 49.0291C140.152 49.2517 139.956 49.428 139.702 49.489C139.63 49.5065 139.37 49.5599 139.099 49.3946C138.781 49.2009 138.723 48.886 138.718 48.7445C138.712 48.6091 138.743 48.5054 138.755 48.4687C138.77 48.4203 138.787 48.3821 138.798 48.3595C138.834 48.2821 138.88 48.2137 138.902 48.1823C138.958 48.1002 139.036 47.996 139.121 47.8873C139.295 47.6642 139.538 47.3664 139.788 47.0645C140.281 46.4697 140.846 45.8091 140.999 45.647C141.982 44.6071 143.034 43.6278 144.064 42.6686C144.208 42.5349 144.351 42.4015 144.493 42.2685C145.122 41.6815 145.74 41.0984 146.335 40.5033C144.171 41.2986 142.161 41.9824 139.746 42.2581C139.311 42.3079 138.917 41.9951 138.868 41.5597ZM140.205 49.0887C140.205 49.0888 140.206 49.0874 140.208 49.0845C140.206 49.0872 140.205 49.0887 140.205 49.0887Z",fill:"#FB923C"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M77.544 15.7511C79.4803 16.8338 81.0922 18.967 81.7172 20.9967C83.6143 27.1572 81.6117 34.4452 79.8541 39.991C78.4581 44.3957 77.0357 48.081 74.948 51.7954C76.6985 54.8586 78.8715 57.4532 81.4423 59.4399C85.8551 62.8501 91.4945 64.5131 98.3314 63.6293C112.316 61.8214 123.88 54.121 135.453 46.4139C137.449 45.0846 139.446 43.755 141.455 42.4554C141.823 42.2174 142.314 42.3228 142.552 42.6908C142.79 43.0589 142.685 43.5502 142.317 43.7882C140.345 45.0638 138.373 46.3783 136.39 47.6997C124.832 55.4035 112.92 63.3439 98.5349 65.2034C91.3041 66.1381 85.237 64.3784 80.4718 60.6958C77.9387 58.7383 75.789 56.2498 74.0291 53.361C73.5234 54.1874 72.9808 55.0204 72.3943 55.868C66.0452 65.0446 56.6459 69.496 45.5489 67.4494C34.2694 65.3691 23.4955 59.6863 13.8697 53.9274C9.80786 51.4974 6.27754 49.0366 3.15772 45.3788C2.87328 45.0453 2.91304 44.5444 3.24653 44.26C3.58001 43.9756 4.08093 44.0153 4.36536 44.3488C7.32035 47.8133 10.6794 50.1692 14.6846 52.5653C24.2872 58.3103 34.8547 63.8631 45.8368 65.8885C56.2593 67.8106 65.0591 63.6802 71.089 54.9649C71.8362 53.885 72.5101 52.8299 73.1261 51.7805C71.9959 49.6698 71.0558 47.3758 70.3078 44.9426C69.1472 41.1672 68.0091 36.0316 67.827 30.9504C67.6457 25.8939 68.4058 20.7483 71.1911 17.0891C72.0889 15.9096 73.1579 15.2516 74.3109 15.0729C75.4486 14.8966 76.5636 15.2029 77.544 15.7511ZM74.0557 50.1204C73.1841 48.3633 72.4392 46.4744 71.825 44.4762C70.6917 40.79 69.589 35.7964 69.4132 30.8935C69.2366 25.9659 70.0016 21.2725 72.4541 18.0505C73.1476 17.1393 73.8738 16.7468 74.554 16.6414C75.2497 16.5336 76.006 16.7096 76.7693 17.1364C78.3204 18.0037 79.6852 19.7913 80.2003 21.4638C81.9309 27.0839 80.1176 33.9056 78.341 39.5115C77.0818 43.4847 75.814 46.8238 74.0557 50.1204Z",fill:"#FB923C"})]})}function m(){return e.jsx("div",{className:"my-[6rem]",children:e.jsxs("div",{style:{backgroundImage:"url(/images/dots-pattern.png)"},className:"relative mx-auto flex w-[868px] max-w-full flex-col items-center justify-between gap-20 rounded-[8px] bg-gray-900 px-[40px] py-[4rem] text-white md:flex-row",children:[e.jsxs("div",{className:"flex-[50%]",children:[e.jsx("h3",{children:"Give it a try - risk free!"}),e.jsx("p",{className:"mt-2 text-sm text-gray-200",children:"See for yourself how Baas can speed up your work 10x 🔥. Aenean diam nisl, gravida varius ligula sed, efficitur sagittis mi. Duis at justo eu dui hendrerit finibus sit amet sed lorem."})]}),e.jsx("div",{className:"absolute bottom-0 left-[50%] hidden translate-x-[-10%] md:block",children:e.jsx(o,{})}),e.jsxs("div",{className:"flex-[30%] text-center",children:[e.jsx("button",{className:"w-full rounded-[6px] bg-indigo-600 px-[20px] py-[10px] text-xs text-white shadow-sm",children:"Start FREE Trial"}),e.jsx("p",{className:"mt-4 text-xs text-gray-200",children:"(No credit card required)"})]})]})})}function u(){return e.jsxs("div",{className:" my-[6rem] md:mx-[6rem]",children:[e.jsxs("div",{className:"mx-auto my-8 w-[868px] max-w-full text-center ",children:[e.jsx("p",{className:"text-orange-500",children:"Features"}),e.jsx("h1",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text text-3xl leading-tight text-transparent md:text-5xl",children:"Detailed view on key features"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-10 md:grid-cols-5",children:[e.jsxs("div",{className:"md:col-span-3",children:[e.jsx("div",{className:"bg- h-[300px] w-[600px] max-w-full rounded-[8px] bg-gray-100",style:{backgroundImage:"url(/images/Wireframes-API-1.png)",backgroundPosition:"center",backgroundRepeat:"no-repeat",backgroundSize:"cover"}}),e.jsxs("div",{className:"mt-4 flex flex-col gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"22",height:"25",viewBox:"0 0 25 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M1.82305 9.06971C1.31554 9.06971 0.884947 9.23795 0.530989 9.57443C0.177032 9.91091 0 10.3147 0 10.7859V18.0215C0 18.5045 0.176979 18.9135 0.530989 19.25C0.884947 19.5865 1.31554 19.7548 1.82305 19.7548C2.33017 19.7548 2.75816 19.5865 3.10663 19.25C3.45441 18.9135 3.62878 18.5045 3.62878 18.0215V10.7859C3.62878 10.3146 3.4518 9.91091 3.09779 9.57443C2.74378 9.23795 2.31878 9.06971 1.82305 9.06971Z",fill:"#262626"}),e.jsx("path",{d:"M16.4805 2.57431L17.7373 0.370338C17.8198 0.224426 17.7905 0.112536 17.649 0.0338593C17.4955 -0.0339831 17.3775 0.000393786 17.2951 0.13456L16.0207 2.35615C14.8993 1.88484 13.7133 1.64876 12.4626 1.64876C11.2117 1.64876 10.0256 1.88489 8.90464 2.35615L7.63021 0.13456C7.54739 0.000393786 7.42942 -0.0336793 7.27625 0.0338593C7.13448 0.112891 7.10513 0.224426 7.18795 0.370338L8.4448 2.57431C7.17037 3.19213 6.15537 4.05256 5.40016 5.15748C4.64495 6.26301 4.26719 7.47111 4.26719 8.78391H20.6403C20.6403 7.47142 20.2625 6.26326 19.5073 5.15748C18.7521 4.05256 17.7429 3.19213 16.4805 2.57431ZM9.21428 5.78001C9.07837 5.90952 8.91603 5.97397 8.72734 5.97397C8.53827 5.97397 8.37924 5.90952 8.2495 5.78001C8.11976 5.65111 8.05489 5.4974 8.05489 5.31742C8.05489 5.13804 8.11976 4.98403 8.2495 4.85482C8.37924 4.72592 8.53864 4.66147 8.72734 4.66147C8.91603 4.66147 9.07837 4.72592 9.21428 4.85482C9.34988 4.98433 9.418 5.13804 9.418 5.31742C9.41762 5.4971 9.34956 5.65111 9.21428 5.78001ZM16.6751 5.78001C16.5451 5.90952 16.3857 5.97397 16.1973 5.97397C16.0082 5.97397 15.8459 5.90952 15.7102 5.78001C15.5744 5.65111 15.5066 5.4974 15.5066 5.31742C15.5066 5.13804 15.5744 4.98403 15.7102 4.85482C15.8459 4.72592 16.0082 4.66147 16.1973 4.66147C16.386 4.66147 16.545 4.72592 16.6751 4.85482C16.8049 4.98433 16.8697 5.13804 16.8697 5.31742C16.8697 5.4971 16.8049 5.65111 16.6751 5.78001Z",fill:"#262626"}),e.jsx("path",{d:"M4.33622 20.5954C4.33622 21.1119 4.52492 21.5492 4.90236 21.908C5.28013 22.2668 5.74007 22.4461 6.28304 22.4461H7.59299L7.61094 26.2663C7.61094 26.7487 7.78792 27.1583 8.14188 27.4949C8.49583 27.8314 8.92089 27.9996 9.4163 27.9996C9.92349 27.9996 10.3543 27.8314 10.7084 27.4949C11.0624 27.1583 11.2393 26.7487 11.2393 26.2663V22.4465H13.682V26.2663C13.682 26.7487 13.8589 27.1583 14.2129 27.4949C14.567 27.8314 14.9974 27.9996 15.505 27.9996C16.0122 27.9996 16.443 27.8314 16.7971 27.4949C17.1511 27.1583 17.328 26.7487 17.328 26.2663V22.4465H18.6556C19.1866 22.4465 19.6406 22.2671 20.0187 21.9083C20.3961 21.5495 20.5849 21.1122 20.5849 20.5958V9.38853H4.33622V20.5954Z",fill:"#262626"}),e.jsx("path",{d:"M23.0998 9.06971C22.6041 9.06971 22.1794 9.23547 21.8254 9.56607C21.4714 9.89729 21.2944 10.3041 21.2944 10.7859V18.0215C21.2944 18.5045 21.4714 18.9135 21.8254 19.25C22.1794 19.5865 22.6044 19.7548 23.0998 19.7548C23.607 19.7548 24.0379 19.5865 24.3919 19.25C24.7459 18.9135 24.9228 18.5045 24.9228 18.0215V10.7859C24.9228 10.3041 24.7459 9.89729 24.3919 9.56607C24.0379 9.23547 23.607 9.06971 23.0998 9.06971Z",fill:"#262626"})]})}),e.jsx("h5",{children:"Feature name"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("div",{className:"h-[300px] w-full rounded-[8px] bg-gray-100",style:{backgroundImage:"url(/images/Wireframes-API-1.png)",backgroundPosition:"center",backgroundRepeat:"no-repeat",backgroundSize:"cover"}}),e.jsxs("div",{className:"mt-4 flex flex-col gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"22",height:"25",viewBox:"0 0 25 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.9637 0C16.4051 0.102569 14.5835 1.05177 13.5219 2.28781C12.5536 3.40911 11.7569 5.07456 12.0675 6.69306C13.7704 6.74348 15.5299 5.77168 16.5495 4.51477C17.5032 3.34479 18.2249 1.68978 17.9637 0Z",fill:"#262626"}),e.jsx("path",{d:"M24.1247 9.51663C22.6283 7.73123 20.5252 6.6951 18.5392 6.6951C15.9172 6.6951 14.8082 7.88943 12.9865 7.88943C11.1082 7.88943 9.68125 6.69858 7.41379 6.69858C5.18652 6.69858 2.81491 7.99373 1.31119 10.2085C-0.802793 13.3273 -0.441023 19.1912 2.98484 24.1858C4.21084 25.9729 5.84794 27.9826 7.98933 27.9999C9.89502 28.0173 10.4322 26.8369 13.0139 26.8247C15.5957 26.8108 16.0853 28.0156 17.9874 27.9965C20.1306 27.9808 21.8572 25.7538 23.0832 23.9667C23.9621 22.6855 24.2891 22.0405 24.9706 20.5941C20.0136 18.7983 19.2188 12.0913 24.1247 9.51663Z",fill:"#262626"})]})}),e.jsx("h5",{children:"Feature name"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros."})]})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("div",{className:"bg- h-[300px] w-full max-w-full rounded-[8px] bg-gray-100",style:{backgroundImage:"url(/images/Wireframes-API-1.png)",backgroundPosition:"center",backgroundRepeat:"no-repeat",backgroundSize:"cover"}}),e.jsxs("div",{className:"mt-4 flex flex-col gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"22",height:"24",viewBox:"0 0 28 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M2.99996 0C1.71129 0 0.666626 1.04467 0.666626 2.33333V12.6667H27.3333V2.33333C27.3333 1.04467 26.2886 0 25 0H2.99996Z",fill:"#262626"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.666626 17.6667V14.6667H27.3333V17.6667C27.3333 18.9553 26.2886 20 25 20H18V24.3333C18 24.8856 17.5522 25.3333 17 25.3333H11C10.4477 25.3333 9.99996 24.8856 9.99996 24.3333V20H2.99996C1.7113 20 0.666626 18.9553 0.666626 17.6667ZM12 20V23.3333H16V20H12Z",fill:"#262626"})]})}),e.jsx("h5",{children:"Feature name"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]})]}),e.jsxs("div",{className:"md:col-span-3",children:[e.jsx("div",{className:"bg- h-[300px] w-[600px] max-w-full rounded-[8px] bg-gray-100",style:{backgroundImage:"url(/images/Wireframes-API-2.png)",backgroundPosition:"center",backgroundRepeat:"no-repeat",backgroundSize:"cover"}}),e.jsxs("div",{className:"mt-4 flex flex-col gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"22",height:"24",viewBox:"0 0 28 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M2.99996 0C1.71129 0 0.666626 1.04467 0.666626 2.33333V12.6667H27.3333V2.33333C27.3333 1.04467 26.2886 0 25 0H2.99996Z",fill:"#262626"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.666626 17.6667V14.6667H27.3333V17.6667C27.3333 18.9553 26.2886 20 25 20H18V24.3333C18 24.8856 17.5522 25.3333 17 25.3333H11C10.4477 25.3333 9.99996 24.8856 9.99996 24.3333V20H2.99996C1.7113 20 0.666626 18.9553 0.666626 17.6667ZM12 20V23.3333H16V20H12Z",fill:"#262626"})]})}),e.jsx("h5",{children:"Feature name"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]})]})]})]})}function p(){return e.jsxs("div",{className:" my-[6rem] md:mx-[6rem]",children:[e.jsxs("div",{className:"mx-auto my-8 w-[868px] max-w-full text-center ",children:[e.jsx("p",{className:"text-indigo-500",children:"Quick answers"}),e.jsx("h1",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text text-3xl leading-tight text-transparent md:text-5xl",children:"Frequently asked questions"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-7 md:grid-cols-2",children:[1,2,3,4,5,6].map((s,t)=>e.jsxs("div",{className:"flex gap-4 rounded-[8px] bg-gray-100 p-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"22",height:"22",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 0.666748C6.63622 0.666748 0.666687 6.63629 0.666687 14.0001C0.666687 21.3639 6.63622 27.3334 14 27.3334C21.3638 27.3334 27.3334 21.3639 27.3334 14.0001C27.3334 6.63629 21.3638 0.666748 14 0.666748ZM12.3334 9.33342C12.1493 9.33342 12 9.48265 12 9.66675V10.3334C12 10.8857 11.5523 11.3334 11 11.3334C10.4477 11.3334 10 10.8857 10 10.3334V9.66675C10 8.37808 11.0447 7.33342 12.3334 7.33342H15.6667C16.9554 7.33342 18 8.37808 18 9.66675V11.2865C18 12.0667 17.6101 12.7952 16.961 13.228L15.1485 14.4363C15.0557 14.4981 15 14.6022 15 14.7137V15.6667C15 16.219 14.5523 16.6667 14 16.6667C13.4477 16.6667 13 16.219 13 15.6667V14.7137C13 13.9335 13.3899 13.205 14.0391 12.7722L15.8516 11.5639C15.9443 11.502 16 11.398 16 11.2865V9.66675C16 9.48265 15.8508 9.33342 15.6667 9.33342H12.3334ZM14 18.0001C13.2636 18.0001 12.6667 18.597 12.6667 19.3334C12.6667 20.0698 13.2636 20.6667 14 20.6667C14.7364 20.6667 15.3334 20.0698 15.3334 19.3334C15.3334 18.597 14.7364 18.0001 14 18.0001Z",fill:"url(#paint0_linear_577_579)"}),e.jsx("defs",{children:e.jsxs("linearGradient",{id:"paint0_linear_577_579",x1:"27.3334",y1:"0.666728",x2:"0.272937",y2:"1.07246",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#525252"}),e.jsx("stop",{offset:"1","stop-color":"#262626"})]})})]})}),e.jsxs("div",{children:[e.jsx("h6",{className:"mb-3",children:"Question example one?"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]})]},t))}),e.jsxs("div",{className:"mx-auto my-10 flex w-[400px] max-w-full flex-col justify-center  text-center",children:[e.jsx("p",{className:"py-3 text-gray-500",children:"Still got questions? We're here to help 🤙"}),e.jsxs("button",{className:"mx-auto flex w-fit items-center justify-center gap-2 rounded-[8px] bg-indigo-600 px-4 py-2 text-white",children:[e.jsx("span",{children:"Get in touch "}),e.jsx("div",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M0.225172 1.65211C-0.0682953 0.771708 0.84918 -0.0240148 1.67923 0.391011L15.0342 7.06847C15.8019 7.45235 15.8019 8.54798 15.0342 8.93186L1.67923 15.6093C0.849177 16.0243 -0.0682947 15.2286 0.225172 14.3482L2.13291 8.625H5.70834C6.05352 8.625 6.33334 8.34518 6.33334 8C6.33334 7.65482 6.05352 7.375 5.70834 7.375H2.1328L0.225172 1.65211Z",fill:"white"})})})]})]})]})}function h(){return e.jsx(e.Fragment,{children:e.jsxs("div",{className:" my-[6rem] md:mx-[6rem]",children:[e.jsxs("div",{className:"mx-auto my-8 w-[868px] max-w-full text-center ",children:[e.jsx("p",{className:"text-orange-500",children:"Tagline"}),e.jsx("h1",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text text-3xl leading-tight text-transparent md:text-5xl",children:"One platform for all your dev needs"}),e.jsx("p",{className:"mx-auto max-w-full py-4 text-sm text-gray-500 md:w-[70%]",children:"Nunc scelerisque accumsan ante vestibulum consequat. Quisque justo urna, rhoncus in erat ac, lobortis eleifend nulla."})]}),e.jsx("div",{children:e.jsxs("div",{className:"gap-15 flex flex-col items-center justify-items-center md:flex-row",children:[e.jsx("div",{className:"flex-1 xs:mb-5 sm:mb-5",children:e.jsxs("div",{children:[e.jsxs("div",{className:"cursor-pointer border-l-[3px] border-indigo-500 py-2 pl-5 pr-5",children:[e.jsx("h5",{children:"Feature preview example one"}),e.jsx("p",{className:"py-2 text-xs  text-gray-500",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]}),e.jsxs("div",{className:"cursor-pointer border-l border-gray-300 py-2 pl-5 pr-5",children:[e.jsx("h5",{children:"Feature preview example one"}),e.jsx("p",{className:"py-2 text-xs  text-gray-500",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]}),e.jsxs("div",{className:"cursor-pointer border-l border-gray-300 py-2 pl-5 pr-5",children:[e.jsx("h5",{children:"Feature preview example one"}),e.jsx("p",{className:"py-2 text-xs  text-gray-500",children:"Mauris mattis lorem a sagittis sagittis. Nulla at vulputate augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo vel suscipit sit amet, imperdiet sed leo."})]})]})}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"mx-auto flex h-[360px] max-w-full items-center justify-center bg-gray-100 md:w-[400px]",children:e.jsx("img",{src:"/images/baas-features-drawer.png",alt:"Baas Feature wireframe",className:"mx-auto w-[70%]"})})})]})})]})})}function i(){r.useState();const s="rounded-[6px] bg-gradient-to-r from-[#262626] to-[#525252]  text-gray-100 text-gray-100 px-1 py-1 md:px-3 md:py-2";return e.jsxs("div",{className:"mx-auto flex w-fit max-w-full items-center gap-1 rounded-[8px] border border-gray-200 p-1 pr-3 md:gap-5",children:[e.jsx("button",{className:`${s} text-[.40rem]  md:text-xs `,children:"Wireframes  ✍️"}),e.jsx("div",{children:e.jsx("svg",{width:"21",height:"20",className:"h-[14px] w-[15px] md:h-[20px] md:w-[21px]",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.4435 4.86011L17.0626 9.47917C17.388 9.80461 17.388 10.3322 17.0626 10.6577L12.4435 15.2768M16.6102 10.0684H3.69348",stroke:"#8D8D8D","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"text-[.40rem] md:text-xs",children:"Working Prototype   ⚙️"}),e.jsx("div",{children:e.jsx("svg",{width:"21",height:"20",className:"h-[14px] w-[15px] md:h-[20px] md:w-[21px]",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.4435 4.86011L17.0626 9.47917C17.388 9.80461 17.388 10.3322 17.0626 10.6577L12.4435 15.2768M16.6102 10.0684H3.69348",stroke:"#8D8D8D","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"text-[.40rem] md:text-xs",children:"Code export 🧑🏼‍💻"}),e.jsx("div",{children:e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",className:"h-[14px] w-[15px] md:h-[20px] md:w-[21px]",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.4435 4.86011L17.0626 9.47917C17.388 9.80461 17.388 10.3322 17.0626 10.6577L12.4435 15.2768M16.6102 10.0684H3.69348",stroke:"#8D8D8D","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"text-[.40rem] md:text-xs",children:"API (by AI✨)"})]})}const g=[{title:"Feature preview example one",description:` Mauris mattis lorem a sagittis sagittis. Nulla at vulputate
    augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo
    vel suscipit sit amet, imperdiet sed leo.`,color:"red"},{title:"Feature preview example two",description:` Mauris mattis lorem a sagittis sagittis. Nulla at vulputate
    augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo
    vel suscipit sit amet, imperdiet sed leo.`,color:"indigo"},{title:"Feature preview example three",description:` Mauris mattis lorem a sagittis sagittis. Nulla at vulputate
    augue. Sed sit amet mi tortor. Praesent lacus lectus, commodo
    vel suscipit sit amet, imperdiet sed leo.`,color:"red"}];function C(){return e.jsxs("div",{className:"my-[6rem] md:mx-[3rem]",children:[e.jsxs("div",{className:"mx-auto my-8 w-[868px] max-w-full text-center ",children:[e.jsx("p",{className:"text-green-500",children:"Tagline"}),e.jsx("h1",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text text-3xl leading-tight text-transparent md:text-5xl",children:"One platform for all your dev needs"}),e.jsx("p",{className:"mx-auto max-w-full py-4 text-gray-500 md:w-[70%] ",children:"Nunc scelerisque accumsan ante vestibulum consequat. Quisque justo urna, rhoncus in erat ac, lobortis eleifend nulla."}),e.jsx("div",{className:"my-5",children:e.jsx(i,{})})]}),e.jsx("div",{className:"flex flex-col gap-10",children:g.map((s,t)=>e.jsxs("div",{className:"flex flex-col gap-3 md:flex-row",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:`border-l-2 border-${s.color}-600 pl-3`,children:[e.jsx("h4",{children:s.title}),e.jsx("p",{className:"py-4 text-sm text-gray-500",children:s.description})]})}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"mx-auto flex h-[270px] w-[470px] max-w-full items-center justify-center rounded-[8px] bg-gray-100 p-5 md:h-[350px]",children:e.jsx("img",{src:"images/feature-preview.png",className:"mx-auto h-auto w-[100%]",alt:"feature preview"})})})]},t))})]})}const j=()=>e.jsx(e.Fragment,{children:e.jsx("svg",{width:"15",height:"14",viewBox:"0 0 15 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.6929 0.394753C15.0272 0.63933 15.0999 1.1086 14.8553 1.4429L6.07592 13.4429C5.95542 13.6076 5.77301 13.7161 5.57076 13.7433C5.36852 13.7706 5.1639 13.7142 5.00411 13.5873L0.283519 9.83731C-0.040813 9.57966 -0.0948719 9.10787 0.162775 8.78354C0.420421 8.45921 0.892208 8.40515 1.21654 8.6628L5.3261 11.9274L13.6447 0.557204C13.8893 0.222907 14.3586 0.150175 14.6929 0.394753Z",fill:"#6366F1"})})}),f=[{icon:"🧑‍💻‍",plan:"solo",price:"9.99",features:["1 seat","Feature name one","Feature name one","Feature name one","Feature name one","Feature name one"]},{icon:"👨🏽‍💻👩🏼💻🧑🏾‍💻",plan:"Team",price:"9.99",features:["Up to 5 seats","Feature name one","Feature name one","Feature name one","Feature name one","Feature name one"]},{icon:"🚀🚀🚀‍🚀",plan:"Agency",price:"499.99",features:["Up to 15 seats","Feature name one","Feature name one","Feature name one","Feature name one","Feature name one"]}];function v(){return e.jsx(e.Fragment,{children:e.jsxs("div",{className:" my-[6rem] md:mx-[6rem]",children:[e.jsxs("div",{className:"mx-auto my-8 w-[868px] max-w-full text-center",children:[e.jsx("p",{className:"text-orange-500",children:"Pricing"}),e.jsx("h1",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text text-3xl leading-tight text-transparent md:text-5xl",children:"Every business has different needs"}),e.jsx("p",{className:"mx-auto max-w-full py-4 text-gray-500 md:w-9/12",children:"We offer flexible pricing plans tailored to meet the needs and demand of solopreneurs to large agencies."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-center gap-3",children:[e.jsx("p",{children:"Monthly"}),e.jsx("div",{children:e.jsxs("label",{className:"relative inline-flex cursor-pointer items-center",children:[e.jsx("input",{type:"checkbox",value:"",className:"peer sr-only"}),e.jsx("div",{className:"dark:peer-focus:gray-300 peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] checked:outline-none focus:outline-none peer-checked:bg-gray-200 peer-checked:outline-none peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4  dark:border-gray-200 dark:bg-gray-300"})]})}),e.jsx("p",{children:"Annually"})]}),e.jsx("div",{className:"mx-auto mt-3 w-fit rounded-full bg-gray-900 px-4 py-1 text-center",children:e.jsx("p",{className:"text-sm text-gray-200",children:"Save 25% on Annual plan 🔥"})})]}),e.jsx("div",{className:"mt-5 grid gap-8 md:grid-cols-3",children:f.map((s,t)=>e.jsxs("div",{className:"w-full rounded-[8px] border border-gray-300 bg-gray-100 px-5 py-9",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{children:s.icon}),e.jsx("p",{className:"my-2",children:s.plan}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsxs("h3",{className:"font-bold",children:["$",s.price]}),e.jsx("p",{className:"text-gray-500",children:"/month"})]})]}),e.jsx("button",{className:t===1?"my-3 w-full rounded-[8px] border border-gray-300 bg-indigo-600 py-2  text-sm text-white":"my-3 w-full rounded-[8px] border border-gray-300 bg-white py-2 text-sm",children:"Start free trial"}),e.jsx("div",{className:"mt-3 flex flex-col gap-3",children:s.features.map((a,n)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{children:e.jsx(j,{})}),e.jsx("p",{className:"text-sm text-gray-800",children:a})]},n))})]},t))})]})})}function w(){return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"my-[6rem]",children:[e.jsxs("div",{className:"mx-auto w-[668px] max-w-full text-center ",children:[e.jsx("div",{className:"mx-auto w-fit rounded-[50px] bg-gradient-to-r from-[#262626] to-[#525252] px-3 py-1 text-xs text-gray-100",children:e.jsx("p",{children:"One stop shop for your dev house"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text leading-tight text-transparent sm:text-lg md:text-5xl",children:"AI-driven, one stop shop for your dev house"}),e.jsxs("p",{className:"mt-3 text-gray-600",children:["Speed up your process by creating"," ",e.jsx("strong",{className:"text-gray-900",children:"wireframes"})," and",e.jsxs("strong",{className:"text-gray-900",children:[" ","working prototypes, code exports"]})," ","and ",e.jsx("strong",{className:"text-gray-900",children:"APIs"})," built by AI - all in one platform"]})]}),e.jsxs("div",{className:"mt-5 flex justify-center  gap-3",children:[e.jsx("button",{className:"rounded-[6px] bg-indigo-600 px-[14px] py-[7px] text-xs text-white shadow-sm",children:"Start FREE Trial 🚀"}),e.jsxs("button",{className:"flex items-center gap-2 rounded-[6px] border border-gray-300 bg-transparent px-[14px] py-[7px] text-xs text-gray-700 shadow-sm",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.38082 1.89899C5.40893 1.30996 4.16663 2.0097 4.16663 3.14615V16.8539C4.16663 17.9903 5.40893 18.69 6.38082 18.101L17.6897 11.2472C18.6263 10.6795 18.6263 9.3205 17.6897 8.75284L6.38082 1.89899Z",fill:"#8D8D8D"})})," ",e.jsx("span",{children:"Watch Demo"})]})]})]}),e.jsxs("div",{className:"my-10",children:[e.jsx("div",{className:"mb-5",children:e.jsx(i,{})}),e.jsx("div",{className:"m mx-auto w-[868px] max-w-full",children:e.jsx("img",{src:"/images/dashboard-wireframe.png",alt:"dashboard-wireframe",className:"w-full"})})]})]})})}function N(){return e.jsxs("div",{className:"my-[6rem] md:mx-[3rem]",children:[e.jsxs("div",{className:"mx-auto my-8 w-[868px] max-w-full text-center ",children:[e.jsx("p",{className:"text-green-500",children:"Tagline"}),e.jsx("h1",{className:"bg-gradient-to-r from-[#262626] to-[#525252] bg-clip-text text-3xl leading-tight text-transparent md:text-5xl",children:"One platform for all your dev needs"}),e.jsx("p",{className:"mx-auto max-w-full py-4 text-gray-500 md:w-[70%] ",children:"Nunc scelerisque accumsan ante vestibulum consequat. Quisque justo urna, rhoncus in erat ac, lobortis eleifend nulla."}),e.jsx("div",{className:"my-5",children:e.jsx(i,{})})]}),e.jsxs("div",{className:"rounded-[8px] bg-gray-100 p-5",children:[e.jsx("h5",{className:"mb-5",children:"Wireframes - quick and easy"}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:[e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{children:e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M3.09838 1.48959e-06C2.75509 -2.14376e-05 2.43974 -4.24882e-05 2.17618 0.0214915C1.892 0.0447096 1.5811 0.09786 1.27403 0.254323C0.834982 0.478027 0.478027 0.834982 0.254323 1.27403C0.0978599 1.5811 0.0447096 1.892 0.0214915 2.17618C-4.24907e-05 2.43974 -2.14376e-05 2.75502 1.48958e-06 3.09831V20.9016C-2.14376e-05 21.2449 -4.24882e-05 21.5603 0.0214915 21.8238C0.0447096 22.108 0.0978604 22.4189 0.254323 22.726C0.478027 23.165 0.834982 23.522 1.27403 23.7457C1.5811 23.9022 1.892 23.9553 2.17618 23.9785C2.43975 24.0001 2.75505 24 3.09836 24L8.00001 24V10H6.65532e-06V8H8.00001V1.48959e-06H3.09838Z",fill:"#2563EB"}),e.jsx("path",{d:"M10 1.48959e-06V8H24L24 3.09836C24 2.75505 24.0001 2.43975 23.9785 2.17618C23.9553 1.892 23.9022 1.5811 23.7457 1.27403C23.522 0.834982 23.165 0.478027 22.726 0.254323C22.4189 0.0978599 22.108 0.0447096 21.8238 0.0214915C21.5603 -4.24907e-05 21.245 -2.14376e-05 20.9017 1.48959e-06H10Z",fill:"#2563EB"}),e.jsx("path",{d:"M24 10H10V24L20.9016 24C21.245 24 21.5603 24.0001 21.8238 23.9785C22.108 23.9553 22.4189 23.9022 22.726 23.7457C23.165 23.522 23.522 23.165 23.7457 22.726C23.9022 22.4189 23.9553 22.108 23.9785 21.8238C24.0001 21.5603 24 21.245 24 20.9016L24 10Z",fill:"#2563EB"})]})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{children:e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.66663 5C7.66663 4.44772 8.11434 4 8.66663 4H23.3333C23.8856 4 24.3333 4.44772 24.3333 5V6.66667H28.3333C28.8856 6.66667 29.3333 7.11438 29.3333 7.66667V24.3333C29.3333 24.8856 28.8856 25.3333 28.3333 25.3333H24.3333V27C24.3333 27.5523 23.8856 28 23.3333 28H8.66663C8.11434 28 7.66663 27.5523 7.66663 27V25.3333H3.66663C3.11434 25.3333 2.66663 24.8856 2.66663 24.3333V7.66667C2.66663 7.11438 3.11434 6.66667 3.66663 6.66667H7.66663V5ZM7.66663 8.66667H4.66663V23.3333H7.66663V8.66667ZM24.3333 23.3333H27.3333V8.66667H24.3333V23.3333Z",fill:"#0EA5E9"})})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Aliquam sed erat lacus. Nam iaculis nulla non elit porttitor, at finibus ipsum hendrerit. Aliquam iaculis orci quis arcu condimentum egestas."})]}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{children:e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 6.33333C4 5.04467 5.04467 4 6.33333 4H25.6667C26.9553 4 28 5.04467 28 6.33333V25.6667C28 26.9553 26.9553 28 25.6667 28H6.33333C5.04467 28 4 26.9553 4 25.6667V6.33333ZM14.3738 11.2929C14.7643 11.6834 14.7643 12.3166 14.3738 12.7071L11.3166 15.7643C11.1864 15.8945 11.1864 16.1055 11.3166 16.2357L14.3738 19.2929C14.7643 19.6834 14.7643 20.3166 14.3738 20.7071C13.9832 21.0976 13.3501 21.0976 12.9596 20.7071L9.90237 17.6499C8.99115 16.7387 8.99115 15.2613 9.90237 14.3501L12.9596 11.2929C13.3501 10.9024 13.9832 10.9024 14.3738 11.2929ZM19.0404 11.2929C18.6499 10.9024 18.0168 10.9024 17.6262 11.2929C17.2357 11.6834 17.2357 12.3166 17.6262 12.7071L20.6834 15.7643C20.8136 15.8945 20.8136 16.1055 20.6834 16.2357L17.6262 19.2929C17.2357 19.6834 17.2357 20.3166 17.6262 20.7071C18.0168 21.0976 18.6499 21.0976 19.0404 20.7071L22.0976 17.6499C23.0089 16.7387 23.0089 15.2613 22.0976 14.3501L19.0404 11.2929Z",fill:"#FBBF24"})})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{children:e.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M6.33329 4C5.78101 4 5.33329 4.44772 5.33329 5C5.33329 5.55228 5.78101 6 6.33329 6H25.6666C26.2189 6 26.6666 5.55228 26.6666 5C26.6666 4.44772 26.2189 4 25.6666 4H6.33329Z",fill:"#EF4444"}),e.jsx("path",{d:"M4.99996 8C3.7113 8 2.66663 9.04467 2.66663 10.3333V24.3333C2.66663 25.622 3.7113 26.6667 4.99996 26.6667H27C28.2886 26.6667 29.3333 25.622 29.3333 24.3333V10.3333C29.3333 9.04467 28.2886 8 27 8H4.99996Z",fill:"#EF4444"})]})}),e.jsx("p",{className:"text-sm text-gray-500",children:"In quis ultricies nibh, in suscipit lectus. Donec nec auctor sapien. Vivamus porta mauris sed augue pretium, a tristique metus ultricies."})]}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{children:e.jsxs("svg",{width:"29",height:"28",viewBox:"0 0 29 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M5.08019 27.1334C5.26686 27.3201 5.52019 27.4134 5.77352 27.4134C6.04019 27.4134 6.29352 27.3068 6.49352 27.1068L8.72019 24.8268C9.10686 24.4268 9.10686 23.8001 8.70686 23.4134C8.30686 23.0401 7.68019 23.0401 7.29352 23.4401L5.06686 25.7201C4.68019 26.1201 4.68019 26.7468 5.08019 27.1334Z",fill:"#14B8A6"}),e.jsx("path",{d:"M14.6609 27.4134C14.6647 27.4134 14.6669 27.4134 14.6669 27.4134H14.6535L14.6609 27.4134Z",fill:"#14B8A6"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.4802 27.4001C14.5268 27.4118 14.6345 27.4132 14.6609 27.4134C14.9115 27.4116 15.1487 27.3184 15.3335 27.1468L19.5735 23.2134C20.0535 22.7734 20.3202 22.1468 20.3202 21.5068V18.6934C26.4535 15.4001 28.9202 10.1201 28.3202 1.68011C28.2935 1.18678 27.8935 0.800115 27.4002 0.760115C18.9602 0.146781 13.6802 2.62678 10.3869 8.74678H7.57352C6.93352 8.74678 6.30686 9.01345 5.86686 9.49345L1.93352 13.7334C1.70686 13.9601 1.61352 14.2801 1.68019 14.6001C1.74686 14.9201 1.94686 15.1868 2.24019 15.3201C2.24019 15.3201 2.24807 15.3239 2.25865 15.329C2.59032 15.4877 7.20032 17.6936 9.29352 19.7868C11.3768 21.883 13.5983 26.5035 13.752 26.8231C13.7566 26.8328 13.7602 26.8401 13.7602 26.8401C13.9069 27.1334 14.1735 27.3334 14.4802 27.4001ZM19.6666 12.0001C21.1394 12.0001 22.3333 10.8062 22.3333 9.33342C22.3333 7.86066 21.1394 6.66675 19.6666 6.66675C18.1939 6.66675 17 7.86066 17 9.33342C17 10.8062 18.1939 12.0001 19.6666 12.0001Z",fill:"#14B8A6"}),e.jsx("path",{d:"M0.640189 25.6134C0.826856 25.8001 1.08019 25.8934 1.33352 25.8934C1.60019 25.8934 1.85352 25.7868 2.05352 25.5868L5.76019 21.7734C6.14686 21.3734 6.14686 20.7468 5.74686 20.3601C5.34686 19.9868 4.72019 19.9868 4.33352 20.3868L0.626855 24.2001C0.240189 24.6001 0.240189 25.2268 0.640189 25.6134Z",fill:"#14B8A6"})]})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Quisque vulputate pharetra purus, at semper augue posuere at. Duis placerat ac diam quis malesuada. Donec ac dictum eros, in mollis libero. Cras odio nisi, consequat bibendum orci ac, ullamcorper varius mi."})]}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0 2.33333C0 1.04467 1.04467 0 2.33333 0H21.6667C22.9553 0 24 1.04467 24 2.33333V21.6667C24 22.9553 22.9553 24 21.6667 24H2.33333C1.04467 24 0 22.9553 0 21.6667V2.33333ZM10.3738 7.29289C10.7643 7.68342 10.7643 8.31658 10.3738 8.70711L7.31658 11.7643C7.18641 11.8945 7.18641 12.1055 7.31658 12.2357L10.3738 15.2929C10.7643 15.6834 10.7643 16.3166 10.3738 16.7071C9.98325 17.0976 9.35008 17.0976 8.95956 16.7071L5.90237 13.6499C4.99115 12.7387 4.99115 11.2613 5.90237 10.3501L8.95956 7.29289C9.35008 6.90237 9.98325 6.90237 10.3738 7.29289ZM15.0404 7.29289C14.6499 6.90237 14.0168 6.90237 13.6262 7.29289C13.2357 7.68342 13.2357 8.31658 13.6262 8.70711L16.6834 11.7643C16.8136 11.8945 16.8136 12.1055 16.6834 12.2357L13.6262 15.2929C13.2357 15.6834 13.2357 16.3166 13.6262 16.7071C14.0168 17.0976 14.6499 17.0976 15.0404 16.7071L18.0976 13.6499C19.0089 12.7387 19.0089 11.2613 18.0976 10.3501L15.0404 7.29289Z",fill:"#A855F7"})})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Donec sit amet metus elit. Donec sagittis, ligula ac condimentum cursus, ex ex ultrices lectus, a ornare neque leo et eros. Donec tempus ligula id enim aliquam dignissim. Mauris iaculis diam non dapibus pharetra."})]})]})]})]})}function I(){return e.jsxs("div",{className:"mx-auto max-h-full min-h-full h-full overflow-auto px-5  w-full md:px-0 lg:px-10",children:[e.jsx(x,{}),e.jsx(w,{}),e.jsx(c,{}),e.jsx(h,{}),e.jsx(N,{}),e.jsx(m,{}),e.jsx(p,{}),e.jsx(C,{}),e.jsx(u,{}),e.jsx(v,{})]})}export{I as default};
