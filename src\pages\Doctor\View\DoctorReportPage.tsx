import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, useEditor, Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import { useNavigate } from "react-router-dom";
import MkdSDK from "@/utils/MkdSDK";
import ReviewOrderModal, {
  ReviewSection,
} from "@/pages/Doctor/View/ReviewOrderModal";
// Toolbar component
const Toolbar: React.FC<{ editor: Editor | null }> = ({ editor }) => {
  const [showNewChanges, setShowNewChanges] = useState(false);
  const [showOriginalAI, setShowOriginalAI] = useState(false);
  if (!editor) return null;
  return (
    <div className="w-full">
      {/* Main Toolbar */}
      <div className="flex items-center gap-2 border-b px-2 py-2 bg-white opacity-50 pointer-events-none">
        {/* Bold */}
        <button
          type="button"
          aria-label="Bold"
          aria-pressed={editor.isActive("bold")}
          className={`px-2 py-1 rounded ${editor.isActive("bold") ? "font-bold text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled
        >
          <b>B</b>
        </button>
        {/* Italic */}
        <button
          type="button"
          aria-label="Italic"
          aria-pressed={editor.isActive("italic")}
          className={`px-2 py-1 rounded ${editor.isActive("italic") ? "italic text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled
        >
          <i>I</i>
        </button>
        {/* Underline */}
        <button
          type="button"
          aria-label="Underline"
          aria-pressed={editor.isActive("underline")}
          className={`px-2 py-1 rounded ${editor.isActive("underline") ? "underline text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleUnderline?.().run()}
          disabled={!editor.can().toggleUnderline?.()}
        >
          <u>U</u>
        </button>
        {/* List Buttons */}
        <button
          type="button"
          aria-label="Bulleted List"
          aria-pressed={editor.isActive("bulletList")}
          className={`px-2 py-1 rounded ${editor.isActive("bulletList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <circle cx="4" cy="5" r="1.5" fill="currentColor" />
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="9" r="1.5" fill="currentColor" />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="13" r="1.5" fill="currentColor" />
            <rect
              x="7"
              y="12"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Numbered List"
          aria-pressed={editor.isActive("orderedList")}
          className={`px-2 py-1 rounded ${editor.isActive("orderedList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <text x="2" y="7" fontSize="6" fill="currentColor">
              1.
            </text>
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <text x="2" y="13" fontSize="6" fill="currentColor">
              2.
            </text>
            <rect
              x="7"
              y="10"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        {/* Divider */}
        <span className="mx-2 border-l h-6" />
        {/* Alignment Buttons */}
        <button
          type="button"
          aria-label="Align Left"
          aria-pressed={editor.isActive({ textAlign: "left" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "left" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="3" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Center"
          aria-pressed={editor.isActive({ textAlign: "center" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "center" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="5" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Right"
          aria-pressed={editor.isActive({ textAlign: "right" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "right" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Justify"
          aria-pressed={editor.isActive({ textAlign: "justify" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "justify" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("justify").run()}
          disabled
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="8"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      {/* Undo/Redo and Checkboxes */}
      <div className="flex items-center justify-between px-2 py-2">
        <div className="flex gap-2">
          <button
            type="button"
            aria-label="Undo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().undo().run()}
            disabled
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M7 4L3 8L7 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Undo
          </button>
          <button
            type="button"
            aria-label="Redo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().redo().run()}
            disabled
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M11 4L15 8L11 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Redo
          </button>
        </div>
        <div className="flex gap-6 items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-blue-100 border border-blue-300" />
              <span className="text-gray-500 text-sm">Original AI Text</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-gray-200 border border-gray-400" />
              <span className="text-gray-500 text-sm">New Changes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface PatientInfo {
  name: string;
  age: string;
}

interface ReportSection {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  body: string;
  report_id: string;
  order?: number;
}

const FileIcon = () => (
  <svg
    width={13}
    height={16}
    viewBox="0 0 13 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1_1864)">
      <path
        d="M2.54688 0C1.44375 0 0.546875 0.896875 0.546875 2V14C0.546875 15.1031 1.44375 16 2.54688 16H10.5469C11.65 16 12.5469 15.1031 12.5469 14V5H8.54688C7.99375 5 7.54688 4.55312 7.54688 4V0H2.54688ZM8.54688 0V4H12.5469L8.54688 0ZM4.04688 8H9.04688C9.32187 8 9.54688 8.225 9.54688 8.5C9.54688 8.775 9.32187 9 9.04688 9H4.04688C3.77187 9 3.54688 8.775 3.54688 8.5C3.54688 8.225 3.77187 8 4.04688 8Z"
        fill="white"
      />
    </g>
  </svg>
);

const VerifiedIcon = () => (
  <svg
    width={14}
    height={14}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1_1820)">
      <path
        d="M7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14Z"
        fill="#10B981"
      />
    </g>
  </svg>
);

const TimeIcon = () => (
  <svg
    width={15}
    height={14}
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1_1825)">
      <path
        d="M7.15625 0C9.01277 0 10.7932 0.737498 12.106 2.05025C13.4188 3.36301 14.1562 5.14348 14.1562 7C14.1562 8.85652 13.4188 10.637 12.106 11.9497C10.7932 13.2625 9.01277 14 7.15625 14C5.29973 14 3.51926 13.2625 2.2065 11.9497C0.893748 10.637 0.15625 8.85652 0.15625 7C0.15625 5.14348 0.893748 3.36301 2.2065 2.05025C3.51926 0.737498 5.29973 0 7.15625 0Z"
        fill="#3B82F6"
      />
    </g>
  </svg>
);

const DoctorAssessmentReport: React.FC = () => {
  const navigate = useNavigate();
  const [showReport, setShowReport] = useState(false);
  const [showReviewOrder, setShowReviewOrder] = useState(false);
  const [sections, setSections] = useState<ReportSection[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const patientInfo: PatientInfo = {
    name: localStorage.getItem("patientName") || "Mr. ClientLastName",
    age: "40 years",
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
      }),
      Underline,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
    ],
    content: sections?.[0]?.body || "",
    editable: false,
    onBlur: ({ editor }) => {
      const html = editor.getHTML();
      setSections(
        (prev) =>
          prev?.map((section, idx) =>
            idx === 0 ? { ...section, body: html } : section
          ) || []
      );
    },
    editorProps: {
      attributes: {
        class:
          "prose prose-sm max-w-none min-h-[400px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",
        spellCheck: "true",
        "aria-label": `View ${sections?.[0]?.title || "Report"}`,
      },
    },
    parseOptions: { preserveWhitespace: "full" },
  });

  useEffect(() => {
    const fetchReportData = async () => {
      try {
        const reportId = localStorage.getItem("reportId");

        if (!reportId) {
          throw new Error("No report ID found");
        }

        const sdk = new MkdSDK();
        const response = await sdk.getReportSections(reportId);

        if (response.error) {
          throw new Error(response.message || "Failed to fetch report data");
        }

        if (!response.sections || !Array.isArray(response.sections)) {
          throw new Error("Invalid response format");
        }

        setSections(response.sections);
        setShowReport(true);
        setError(null);

        // Combine all sections into one document
        const combinedContent = response.sections
          .map((section) => `${section.body}`)
          .join("");

        editor?.commands.setContent(combinedContent);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while fetching the report"
        );
        setSections(null);
      } finally {
        setIsLoading(false);
      }
    };

    const timer = setTimeout(() => {
      fetchReportData();
    }, 3500);

    return () => clearTimeout(timer);
  }, [editor]);

  // Update sections when editor content changes
  useEffect(() => {
    if (editor && sections) {
      const handleUpdate = () => {
        const html = editor.getHTML();
        // Split the content back into sections
        const sectionContents = html.split("<h2>").filter(Boolean);
        const updatedSections = sections.map((section, index) => {
          const content = sectionContents[index] || "";
          const body = content.replace(/^.*?<\/h2>/, "").trim();
          return { ...section, body };
        });
        setSections(updatedSections);
      };

      editor.on("update", handleUpdate);
      return () => {
        editor.off("update", handleUpdate);
      };
    }
  }, [editor, sections]);

  const handleProceedReview = () => setShowReviewOrder(true);
  const handleCloseReviewOrder = () => setShowReviewOrder(false);

  const handleBeginReview = async (reorderedSections: ReviewSection[]) => {
    try {
      const reportId = localStorage.getItem("reportId");
      if (!reportId) {
        throw new Error("No report ID found");
      }

      const reportSections = reorderedSections.map((section) => ({
        id: parseInt(section.id),
        title: section.title,
        order: section.order,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        body: "",
        report_id: reportId,
      }));

      const sectionOrder = reportSections.map((section) =>
        section.id.toString()
      );

      const sdk = new MkdSDK();
      const response = await sdk.updateReportSectionOrder({
        report_id: reportId,
        order: sectionOrder,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to update section order");
      }

      setShowReviewOrder(false);
      navigate("/doctor/section-editor");
    } catch (err) {
      console.error("Error updating section order:", err);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center h-screen w-full justify-center">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-600">Error loading report: {error}</div>
      </div>
    );
  }

  if (!sections || sections.length === 0) {
    return (
      <div className="flex min-h-screen items-center justify-center h-full">
        <div className="text-gray-600">No report data available</div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-[1280px] mx-auto p-6">
          {/* Header */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-semibold text-gray-800">
                Psychological Assessment Report
              </h1>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => {
                    navigate("/doctor/cheatsheet");
                  }}
                  className="flex items-center gap-2 bg-white text-violet-600 border border-violet-600 px-6 py-2 rounded-lg hover:bg-violet-50"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14 2H2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V3C15 2.44772 14.5523 2 14 2Z"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M5 5H11"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M5 8H11"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M5 11H8"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span>Generate Cheatsheet</span>
                </button>
                <button
                  onClick={handleProceedReview}
                  className="flex items-center gap-2 bg-violet-600 text-white px-6 py-2 rounded-lg hover:bg-violet-700"
                >
                  <FileIcon />
                  <span>Proceed to Review</span>
                </button>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <VerifiedIcon />
                <span>Verified by Psychometrist</span>
              </div>
              <div className="flex items-center gap-2">
                <TimeIcon />
                <span>Last modified: May 7, 2025</span>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white rounded-lg">
            {/* Patient Info */}
            <div className="bg-violet-50 p-4 m-4 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Patient Name</p>
                  <p className="font-medium text-gray-800">
                    {patientInfo.name}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Age</p>
                  <p className="font-medium text-gray-800">{patientInfo.age}</p>
                </div>
              </div>
            </div>

            {/* Editor */}
            <div className="flex justify-center items-center w-full min-h-[900px] overflow-y-auto rounded-lg bg-white">
              <div className="min-h-[811px]">
                {editor?.isEditable && <Toolbar editor={editor} />}
                <EditorContent editor={editor} />
              </div>
            </div>
          </div>
        </div>
      </div>

      <ReviewOrderModal
        open={showReviewOrder}
        onClose={handleCloseReviewOrder}
        onBeginReview={handleBeginReview}
        sections={
          sections?.map((section) => ({
            id: section.id.toString(),
            title: section.title,
            order: section.order || 0,
          })) || []
        }
      />
    </>
  );
};

export default DoctorAssessmentReport;
