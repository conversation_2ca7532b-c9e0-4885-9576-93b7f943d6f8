import{j as n}from"./@react-google-maps/api-5b2d83cc.js";import{r as k}from"./vendor-489b60f1.js";import{g as W,C as A,c as B,L as F,S as G}from"./index-95f0e460.js";import{_ as J}from"./react-toggle-88721710.js";import{M as K}from"./index-52d51cfb.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@uppy/dashboard-6cf0145e.js";const P=new G,gt=({type:e="text",page:h,cols:w="30",rows:b="50",name:l,label:j,errors:t=null,register:a=null,className:s,placeholder:x,options:M=[],mapping:N=null,disabled:o=!1,value:u=null,checked:T=null,onChange:d,loading:E=!1,required:m=!1,labelClassName:S="",customField:g=!1,showErrorMessage:q=!0})=>{var y,z,I,C,V,v,D,L;const p=k.useId(),[_,H]=k.useState(null),[f,O]=k.useState({modal:null,showModal:!1}),$=(c,i)=>{O(R=>({...R,modal:c,showModal:i}))};return n.jsxs(n.Fragment,{children:[n.jsxs("div",{className:`relative grow ${h==="list"?"w-full pl-2 pr-2 md:w-1/2":""}`,children:[["radio","checkbox","color","toggle"].includes(e)?null:n.jsx(n.Fragment,{children:j&&n.jsxs("label",{className:`mb-2 block cursor-pointer text-[.875rem] font-bold ${S}`,htmlFor:p,children:[j,m&&n.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]})}),E?n.jsx(W,{count:1,counts:[2],className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-[.625rem] !bg-[#ebebeb] !p-0"}):e==="textarea"?n.jsx(n.Fragment,{children:n.jsx("textarea",{className:`focus:shadow-outline font-inter w-full appearance-none rounded border px-3 py-2 leading-tight text-black shadow focus:outline-none ${s} ${l&&t&&(t!=null&&t[l])&&((y=t==null?void 0:t[l])!=null&&y.message)?"!border-red-500":"border-gray-200"} ${o?"appearance-none bg-gray-200":""}`,disabled:o,id:p,cols:w,name:l,placeholder:x,rows:b,...u?{value:u}:null,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d}})}):["radio","checkbox","color","toggle"].includes(e)?n.jsxs("div",{className:"flex h-[1.875rem] items-center gap-2 pb-1 pt-3",children:[["toggle"].includes(e)?n.jsx(J,{className:`toggle_class ${s}`,disabled:o,icons:!1,...d?{onChange:d}:null,...[!0,!1].includes(u)?{checked:u}:null}):n.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:e,defaultValue:"",disabled:o,id:p,name:l,...u?{value:u}:null,checked:T,placeholder:x,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d},className:`focus:shadow-outline font-inter !h-4 !w-4 cursor-pointer appearance-none rounded border leading-tight text-primary shadow focus:outline-none focus:ring-0 ${s} ${l&&t&&(t!=null&&t[l])&&((z=t==null?void 0:t[l])!=null&&z.message)?"!border-red-500":"border-gray-200"} ${e==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""} ${o?"appearance-none bg-gray-200":""}`}),n.jsx("label",{className:`font-inter mb-2 block h-full cursor-pointer whitespace-nowrap text-[.9375rem] font-bold capitalize text-black ${S}`,htmlFor:p,children:j})]}):e==="dropdown"||e==="select"?n.jsxs("select",{type:e,defaultValue:"",id:p,name:l,disabled:o,placeholder:x,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d},className:`focus:shadow-outline font-inter h-[3rem] w-full appearance-none truncate rounded-[.625rem]  border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0  ${s} ${l&&t&&(t!=null&&t[l])&&((I=t==null?void 0:t[l])!=null&&I.message)?"!border-red-500":"border-gray-200"}  ${o?"appearance-none bg-gray-200":""}`,children:[n.jsx("option",{}),M.map((c,i)=>n.jsx("option",{value:c,children:c},i+1))]}):e==="mapping"?n.jsx(n.Fragment,{children:N?n.jsxs("select",{id:p,name:l,disabled:o,...u?{value:u}:null,placeholder:x,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d},className:`focus:shadow-outline font-inter h-[3rem] w-full truncate rounded-[.625rem]  border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0  ${s} ${l&&t&&(t!=null&&t[l])&&((C=t==null?void 0:t[l])!=null&&C.message)?"!border-red-500":"border-gray-200"} ${o?"appearance-none bg-gray-200":""}`,children:[n.jsx("option",{}),M.map((c,i)=>n.jsx("option",{value:c,children:N[c]},i+1))]}):"Please Pass the mapping e.g {key:value}"}):["number","decimal"].includes(e)?n.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:e,defaultValue:"",id:p,name:l,disabled:o,placeholder:x,...u?{value:u}:null,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d},step:"0.01",min:"0.00",onInput:c=>{let i=c.target.value;i.startsWith(".")&&(i=`0${i}`),/^\d+(\.\d{0,2})?$/.test(i)||(i=i.slice(0,-1)),c.target.value=i},className:`focus:shadow-outline font-inter h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0 ${s} ${l&&t&&(t!=null&&t[l])&&((V=t==null?void 0:t[l])!=null&&V.message)?"!border-red-500":"border-gray-200"} ${o?"appearance-none bg-gray-200":""}`}):["custom_date"].includes(e)?n.jsxs("div",{onClick:()=>{o||$("custom_date",!0)},className:"relative cursor-pointer",children:[n.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:e,defaultValue:"",id:p,name:l,disabled:!0,placeholder:x,...u?{value:u}:null,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d},...e==="number"?{step:"0.01"}:null,min:e==="number"?"0.00":void 0,className:`focus:shadow-outline bg-brown-main-bg h-[3rem] w-full appearance-none truncate rounded-sm border-[.125rem] border-[#1f1d1a]  text-center text-sm font-normal leading-tight text-[#1f1d1a] shadow focus:outline-none focus:ring-0 ${s} ${l&&t&&(t!=null&&t[l])&&((v=t==null?void 0:t[l])!=null&&v.message)?"!border-red-500":"border-gray-200"}`}),n.jsx(A,{className:"absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer"})]}):n.jsx("input",{autoComplete:"new-password","aria-autocomplete":"none",type:e,defaultValue:"",id:p,name:l,disabled:o,placeholder:x,...u?{value:u}:null,...a?a(l,{...m&&g?{required:!0}:null}):{onChange:d},...e==="number"?{step:"0.01"}:null,min:e==="number"?"0.00":void 0,className:`focus:shadow-outline font-inter h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 leading-tight text-black shadow focus:outline-none focus:ring-0 ${s} ${l&&t&&(t!=null&&t[l])&&((D=t==null?void 0:t[l])!=null&&D.message)?"!border-red-500":"border-gray-200"} ${o?"appearance-none bg-gray-200":""}`}),q&&l&&t&&(t==null?void 0:t[l])&&n.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:P.Capitalize((L=t==null?void 0:t[l])==null?void 0:L.message,{separator:" "})})]}),f.showModal?n.jsx(B,{modalHeader:!0,title:f.modal?Q(f==null?void 0:f.modal):"",isOpen:f.showModal,modalCloseClick:()=>$(null,!1),classes:{modalDialog:"!px-0 !rounded-[.125rem] h-fit min-h-fit max-h-fit !w-full !max-w-full !min-w-full ",modalContent:"!z-10 !mt-0 overflow-hidden !pt-0",modal:"h-full"},children:f.showModal&&["custom_date"].includes(f.modal)?n.jsx(F,{children:n.jsx(K,{selectedDay:_,setSelectedDay:H,onSave:()=>{d({target:{value:_}}),$(null,!1)}})}):null}):null]})};function Q(e){const h=e.split("_"),w=h.includes("date"),b=h.includes("time");return w&&b?"Pick date & time":w?"Pick date":b?"Select Time":P.Capitalize(e,{separator:"space"})}export{gt as default};
