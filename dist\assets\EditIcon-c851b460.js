import{j as C}from"./@react-google-maps/api-5b2d83cc.js";import"./vendor-489b60f1.js";const r=({className:s="",fill:t="#A8A8A8",onClick:L=()=>{}})=>C.jsxs("svg",{className:`${s}`,onClick:L,xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[C.jsx("path",{d:"M13.1355 2.43886C13.705 1.86934 14.6284 1.86934 15.1979 2.43885L16.7277 3.96868C17.2972 4.53819 17.2972 5.46156 16.7277 6.03107L5.68604 17.0727C5.41255 17.3462 5.04162 17.4999 4.65484 17.4999H2.29169C1.94651 17.4999 1.66669 17.2201 1.66669 16.8749V14.5117C1.66669 14.1249 1.82033 13.754 2.09382 13.4805L13.1355 2.43886Z",fill:t}),C.jsx("path",{d:"M17.9086 14.527C17.6474 14.3015 17.2529 14.3302 17.0272 14.5912L17.0218 14.5974C17.016 14.6038 17.0063 14.6145 16.9929 14.6289C16.966 14.6577 16.9246 14.7009 16.8705 14.7536C16.7616 14.8597 16.6051 15.0011 16.4158 15.1414C16.0191 15.4354 15.5626 15.662 15.1384 15.662C14.6955 15.662 14.27 15.4518 13.6815 15.1394L13.6246 15.1091C13.1021 14.8313 12.4244 14.4709 11.6555 14.4709C10.1719 14.4709 9.30596 15.1795 8.50384 16.0293C8.26691 16.2804 8.27834 16.6759 8.52936 16.9129C8.78038 17.1498 9.17595 17.1384 9.41288 16.8873C10.1272 16.1306 10.6846 15.7209 11.6555 15.7209C12.0857 15.7209 12.5031 15.9291 13.0954 16.2435L13.1398 16.267C13.6676 16.5475 14.3535 16.912 15.1384 16.912C15.9642 16.912 16.6885 16.4951 17.1601 16.1456C17.405 15.9642 17.6045 15.7838 17.7431 15.6486C17.8128 15.5807 17.868 15.5233 17.9068 15.4817C17.9262 15.4609 17.9416 15.444 17.9527 15.4316L17.9661 15.4164L17.9704 15.4116L17.9719 15.4098L17.973 15.4086C17.973 15.4086 17.973 15.4086 17.5 15L17.973 15.4086C18.1986 15.1473 18.1698 14.7527 17.9086 14.527Z",fill:t})]});export{r as default};
