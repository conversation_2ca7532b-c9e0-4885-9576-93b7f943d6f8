import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{d as q,j as V,r as h}from"./vendor-489b60f1.js";import{a as z}from"./index-95f0e460.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Z=["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"],R=l=>{if(!l.trim())return"Patient ID is required";if(l.trim().length<3)return"Patient ID must be at least 3 characters long";if(!/^[a-zA-Z0-9-_]+$/.test(l.trim()))return"Patient ID can only contain letters, numbers, hyphens, and underscores"},X=l=>{if(!l.trim())return"Patient name is required";if(l.trim().length<2)return"Patient name must be at least 2 characters long";if(!/^[a-zA-Z\s'-]+$/.test(l.trim()))return"Patient name can only contain letters, spaces, hyphens, and apostrophes"},Y=l=>{if(!l)return"Date of birth is required";const f=new Date(l),c=new Date,n=new Date("1900-01-01"),j=new Date(c.getFullYear()-1,c.getMonth(),c.getDate());if(f<n)return"Date of birth cannot be before 1900";if(f>j)return"Patient must be at least 1 year old";if(f>c)return"Date of birth cannot be in the future"},me=()=>{const l=q(),[f]=V(),{sdk:c}=z();f.get("projectId");const[n,j]=h.useState({patientId:"",patientName:"",dob:""}),[i,w]=h.useState({}),[d,H]=h.useState(new Set),[o,b]=h.useState([]),[x,E]=h.useState(!1),[M,u]=h.useState(null),[A,N]=h.useState(!1),[v,D]=h.useState(0),U=(t,a)=>{switch(t){case"patientId":return R(a);case"patientName":return X(a);case"dob":return Y(a);default:return}},I=t=>{const{name:a,value:r}=t.target;j(s=>({...s,[a]:r})),i[a]&&w(s=>({...s,[a]:void 0}))},L=t=>{const{name:a,value:r}=t.target;H(p=>new Set(p).add(a));const s=U(a,r);w(p=>({...p,[a]:s}))},$=()=>{const t={};return Object.keys(n).forEach(a=>{const r=a,s=U(r,n[r]);s&&(t[r]=s)}),w(t),Object.keys(t).length===0},P=t=>{t.preventDefault(),t.stopPropagation(),t.type==="dragenter"||t.type==="dragover"?N(!0):t.type==="dragleave"&&N(!1)},_=async t=>{t.preventDefault(),t.stopPropagation(),N(!1);const a=Array.from(t.dataTransfer.files);await B(a)},T=async t=>{if(t.target.files){const a=Array.from(t.target.files);await B(a)}},B=async t=>{try{u(null);const a=t.map(r=>{if(!Z.includes(r.type))throw new Error(`Unsupported file type: ${r.type}`);return{id:Math.random().toString(36).substr(2,9),file:r,status:"pending"}});b(r=>[...r,...a])}catch(a){u(a instanceof Error?a.message:"Failed to process files")}},k=t=>{b(a=>a.filter(r=>r.id!==t))},O=async()=>{if(!$()){u("Please fix the validation errors before proceeding");return}if(o.length===0){u("Please upload at least one file");return}try{E(!0),u(null),D(0);let t=[];for(let s=0;s<o.length;s++){const p=o[s];b(m=>m.map((g,y)=>y===s?{...g,status:"pending"}:g));try{const m=await(c==null?void 0:c.uploadDocument([p.file],n.patientId,n.patientName,n.dob));b(y=>y.map((C,S)=>S===s?{...C,status:"success"}:C)),t.push({success:!0});const g=(s+1)/o.length*100;D(g)}catch(m){b(y=>y.map((C,S)=>S===s?{...C,status:"error",error:m instanceof Error?m.message:"Upload failed"}:C)),t.push({success:!1,error:m instanceof Error?m.message:"Upload failed"}),console.error(`Failed to upload ${p.file.name}:`,m);const g=(s+1)/o.length*100;D(g)}}localStorage.setItem("patientId",n.patientId),localStorage.setItem("patientName",n.patientName);const a=await c.createProject({name:"case report "+n.patientName||"",diagnosis:"",user_id:localStorage.getItem("user")||"",status:0,patient_id:n.patientId,patient_name:n.patientName||""});if(!a||a.error)throw new Error((a==null?void 0:a.message)||"Failed to create project");localStorage.setItem("projectId",a.project.id);const r=t.every(s=>s.success);if(console.log("Upload results:",t),console.log("All files successful:",r),r)l("/member/analysis");else{const s=t.filter(p=>!p.success).length;u(`${s} file(s) failed to upload. Please check the file status and try again.`)}}catch(t){u(t instanceof Error?t.message:"Failed to upload files")}finally{E(!1)}},F=t=>{const a="w-full rounded-md border bg-white text-gray-900 px-3 py-2 focus:outline-none focus:ring-1";return i[t]&&d.has(t)?`${a} border-red-300 focus:ring-red-500 focus:border-red-500`:`${a} border-gray-300 focus:ring-gray-500 focus:border-gray-500`};return e.jsx("main",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-8 px-2",children:e.jsxs("div",{className:"w-full max-w-3xl bg-white rounded-xl shadow p-8 flex flex-col gap-y-6",children:[e.jsxs("form",{className:"flex flex-col gap-y-4",autoComplete:"off","aria-label":"Upload Patient Documents",onSubmit:t=>t.preventDefault(),children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-800 mb-2",children:"Upload Patient Documents"}),e.jsxs("div",{className:"flex flex-col gap-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"patientId",className:"block text-sm text-gray-700 mb-1",children:["Patient ID ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{id:"patientId",name:"patientId",type:"text",required:!0,value:n.patientId,onChange:I,onBlur:L,placeholder:"Enter patient ID (e.g., PAT-001)",className:F("patientId"),"aria-label":"Patient ID","aria-invalid":!!(i.patientId&&d.has("patientId")),"aria-describedby":i.patientId&&d.has("patientId")?"patientId-error":void 0}),i.patientId&&d.has("patientId")&&e.jsx("p",{id:"patientId-error",className:"mt-1 text-sm text-red-600",children:i.patientId})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"patientName",className:"block text-sm text-gray-700 mb-1",children:["Patient Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{id:"patientName",name:"patientName",type:"text",required:!0,value:n.patientName,onChange:I,onBlur:L,placeholder:"Enter patient name",className:F("patientName"),"aria-label":"Patient Name","aria-invalid":!!(i.patientName&&d.has("patientName")),"aria-describedby":i.patientName&&d.has("patientName")?"patientName-error":void 0}),i.patientName&&d.has("patientName")&&e.jsx("p",{id:"patientName-error",className:"mt-1 text-sm text-red-600",children:i.patientName})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"dob",className:"block text-sm text-gray-700 mb-1",children:["Date of Birth ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"relative",children:e.jsx("input",{id:"dob",name:"dob",type:"date",required:!0,value:n.dob,onChange:I,onBlur:L,max:new Date().toISOString().split("T")[0],className:F("dob"),"aria-label":"Date of Birth","aria-invalid":!!(i.dob&&d.has("dob")),"aria-describedby":i.dob&&d.has("dob")?"dob-error":void 0})}),i.dob&&d.has("dob")&&e.jsx("p",{id:"dob-error",className:"mt-1 text-sm text-red-600",children:i.dob})]})]})]}),e.jsxs("section",{className:`flex flex-col items-center justify-center gap-2 w-full rounded-lg border-2 border-dashed ${A?"border-gray-400 bg-gray-50":"border-gray-300"} bg-white py-8 transition-colors`,"aria-label":"File upload area",onDragEnter:P,onDragLeave:P,onDragOver:P,onDrop:_,children:[e.jsxs("svg",{width:46,height:36,viewBox:"0 0 46 36",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[e.jsx("g",{clipPath:"url(#clip0_1_146)",children:e.jsx("path",{d:"M10.625 33.75C5.03516 33.75 0.5 29.2148 0.5 23.625C0.5 19.2094 3.32656 15.4547 7.26406 14.0695C7.25703 13.8797 7.25 13.6898 7.25 13.5C7.25 7.28437 12.2844 2.25 18.5 2.25C22.6695 2.25 26.3047 4.51406 28.2523 7.88906C29.3211 7.17188 30.6148 6.75 32 6.75C35.7266 6.75 38.75 9.77344 38.75 13.5C38.75 14.3578 38.5883 15.1734 38.3 15.9328C42.4062 16.7625 45.5 20.3977 45.5 24.75C45.5 29.7211 41.4711 33.75 36.5 33.75H10.625ZM16.1797 18.4922C15.5188 19.1531 15.5188 20.2219 16.1797 20.8758C16.8406 21.5297 17.9094 21.5367 18.5633 20.8758L21.3055 18.1336V27.5625C21.3055 28.4977 22.0578 29.25 22.993 29.25C23.9281 29.25 24.6805 28.4977 24.6805 27.5625V18.1336L27.4227 20.8758C28.0836 21.5367 29.1523 21.5367 29.8062 20.8758C30.4602 20.2148 30.4672 19.1461 29.8062 18.4922L24.1812 12.8672C23.5203 12.2063 22.4516 12.2063 21.7977 12.8672L16.1727 18.4922H16.1797Z",fill:"#9CA3AF"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_146",children:e.jsx("path",{d:"M0.5 0H45.5V36H0.5V0Z",fill:"white"})})})]}),e.jsx("div",{className:"text-lg text-gray-700 font-medium",children:x?"Uploading...":"Drag and drop files here"}),e.jsx("div",{className:"text-xs text-gray-500 mb-2",children:"Supported formats: PDF, DOC, DOCX, TXT"}),e.jsxs("label",{className:"px-4 py-2 bg-gray-800 text-white rounded-md cursor-pointer hover:bg-gray-700 transition-colors",children:["Browse Files",e.jsx("input",{type:"file",multiple:!0,accept:Z.join(","),onChange:T,className:"hidden"})]})]}),M&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative",children:M}),x&&e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700",children:["Uploading files... ",Math.round(v),"%"]}),e.jsxs("span",{className:"text-sm text-gray-500",children:[o.filter(t=>t.status==="success").length," of ",o.length," completed"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${v}%`}})})]}),e.jsxs("section",{className:"w-full max-h-[500px] overflow-y-auto overflow-x-hidden","aria-label":"Uploaded files",children:[e.jsxs("table",{className:"w-full text-left border-collapse",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-50",children:[e.jsx("th",{className:"py-3 pl-6 text-gray-500 text-xs font-bold",children:"Filename"}),e.jsx("th",{className:"py-3 pl-6 text-gray-500 text-xs font-bold",children:"Type"}),e.jsx("th",{className:"py-3 pl-6 text-gray-500 text-xs font-bold",children:"Status"}),e.jsx("th",{className:"py-3 pl-6 text-gray-500 text-xs font-bold",children:"Actions"})]})}),e.jsx("tbody",{children:o.map((t,a)=>e.jsxs("tr",{className:a===o.length-1?"":"border-b border-gray-200",children:[e.jsx("td",{className:"py-3 pl-6 text-gray-900 text-sm",children:t.file.name}),e.jsx("td",{className:"py-3 pl-6 text-gray-500 text-sm",children:t.file.type.split("/")[1].toUpperCase()}),e.jsxs("td",{className:"py-3 pl-6",children:[e.jsx("span",{className:`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs ${t.status==="success"?"bg-green-100 text-green-800":t.status==="error"?"bg-red-100 text-red-800":x&&t.status==="pending"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:t.status==="success"?e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:e.jsx("path",{d:"M10.9516 2.47034C11.2446 2.76331 11.2446 3.23909 10.9516 3.53206L4.95161 9.53206C4.65864 9.82502 4.18286 9.82502 3.88989 9.53206L0.889893 6.53206C0.596924 6.23909 0.596924 5.76331 0.889893 5.47034C1.18286 5.17737 1.65864 5.17737 1.95161 5.47034L4.42192 7.93831L9.89224 2.47034C10.1852 2.17737 10.661 2.17737 10.954 2.47034H10.9516Z",fill:"currentColor"})}),"Success"]}):t.status==="error"?e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:10,height:12,viewBox:"0 0 10 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:e.jsx("path",{d:"M8.70161 3.52974C8.99458 3.23677 8.99458 2.76099 8.70161 2.46802C8.40864 2.17505 7.93286 2.17505 7.63989 2.46802L5.17192 4.93833L2.70161 2.47036C2.40864 2.17739 1.93286 2.17739 1.63989 2.47036C1.34692 2.76333 1.34692 3.23911 1.63989 3.53208L4.1102 6.00005L1.64224 8.47036C1.34927 8.76333 1.34927 9.23911 1.64224 9.53208C1.93521 9.82505 2.41099 9.82505 2.70396 9.53208L5.17192 7.06177L7.64224 9.52974C7.93521 9.8227 8.41099 9.8227 8.70396 9.52974C8.99692 9.23677 8.99692 8.76099 8.70396 8.46802L6.23364 6.00005L8.70161 3.52974Z",fill:"currentColor"})}),"Error"]}):x&&t.status==="pending"?e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",className:"animate-spin",children:e.jsx("path",{d:"M6 1.5C3.51472 1.5 1.5 3.51472 1.5 6C1.5 8.48528 3.51472 10.5 6 10.5C8.48528 10.5 10.5 8.48528 10.5 6C10.5 3.51472 8.48528 1.5 6 1.5ZM0 6C0 2.68629 2.68629 0 6 0C9.31371 0 12 2.68629 12 6C12 9.31371 9.31371 12 6 12C2.68629 12 0 9.31371 0 6ZM6 2.5C6.82843 2.5 7.5 3.17157 7.5 4C7.5 4.82843 6.82843 5.5 6 5.5C5.17157 5.5 4.5 4.82843 4.5 4C4.5 3.17157 5.17157 2.5 6 2.5Z",fill:"currentColor"})}),"Uploading..."]}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:10,height:12,viewBox:"0 0 10 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:e.jsx("path",{d:"M8.70161 3.52974C8.99458 3.23677 8.99458 2.76099 8.70161 2.46802C8.40864 2.17505 7.93286 2.17505 7.63989 2.46802L5.17192 4.93833L2.70161 2.47036C2.40864 2.17739 1.93286 2.17739 1.63989 2.47036C1.34692 2.76333 1.34692 3.23911 1.63989 3.53208L4.1102 6.00005L1.64224 8.47036C1.34927 8.76333 1.34927 9.23911 1.64224 9.53208C1.93521 9.82505 2.41099 9.82505 2.70396 9.53208L5.17192 7.06177L7.64224 9.52974C7.93521 9.8227 8.41099 9.8227 8.70396 9.52974C8.99692 9.23677 8.99692 8.76099 8.70396 8.46802L6.23364 6.00005L8.70161 3.52974Z",fill:"currentColor"})}),"Pending"]})}),t.error&&e.jsx("div",{className:"mt-1 text-xs text-red-600",children:t.error})]}),e.jsx("td",{className:"py-3 pl-6",children:e.jsx("div",{className:"flex gap-2",children:e.jsx("button",{type:"button",className:"p-1 hover:bg-gray-100 rounded",onClick:()=>k(t.id),"aria-label":`Delete ${t.file.name}`,children:e.jsx("svg",{width:13,height:14,viewBox:"0 0 13 14",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:e.jsx("path",{d:"M4.00937 0.483984L3.8125 0.875H1.1875C0.703516 0.875 0.3125 1.26602 0.3125 1.75C0.3125 2.23398 0.703516 2.625 1.1875 2.625H11.6875C12.1715 2.625 12.5625 2.23398 12.5625 1.75C12.5625 1.26602 12.1715 0.875 11.6875 0.875H9.0625L8.86562 0.483984C8.71797 0.185938 8.41445 0 8.08359 0H4.79141C4.46055 0 4.15703 0.185938 4.00937 0.483984ZM11.6875 3.5H1.1875L1.76719 12.7695C1.81094 13.4613 2.38516 14 3.07695 14H9.79805C10.4898 14 11.0641 13.4613 11.1078 12.7695L11.6875 3.5Z",fill:"#4B5563"})})})})})]},t.id))})]}),o.length===0&&e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No files uploaded yet"})]}),e.jsxs("div",{className:"flex justify-end gap-4 mt-4",children:[e.jsx("button",{type:"button",className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50",onClick:()=>l("/projects"),children:"Cancel"}),e.jsx("button",{type:"button",className:"flex items-center gap-2 px-6 py-2 rounded-md bg-gray-800 text-white hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",onClick:O,disabled:x||o.length===0,children:x?e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"animate-spin",children:e.jsx("path",{d:"M7 1.5C3.96243 1.5 1.5 3.96243 1.5 7C1.5 10.0376 3.96243 12.5 7 12.5C10.0376 12.5 12.5 10.0376 12.5 7C12.5 3.96243 10.0376 1.5 7 1.5ZM0 7C0 3.13401 3.13401 0 7 0C10.866 0 14 3.13401 14 7C14 10.866 10.866 14 7 14C3.13401 14 0 10.866 0 7ZM7 3.5C7.82843 3.5 8.5 4.17157 8.5 5C8.5 5.82843 7.82843 6.5 7 6.5C6.17157 6.5 5.5 5.82843 5.5 5C5.5 4.17157 6.17157 3.5 7 3.5Z",fill:"white"})}),"Uploading... (",Math.round(v),"%)"]}):e.jsxs(e.Fragment,{children:["Upload & Proceed",e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"16",viewBox:"0 0 14 16",fill:"none",children:e.jsx("path",{d:"M13.7063 8.70627C14.0969 8.31565 14.0969 7.68127 13.7063 7.29065L8.70625 2.29065C8.31563 1.90002 7.68125 1.90002 7.29063 2.29065C6.9 2.68127 6.9 3.31565 7.29063 3.70627L10.5875 7.00002H1C0.446875 7.00002 0 7.4469 0 8.00002C0 8.55315 0.446875 9.00002 1 9.00002H10.5844L7.29375 12.2938C6.90312 12.6844 6.90312 13.3188 7.29375 13.7094C7.68437 14.1 8.31875 14.1 8.70938 13.7094L13.7094 8.7094L13.7063 8.70627Z",fill:"white"})})]})})]})]})})};export{me as default};
