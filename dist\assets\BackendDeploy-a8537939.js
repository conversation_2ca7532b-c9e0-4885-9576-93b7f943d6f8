import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as m}from"./vendor-489b60f1.js";import{a as w,u as h,T as b}from"./index-95f0e460.js";import{c as C}from"./@tanstack/react-query-dc4b6186.js";import{I as p}from"./index-ec6e151a.js";import{F as k,D as x,c as N,d as S}from"./index.esm-05b2469a.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./react-icons-fe0a0adf.js";const v=()=>{const{sdk:t}=w(),{showToast:r,tokenExpireError:d}=h();return C({mutationFn:async s=>await t.request(s),onSuccess:s=>{console.log(s),r((s==null?void 0:s.message)||"Success",5e3,b.SUCCESS)},onError:s=>{r(s.message,5e3,b.ERROR),d(s.message),console.error(s)}})},E=({project:t})=>{const{globalState:{settings:r,nodes:d,roles:l,routes:s}}=h(),{mutateAsync:u,isPending:B}=v(),j=m.useMemo(()=>({settings:r,nodes:d,roles:l,routes:s}),[r,d,l,s]),[n,D]=m.useState({}),[i,c]=m.useState({setup:!1,create_repo:!1,create_jenkins_job:!1}),[g,T]=m.useState([]),[f,F]=m.useState(!1),y=async()=>{try{c(a=>({...a,setup:!0}));const o=await u({endpoint:"/v1/api/{{project}}/custom/build/backend",method:"POST",body:{project:t==null?void 0:t.slug,config:JSON.stringify(j)}});if(o.error)throw new Error(o.message);c(a=>({...a,setup:!1}))}catch{c(a=>({...a,setup:!1}))}},_=async()=>{try{c(a=>({...a,create_jenkins_job:!0}));const o=await u({endpoint:"/v1/api/{{project}}/custom/job/backend",method:"POST",body:{project:t==null?void 0:t.slug,branch:"master"}});if(o.error)throw new Error(o.message);c(a=>({...a,create_jenkins_job:!1}))}catch{c(a=>({...a,create_jenkins_job:!1}))}};return{deployment:n,loading:i,backendBranches:g,BRANCHES:[],DEPLOY_ITEMS:[{id:1,name:"Deployment",actionBtn:()=>e.jsx(p,{className:"flex items-center rounded-md px-3 py-2 shadow-sm !cursor-default border border-[#C6C6C6] !shadow-green-500 !bg-green-500",disabled:!0,children:"Initialized"}),icon:()=>e.jsx(k,{className:"h-6 w-6 text-green-500"})},{id:3,name:"Set Up Backend",actionBtn:()=>e.jsxs("div",{className:"flex items-center gap-4",children:[n.has_be_repo?e.jsx(x,{text:`http://23.29.118.76:3000/mkdlabs/${t.slug}_backend.git`}):"",e.jsx(p,{className:`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${n.has_be_repo?"!cursor-not-allowed border border-[#C6C6C6] !bg-green-500":i.setup?"!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700":"bg-primaryBlue text-white"} `,loading:i.setup,disabled:n.has_be_repo||i.setup,onClick:o=>y(),children:n.has_be_repo?"Created":"Setup"})]}),icon:()=>e.jsx(N,{className:"h-6 w-6 text-purple-500"})},{id:8,name:"Back-end Jenkins job",actionBtn:()=>e.jsxs("div",{className:"flex items-center gap-4",children:[n.has_be_job?e.jsx(x,{text:`http://23.29.118.76:8080/job/${t.slug}_backend/`}):"",e.jsx(p,{className:`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${n.has_be_job?"!cursor-not-allowed border border-[#C6C6C6] !bg-green-500":i.create_jenkins_job?"!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700":"bg-primaryBlue text-white"} `,loading:i.create_jenkins_job,disabled:n.has_be_job||i.create_jenkins_job,onClick:o=>_(),children:n!=null&&n.has_be_job?"Created":"Run"})]}),icon:()=>e.jsx(S,{className:"h-6 w-6 text-green-900"})}],branchesLoading:f}},se=({project:t})=>{const{BRANCHES:r,DEPLOY_ITEMS:d,branchesLoading:l}=E({project:t});return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"relative px-10 pb-6 pt-3 flex w-full rounded-b-md border-t-0  bg-white py-2 text-sm text-gray-700 ",children:e.jsxs("ol",{className:"list-decimal",children:[e.jsx("li",{children:"Set Up Backend"}),e.jsx("li",{children:"Create Backend Jenkins job"})]})}),e.jsx("div",{className:"flex justify-between border-t border-[#E0E0E0] bg-[#f9f9f9] px-8",children:e.jsx("div",{className:"mb-4 mt-6 w-full rounded-md border border-[#E0E0E0] bg-white px-5 py-3",children:e.jsxs("div",{className:"divide-y",children:[d.map(s=>e.jsxs("div",{className:"flex items-center justify-between py-6 text-sm text-[#393939]",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{children:s.icon()}),e.jsx("span",{children:s.name})]}),e.jsx("div",{children:s.actionBtn()})]},s.id)),l?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:" rounded-md border-b-0 border-r-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ",children:e.jsx("div",{className:"my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"})}),e.jsx("div",{className:" rounded-md border-b-0 border-l-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ",children:e.jsx("div",{className:"my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"})})]}):(r==null?void 0:r.length)>0&&(r==null?void 0:r.map(s=>e.jsxs("div",{className:"flex items-center justify-between py-6 text-sm text-[#393939]",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{children:s.icon()}),e.jsx("span",{children:s.name})]}),e.jsx("div",{children:s.actionBtn()})]},s.id)))]})})})]})};export{se as default};
