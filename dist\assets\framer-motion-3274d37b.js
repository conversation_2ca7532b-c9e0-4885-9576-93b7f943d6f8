import{r as b,R as as}from"./vendor-489b60f1.js";const ls=b.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Zt=b.createContext({}),Le=b.createContext(null),Qt=typeof document<"u",Li=Qt?b.useLayoutEffect:b.useEffect,cs=b.createContext({strict:!1}),Re=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Ri="framerAppearId",us="data-"+Re(Ri);function Bi(t,e,n,s){const{visualElement:i}=b.useContext(Zt),r=b.useContext(cs),o=b.useContext(Le),a=b.useContext(ls).reducedMotion,l=b.useRef();s=s||r.renderer,!l.current&&s&&(l.current=s(t,{visualState:e,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;b.useInsertionEffect(()=>{c&&c.update(n,o)});const u=b.useRef(!!(n[us]&&!window.HandoffComplete));return Li(()=>{c&&(c.render(),u.current&&c.animationState&&c.animationState.animateChanges())}),b.useEffect(()=>{c&&(c.updateFeatures(),!u.current&&c.animationState&&c.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),c}function dt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Fi(t,e,n){return b.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):dt(n)&&(n.current=s))},[e])}function Mt(t){return typeof t=="string"||Array.isArray(t)}function Jt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const Be=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Fe=["initial",...Be];function te(t){return Jt(t.animate)||Fe.some(e=>Mt(t[e]))}function hs(t){return!!(te(t)||t.variants)}function Ei(t,e){if(te(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Mt(n)?n:void 0,animate:Mt(s)?s:void 0}}return t.inherit!==!1?e:{}}function ki(t){const{initial:e,animate:n}=Ei(t,b.useContext(Zt));return b.useMemo(()=>({initial:e,animate:n}),[nn(e),nn(n)])}function nn(t){return Array.isArray(t)?t.join(" "):t}const sn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Lt={};for(const t in sn)Lt[t]={isEnabled:e=>sn[t].some(n=>!!e[n])};function ji(t){for(const e in t)Lt[e]={...Lt[e],...t[e]}}const fs=b.createContext({}),ds=b.createContext({}),Oi=Symbol.for("motionComponentSymbol");function Ii({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&ji(t);function r(a,l){let c;const u={...b.useContext(ls),...a,layoutId:Ui(a)},{isStatic:h}=u,f=ki(a),d=s(a,h);if(!h&&Qt){f.visualElement=Bi(i,d,u,e);const m=b.useContext(ds),p=b.useContext(cs).strict;f.visualElement&&(c=f.visualElement.loadFeatures(u,p,t,m))}return b.createElement(Zt.Provider,{value:f},c&&f.visualElement?b.createElement(c,{visualElement:f.visualElement,...u}):null,n(i,a,Fi(d,f.visualElement,l),d,h,f.visualElement))}const o=b.forwardRef(r);return o[Oi]=i,o}function Ui({layoutId:t}){const e=b.useContext(fs).id;return e&&t!==void 0?e+"-"+t:t}function Ni(t){function e(s,i={}){return Ii(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const Wi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ee(t){return typeof t!="string"||t.includes("-")?!1:!!(Wi.indexOf(t)>-1||/[A-Z]/.test(t))}const Ht={};function Gi(t){Object.assign(Ht,t)}const Bt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],lt=new Set(Bt);function ms(t,{layout:e,layoutId:n}){return lt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Ht[t]||t==="opacity")}const O=t=>!!(t&&t.getVelocity),Hi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},$i=Bt.length;function zi(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},s,i){let r="";for(let o=0;o<$i;o++){const a=Bt[o];if(t[a]!==void 0){const l=Hi[a]||a;r+=`${l}(${t[a]}) `}}return e&&!t.z&&(r+="translateZ(0)"),r=r.trim(),i?r=i(t,s?"":r):n&&s&&(r="none"),r}const ps=t=>e=>typeof e=="string"&&e.startsWith(t),gs=ps("--"),ye=ps("var(--"),Ki=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,_i=(t,e)=>e&&typeof t=="number"?e.transform(t):t,tt=(t,e,n)=>Math.min(Math.max(n,t),e),ct={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},At={...ct,transform:t=>tt(0,1,t)},Ot={...ct,default:1},Ct=t=>Math.round(t*1e5)/1e5,ee=/(-)?([\d]*\.?[\d])+/g,ys=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Xi=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Ft(t){return typeof t=="string"}const Et=t=>({test:e=>Ft(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),q=Et("deg"),G=Et("%"),P=Et("px"),Yi=Et("vh"),qi=Et("vw"),on={...G,parse:t=>G.parse(t)/100,transform:t=>G.transform(t*100)},rn={...ct,transform:Math.round},vs={borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,size:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,rotate:q,rotateX:q,rotateY:q,rotateZ:q,scale:Ot,scaleX:Ot,scaleY:Ot,scaleZ:Ot,skew:q,skewX:q,skewY:q,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:At,originX:on,originY:on,originZ:P,zIndex:rn,fillOpacity:At,strokeOpacity:At,numOctaves:rn};function ke(t,e,n,s){const{style:i,vars:r,transform:o,transformOrigin:a}=t;let l=!1,c=!1,u=!0;for(const h in e){const f=e[h];if(gs(h)){r[h]=f;continue}const d=vs[h],m=_i(f,d);if(lt.has(h)){if(l=!0,o[h]=m,!u)continue;f!==(d.default||0)&&(u=!1)}else h.startsWith("origin")?(c=!0,a[h]=m):i[h]=m}if(e.transform||(l||s?i.transform=zi(t.transform,n,u,s):i.transform&&(i.transform="none")),c){const{originX:h="50%",originY:f="50%",originZ:d=0}=a;i.transformOrigin=`${h} ${f} ${d}`}}const je=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function xs(t,e,n){for(const s in e)!O(e[s])&&!ms(s,n)&&(t[s]=e[s])}function Zi({transformTemplate:t},e,n){return b.useMemo(()=>{const s=je();return ke(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function Qi(t,e,n){const s=t.style||{},i={};return xs(i,s,t),Object.assign(i,Zi(t,e,n)),t.transformValues?t.transformValues(i):i}function Ji(t,e,n){const s={},i=Qi(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=i,s}const to=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function $t(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||to.has(t)}let Ps=t=>!$t(t);function eo(t){t&&(Ps=e=>e.startsWith("on")?!$t(e):t(e))}try{eo(require("@emotion/is-prop-valid").default)}catch{}function no(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Ps(i)||n===!0&&$t(i)||!e&&!$t(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function an(t,e,n){return typeof t=="string"?t:P.transform(e+n*t)}function so(t,e,n){const s=an(e,t.x,t.width),i=an(n,t.y,t.height);return`${s} ${i}`}const io={offset:"stroke-dashoffset",array:"stroke-dasharray"},oo={offset:"strokeDashoffset",array:"strokeDasharray"};function ro(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?io:oo;t[r.offset]=P.transform(-s);const o=P.transform(e),a=P.transform(n);t[r.array]=`${o} ${a}`}function Oe(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...c},u,h,f){if(ke(t,c,u,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:p}=t;d.transform&&(p&&(m.transform=d.transform),delete d.transform),p&&(i!==void 0||r!==void 0||m.transform)&&(m.transformOrigin=so(p,i!==void 0?i:.5,r!==void 0?r:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),o!==void 0&&ro(d,o,a,l,!1)}const Ts=()=>({...je(),attrs:{}}),Ie=t=>typeof t=="string"&&t.toLowerCase()==="svg";function ao(t,e,n,s){const i=b.useMemo(()=>{const r=Ts();return Oe(r,e,{enableHardwareAcceleration:!1},Ie(s),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};xs(r,t.style,t),i.style={...r,...i.style}}return i}function lo(t=!1){return(n,s,i,{latestValues:r},o)=>{const l=(Ee(n)?ao:Ji)(s,r,o,n),u={...no(s,typeof n=="string",t),...l,ref:i},{children:h}=s,f=b.useMemo(()=>O(h)?h.get():h,[h]);return b.createElement(n,{...u,children:f})}}function bs(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const r in n)t.style.setProperty(r,n[r])}const Vs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ss(t,e,n,s){bs(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Vs.has(i)?i:Re(i),e.attrs[i])}function Ue(t,e){const{style:n}=t,s={};for(const i in n)(O(n[i])||e.style&&O(e.style[i])||ms(i,t))&&(s[i]=n[i]);return s}function As(t,e){const n=Ue(t,e);for(const s in t)if(O(t[s])||O(e[s])){const i=Bt.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;n[i]=t[s]}return n}function Ne(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}function co(t){const e=b.useRef(null);return e.current===null&&(e.current=t()),e.current}const zt=t=>Array.isArray(t),uo=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),ho=t=>zt(t)?t[t.length-1]||0:t;function Wt(t){const e=O(t)?t.get():t;return uo(e)?e.toValue():e}function fo({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,r){const o={latestValues:mo(s,i,r,t),renderState:e()};return n&&(o.mount=a=>n(s,a,o)),o}const Cs=t=>(e,n)=>{const s=b.useContext(Zt),i=b.useContext(Le),r=()=>fo(t,e,s,i);return n?r():co(r)};function mo(t,e,n,s){const i={},r=s(t,{});for(const f in r)i[f]=Wt(r[f]);let{initial:o,animate:a}=t;const l=te(t),c=hs(t);e&&c&&!l&&t.inherit!==!1&&(o===void 0&&(o=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||o===!1;const h=u?a:o;return h&&typeof h!="boolean"&&!Jt(h)&&(Array.isArray(h)?h:[h]).forEach(d=>{const m=Ne(t,d);if(!m)return;const{transitionEnd:p,transition:y,...T}=m;for(const v in T){let g=T[v];if(Array.isArray(g)){const x=u?g.length-1:0;g=g[x]}g!==null&&(i[v]=g)}for(const v in p)i[v]=p[v]}),i}const L=t=>t;class ln{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const n=this.order.indexOf(e);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}function po(t){let e=new ln,n=new ln,s=0,i=!1,r=!1;const o=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const h=u&&i,f=h?e:n;return c&&o.add(l),f.add(l)&&h&&i&&(s=e.order.length),l},cancel:l=>{n.remove(l),o.delete(l)},process:l=>{if(i){r=!0;return}if(i=!0,[e,n]=[n,e],n.clear(),s=e.order.length,s)for(let c=0;c<s;c++){const u=e.order[c];u(l),o.has(u)&&(a.schedule(u),t())}i=!1,r&&(r=!1,a.process(l))}};return a}const It=["prepare","read","update","preRender","render","postRender"],go=40;function yo(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=It.reduce((h,f)=>(h[f]=po(()=>n=!0),h),{}),o=h=>r[h].process(i),a=()=>{const h=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(h-i.timestamp,go),1),i.timestamp=h,i.isProcessing=!0,It.forEach(o),i.isProcessing=!1,n&&e&&(s=!1,t(a))},l=()=>{n=!0,s=!0,i.isProcessing||t(a)};return{schedule:It.reduce((h,f)=>{const d=r[f];return h[f]=(m,p=!1,y=!1)=>(n||l(),d.schedule(m,p,y)),h},{}),cancel:h=>It.forEach(f=>r[f].cancel(h)),state:i,steps:r}}const{schedule:A,cancel:X,state:E,steps:oe}=yo(typeof requestAnimationFrame<"u"?requestAnimationFrame:L,!0),vo={useVisualState:Cs({scrapeMotionValuesFromProps:As,createRenderState:Ts,onMount:(t,e,{renderState:n,latestValues:s})=>{A.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),A.render(()=>{Oe(n,s,{enableHardwareAcceleration:!1},Ie(e.tagName),t.transformTemplate),Ss(e,n)})}})},xo={useVisualState:Cs({scrapeMotionValuesFromProps:Ue,createRenderState:je})};function Po(t,{forwardMotionProps:e=!1},n,s){return{...Ee(t)?vo:xo,preloadedFeatures:n,useRender:lo(e),createVisualElement:s,Component:t}}function z(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const ws=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function ne(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const To=t=>e=>ws(e)&&t(e,ne(e));function K(t,e,n,s){return z(t,e,To(n),s)}const bo=(t,e)=>n=>e(t(n)),Q=(...t)=>t.reduce(bo);function Ds(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const cn=Ds("dragHorizontal"),un=Ds("dragVertical");function Ms(t){let e=!1;if(t==="y")e=un();else if(t==="x")e=cn();else{const n=cn(),s=un();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Ls(){const t=Ms(!0);return t?(t(),!1):!0}class nt{constructor(e){this.isMounted=!1,this.node=e}update(){}}function hn(t,e){const n="pointer"+(e?"enter":"leave"),s="onHover"+(e?"Start":"End"),i=(r,o)=>{if(r.pointerType==="touch"||Ls())return;const a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e),a[s]&&A.update(()=>a[s](r,o))};return K(t.current,n,i,{passive:!t.getProps()[s]})}class Vo extends nt{mount(){this.unmount=Q(hn(this.node,!0),hn(this.node,!1))}unmount(){}}class So extends nt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Q(z(this.node.current,"focus",()=>this.onFocus()),z(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Rs=(t,e)=>e?t===e?!0:Rs(t,e.parentElement):!1;function re(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,ne(n))}class Ao extends nt{constructor(){super(...arguments),this.removeStartListeners=L,this.removeEndListeners=L,this.removeAccessibleListeners=L,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),r=K(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:h}=this.node.getProps();A.update(()=>{!h&&!Rs(this.node.current,a.target)?u&&u(a,l):c&&c(a,l)})},{passive:!(s.onTap||s.onPointerUp)}),o=K(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=Q(r,o),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=r=>{if(r.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||re("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&A.update(()=>u(l,c))})};this.removeEndListeners(),this.removeEndListeners=z(this.node.current,"keyup",o),re("down",(a,l)=>{this.startPress(a,l)})},n=z(this.node.current,"keydown",e),s=()=>{this.isPressing&&re("cancel",(r,o)=>this.cancelPress(r,o))},i=z(this.node.current,"blur",s);this.removeAccessibleListeners=Q(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&A.update(()=>s(e,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Ls()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&A.update(()=>s(e,n))}mount(){const e=this.node.getProps(),n=K(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=z(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Q(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const ve=new WeakMap,ae=new WeakMap,Co=t=>{const e=ve.get(t.target);e&&e(t)},wo=t=>{t.forEach(Co)};function Do({root:t,...e}){const n=t||document;ae.has(n)||ae.set(n,{});const s=ae.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(wo,{root:t,...e})),s[i]}function Mo(t,e,n){const s=Do(e);return ve.set(t,n),s.observe(t),()=>{ve.delete(t),s.unobserve(t)}}const Lo={some:0,all:1};class Ro extends nt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,o={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Lo[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,r&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return Mo(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Bo(e,n))&&this.startObserver()}unmount(){}}function Bo({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Fo={inView:{Feature:Ro},tap:{Feature:Ao},focus:{Feature:So},hover:{Feature:Vo}};function Bs(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function Eo(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function ko(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function se(t,e,n){const s=t.getProps();return Ne(s,e,n!==void 0?n:s.custom,Eo(t),ko(t))}let jo=L,We=L;const J=t=>t*1e3,_=t=>t/1e3,Oo={current:!1},Fs=t=>Array.isArray(t)&&typeof t[0]=="number";function Es(t){return!!(!t||typeof t=="string"&&ks[t]||Fs(t)||Array.isArray(t)&&t.every(Es))}const St=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,ks={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:St([0,.65,.55,1]),circOut:St([.55,0,1,.45]),backIn:St([.31,.01,.66,-.59]),backOut:St([.33,1.53,.69,.99])};function js(t){if(t)return Fs(t)?St(t):Array.isArray(t)?t.map(js):ks[t]}function Io(t,e,n,{delay:s=0,duration:i,repeat:r=0,repeatType:o="loop",ease:a,times:l}={}){const c={[e]:n};l&&(c.offset=l);const u=js(a);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"})}function Uo(t,{repeat:e,repeatType:n="loop"}){const s=e&&n!=="loop"&&e%2===1?0:t.length-1;return t[s]}const Os=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,No=1e-7,Wo=12;function Go(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=Os(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>No&&++a<Wo);return o}function kt(t,e,n,s){if(t===e&&n===s)return L;const i=r=>Go(r,0,1,t,n);return r=>r===0||r===1?r:Os(i(r),e,s)}const Ho=kt(.42,0,1,1),$o=kt(0,0,.58,1),Is=kt(.42,0,.58,1),zo=t=>Array.isArray(t)&&typeof t[0]!="number",Us=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ns=t=>e=>1-t(1-e),Ge=t=>1-Math.sin(Math.acos(t)),Ws=Ns(Ge),Ko=Us(Ge),Gs=kt(.33,1.53,.69,.99),He=Ns(Gs),_o=Us(He),Xo=t=>(t*=2)<1?.5*He(t):.5*(2-Math.pow(2,-10*(t-1))),Yo={linear:L,easeIn:Ho,easeInOut:Is,easeOut:$o,circIn:Ge,circInOut:Ko,circOut:Ws,backIn:He,backInOut:_o,backOut:Gs,anticipate:Xo},fn=t=>{if(Array.isArray(t)){We(t.length===4);const[e,n,s,i]=t;return kt(e,n,s,i)}else if(typeof t=="string")return Yo[t];return t},$e=(t,e)=>n=>!!(Ft(n)&&Xi.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Hs=(t,e,n)=>s=>{if(!Ft(s))return s;const[i,r,o,a]=s.match(ee);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},qo=t=>tt(0,255,t),le={...ct,transform:t=>Math.round(qo(t))},at={test:$e("rgb","red"),parse:Hs("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+le.transform(t)+", "+le.transform(e)+", "+le.transform(n)+", "+Ct(At.transform(s))+")"};function Zo(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const xe={test:$e("#"),parse:Zo,transform:at.transform},mt={test:$e("hsl","hue"),parse:Hs("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+G.transform(Ct(e))+", "+G.transform(Ct(n))+", "+Ct(At.transform(s))+")"},j={test:t=>at.test(t)||xe.test(t)||mt.test(t),parse:t=>at.test(t)?at.parse(t):mt.test(t)?mt.parse(t):xe.parse(t),transform:t=>Ft(t)?t:t.hasOwnProperty("red")?at.transform(t):mt.transform(t)},D=(t,e,n)=>-n*t+n*e+t;function ce(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Qo({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=ce(l,a,t+1/3),r=ce(l,a,t),o=ce(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}const ue=(t,e,n)=>{const s=t*t;return Math.sqrt(Math.max(0,n*(e*e-s)+s))},Jo=[xe,at,mt],tr=t=>Jo.find(e=>e.test(t));function dn(t){const e=tr(t);let n=e.parse(t);return e===mt&&(n=Qo(n)),n}const $s=(t,e)=>{const n=dn(t),s=dn(e),i={...n};return r=>(i.red=ue(n.red,s.red,r),i.green=ue(n.green,s.green,r),i.blue=ue(n.blue,s.blue,r),i.alpha=D(n.alpha,s.alpha,r),at.transform(i))};function er(t){var e,n;return isNaN(t)&&Ft(t)&&(((e=t.match(ee))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(ys))===null||n===void 0?void 0:n.length)||0)>0}const zs={regex:Ki,countKey:"Vars",token:"${v}",parse:L},Ks={regex:ys,countKey:"Colors",token:"${c}",parse:j.parse},_s={regex:ee,countKey:"Numbers",token:"${n}",parse:ct.parse};function he(t,{regex:e,countKey:n,token:s,parse:i}){const r=t.tokenised.match(e);r&&(t["num"+n]=r.length,t.tokenised=t.tokenised.replace(e,s),t.values.push(...r.map(i)))}function Kt(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&he(n,zs),he(n,Ks),he(n,_s),n}function Xs(t){return Kt(t).values}function Ys(t){const{values:e,numColors:n,numVars:s,tokenised:i}=Kt(t),r=e.length;return o=>{let a=i;for(let l=0;l<r;l++)l<s?a=a.replace(zs.token,o[l]):l<s+n?a=a.replace(Ks.token,j.transform(o[l])):a=a.replace(_s.token,Ct(o[l]));return a}}const nr=t=>typeof t=="number"?0:t;function sr(t){const e=Xs(t);return Ys(t)(e.map(nr))}const et={test:er,parse:Xs,createTransformer:Ys,getAnimatableNone:sr},qs=(t,e)=>n=>`${n>0?e:t}`;function Zs(t,e){return typeof t=="number"?n=>D(t,e,n):j.test(t)?$s(t,e):t.startsWith("var(")?qs(t,e):Js(t,e)}const Qs=(t,e)=>{const n=[...t],s=n.length,i=t.map((r,o)=>Zs(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}},ir=(t,e)=>{const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Zs(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}},Js=(t,e)=>{const n=et.createTransformer(e),s=Kt(t),i=Kt(e);return s.numVars===i.numVars&&s.numColors===i.numColors&&s.numNumbers>=i.numNumbers?Q(Qs(s.values,i.values),n):qs(t,e)},Rt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},mn=(t,e)=>n=>D(t,e,n);function or(t){return typeof t=="number"?mn:typeof t=="string"?j.test(t)?$s:Js:Array.isArray(t)?Qs:typeof t=="object"?ir:mn}function rr(t,e,n){const s=[],i=n||or(t[0]),r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const l=Array.isArray(e)?e[o]||L:e;a=Q(l,a)}s.push(a)}return s}function ti(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(We(r===e.length),r===1)return()=>e[0];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const o=rr(e,s,i),a=o.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const h=Rt(t[u],t[u+1],c);return o[u](h)};return n?c=>l(tt(t[0],t[r-1],c)):l}function ar(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Rt(0,e,s);t.push(D(n,1,i))}}function lr(t){const e=[0];return ar(e,t.length-1),e}function cr(t,e){return t.map(n=>n*e)}function ur(t,e){return t.map(()=>e||Is).splice(0,t.length-1)}function _t({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=zo(s)?s.map(fn):fn(s),r={done:!1,value:e[0]},o=cr(n&&n.length===e.length?n:lr(e),t),a=ti(o,e,{ease:Array.isArray(i)?i:ur(e,i)});return{calculatedDuration:t,next:l=>(r.value=a(l),r.done=l>=t,r)}}function ei(t,e){return e?t*(1e3/e):0}const hr=5;function ni(t,e,n){const s=Math.max(e-hr,0);return ei(n-t(s),e-s)}const fe=.001,fr=.01,pn=10,dr=.05,mr=1;function pr({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,r;jo(t<=J(pn));let o=1-e;o=tt(dr,mr,o),t=tt(fr,pn,_(t)),o<1?(i=c=>{const u=c*o,h=u*t,f=u-n,d=Pe(c,o),m=Math.exp(-h);return fe-f/d*m},r=c=>{const h=c*o*t,f=h*n+n,d=Math.pow(o,2)*Math.pow(c,2)*t,m=Math.exp(-h),p=Pe(Math.pow(c,2),o);return(-i(c)+fe>0?-1:1)*((f-d)*m)/p}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-fe+u*h},r=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=yr(i,r,a);if(t=J(t),isNaN(l))return{stiffness:100,damping:10,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:o*2*Math.sqrt(s*c),duration:t}}}const gr=12;function yr(t,e,n){let s=n;for(let i=1;i<gr;i++)s=s-t(s)/e(s);return s}function Pe(t,e){return t*Math.sqrt(1-e*e)}const vr=["duration","bounce"],xr=["stiffness","damping","mass"];function gn(t,e){return e.some(n=>t[n]!==void 0)}function Pr(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!gn(t,xr)&&gn(t,vr)){const n=pr(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function si({keyframes:t,restDelta:e,restSpeed:n,...s}){const i=t[0],r=t[t.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:c,duration:u,velocity:h,isResolvedFromDuration:f}=Pr({...s,velocity:-_(s.velocity||0)}),d=h||0,m=l/(2*Math.sqrt(a*c)),p=r-i,y=_(Math.sqrt(a/c)),T=Math.abs(p)<5;n||(n=T?.01:2),e||(e=T?.005:.5);let v;if(m<1){const g=Pe(y,m);v=x=>{const V=Math.exp(-m*y*x);return r-V*((d+m*y*p)/g*Math.sin(g*x)+p*Math.cos(g*x))}}else if(m===1)v=g=>r-Math.exp(-y*g)*(p+(d+y*p)*g);else{const g=y*Math.sqrt(m*m-1);v=x=>{const V=Math.exp(-m*y*x),R=Math.min(g*x,300);return r-V*((d+m*y*p)*Math.sinh(R)+g*p*Math.cosh(R))/g}}return{calculatedDuration:f&&u||null,next:g=>{const x=v(g);if(f)o.done=g>=u;else{let V=d;g!==0&&(m<1?V=ni(v,g,x):V=0);const R=Math.abs(V)<=n,C=Math.abs(r-x)<=e;o.done=R&&C}return o.value=o.done?r:x,o}}}function yn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=S=>a!==void 0&&S<a||l!==void 0&&S>l,m=S=>a===void 0?l:l===void 0||Math.abs(a-S)<Math.abs(l-S)?a:l;let p=n*e;const y=h+p,T=o===void 0?y:o(y);T!==y&&(p=T-h);const v=S=>-p*Math.exp(-S/s),g=S=>T+v(S),x=S=>{const M=v(S),H=g(S);f.done=Math.abs(M)<=c,f.value=f.done?T:H};let V,R;const C=S=>{d(f.value)&&(V=S,R=si({keyframes:[f.value,m(f.value)],velocity:ni(g,S,f.value),damping:i,stiffness:r,restDelta:c,restSpeed:u}))};return C(0),{calculatedDuration:null,next:S=>{let M=!1;return!R&&V===void 0&&(M=!0,x(S),C(S)),V!==void 0&&S>V?R.next(S-V):(!M&&x(S),f)}}}const Tr=t=>{const e=({timestamp:n})=>t(n);return{start:()=>A.update(e,!0),stop:()=>X(e),now:()=>E.isProcessing?E.timestamp:performance.now()}},vn=2e4;function xn(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<vn;)e+=n,s=t.next(e);return e>=vn?1/0:e}const br={decay:yn,inertia:yn,tween:_t,keyframes:_t,spring:si};function Xt({autoplay:t=!0,delay:e=0,driver:n=Tr,keyframes:s,type:i="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:c,onComplete:u,onUpdate:h,...f}){let d=1,m=!1,p,y;const T=()=>{y=new Promise(w=>{p=w})};T();let v;const g=br[i]||_t;let x;g!==_t&&typeof s[0]!="number"&&(x=ti([0,100],s,{clamp:!1}),s=[0,100]);const V=g({...f,keyframes:s});let R;a==="mirror"&&(R=g({...f,keyframes:[...s].reverse(),velocity:-(f.velocity||0)}));let C="idle",S=null,M=null,H=null;V.calculatedDuration===null&&r&&(V.calculatedDuration=xn(V));const{calculatedDuration:ut}=V;let W=1/0,$=1/0;ut!==null&&(W=ut+o,$=W*(r+1)-o);let k=0;const ht=w=>{if(M===null)return;d>0&&(M=Math.min(M,w)),d<0&&(M=Math.min(w-$/d,M)),S!==null?k=S:k=Math.round(w-M)*d;const Pt=k-e*(d>=0?1:-1),Qe=d>=0?Pt<0:Pt>$;k=Math.max(Pt,0),C==="finished"&&S===null&&(k=$);let Je=k,tn=V;if(r){const ie=Math.min(k,$)/W;let jt=Math.floor(ie),st=ie%1;!st&&ie>=1&&(st=1),st===1&&jt--,jt=Math.min(jt,r+1),!!(jt%2)&&(a==="reverse"?(st=1-st,o&&(st-=o/W)):a==="mirror"&&(tn=R)),Je=tt(0,1,st)*W}const Tt=Qe?{done:!1,value:s[0]}:tn.next(Je);x&&(Tt.value=x(Tt.value));let{done:en}=Tt;!Qe&&ut!==null&&(en=d>=0?k>=$:k<=0);const Mi=S===null&&(C==="finished"||C==="running"&&en);return h&&h(Tt.value),Mi&&xt(),Tt},F=()=>{v&&v.stop(),v=void 0},Y=()=>{C="idle",F(),p(),T(),M=H=null},xt=()=>{C="finished",u&&u(),F(),p()},ft=()=>{if(m)return;v||(v=n(ht));const w=v.now();l&&l(),S!==null?M=w-S:(!M||C==="finished")&&(M=w),C==="finished"&&T(),H=M,S=null,C="running",v.start()};t&&ft();const Ze={then(w,Pt){return y.then(w,Pt)},get time(){return _(k)},set time(w){w=J(w),k=w,S!==null||!v||d===0?S=w:M=v.now()-w/d},get duration(){const w=V.calculatedDuration===null?xn(V):V.calculatedDuration;return _(w)},get speed(){return d},set speed(w){w===d||!v||(d=w,Ze.time=_(k))},get state(){return C},play:ft,pause:()=>{C="paused",S=k},stop:()=>{m=!0,C!=="idle"&&(C="idle",c&&c(),Y())},cancel:()=>{H!==null&&ht(H),Y()},complete:()=>{C="finished"},sample:w=>(M=0,ht(w))};return Ze}function Vr(t){let e;return()=>(e===void 0&&(e=t()),e)}const Sr=Vr(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ar=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ut=10,Cr=2e4,wr=(t,e)=>e.type==="spring"||t==="backgroundColor"||!Es(e.ease);function Dr(t,e,{onUpdate:n,onComplete:s,...i}){if(!(Sr()&&Ar.has(e)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,a,l,c=!1;const u=()=>{l=new Promise(g=>{a=g})};u();let{keyframes:h,duration:f=300,ease:d,times:m}=i;if(wr(e,i)){const g=Xt({...i,repeat:0,delay:0});let x={done:!1,value:h[0]};const V=[];let R=0;for(;!x.done&&R<Cr;)x=g.sample(R),V.push(x.value),R+=Ut;m=void 0,h=V,f=R-Ut,d="linear"}const p=Io(t.owner.current,e,h,{...i,duration:f,ease:d,times:m}),y=()=>{c=!1,p.cancel()},T=()=>{c=!0,A.update(y),a(),u()};return p.onfinish=()=>{c||(t.set(Uo(h,i)),s&&s(),T())},{then(g,x){return l.then(g,x)},attachTimeline(g){return p.timeline=g,p.onfinish=null,L},get time(){return _(p.currentTime||0)},set time(g){p.currentTime=J(g)},get speed(){return p.playbackRate},set speed(g){p.playbackRate=g},get duration(){return _(f)},play:()=>{o||(p.play(),X(y))},pause:()=>p.pause(),stop:()=>{if(o=!0,p.playState==="idle")return;const{currentTime:g}=p;if(g){const x=Xt({...i,autoplay:!1});t.setWithVelocity(x.sample(g-Ut).value,x.sample(g).value,Ut)}T()},complete:()=>{c||p.finish()},cancel:T}}function Mr({keyframes:t,delay:e,onUpdate:n,onComplete:s}){const i=()=>(n&&n(t[t.length-1]),s&&s(),{time:0,speed:1,duration:0,play:L,pause:L,stop:L,then:r=>(r(),Promise.resolve()),cancel:L,complete:L});return e?Xt({keyframes:[0,1],duration:0,delay:e,onComplete:i}):i()}const Lr={type:"spring",stiffness:500,damping:25,restSpeed:10},Rr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Br={type:"keyframes",duration:.8},Fr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Er=(t,{keyframes:e})=>e.length>2?Br:lt.has(t)?t.startsWith("scale")?Rr(e[1]):Lr:Fr,Te=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(et.test(e)||e==="0")&&!e.startsWith("url(")),kr=new Set(["brightness","contrast","saturate","opacity"]);function jr(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(ee)||[];if(!s)return t;const i=n.replace(s,"");let r=kr.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Or=/([a-z-]*)\(.*?\)/g,be={...et,getAnimatableNone:t=>{const e=t.match(Or);return e?e.map(jr).join(" "):t}},Ir={...vs,color:j,backgroundColor:j,outlineColor:j,fill:j,stroke:j,borderColor:j,borderTopColor:j,borderRightColor:j,borderBottomColor:j,borderLeftColor:j,filter:be,WebkitFilter:be},ze=t=>Ir[t];function ii(t,e){let n=ze(t);return n!==be&&(n=et),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const oi=t=>/^0[^.\s]+$/.test(t);function Ur(t){if(typeof t=="number")return t===0;if(t!==null)return t==="none"||t==="0"||oi(t)}function Nr(t,e,n,s){const i=Te(e,n);let r;Array.isArray(n)?r=[...n]:r=[null,n];const o=s.from!==void 0?s.from:t.get();let a;const l=[];for(let c=0;c<r.length;c++)r[c]===null&&(r[c]=c===0?o:r[c-1]),Ur(r[c])&&l.push(c),typeof r[c]=="string"&&r[c]!=="none"&&r[c]!=="0"&&(a=r[c]);if(i&&l.length&&a)for(let c=0;c<l.length;c++){const u=l[c];r[u]=ii(e,a)}return r}function Wr({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function Ke(t,e){return t[e]||t.default||t}const Gr={skipAnimations:!1},_e=(t,e,n,s={})=>i=>{const r=Ke(s,t)||{},o=r.delay||s.delay||0;let{elapsed:a=0}=s;a=a-J(o);const l=Nr(e,t,n,r),c=l[0],u=l[l.length-1],h=Te(t,c),f=Te(t,u);let d={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...r,delay:-a,onUpdate:m=>{e.set(m),r.onUpdate&&r.onUpdate(m)},onComplete:()=>{i(),r.onComplete&&r.onComplete()}};if(Wr(r)||(d={...d,...Er(t,d)}),d.duration&&(d.duration=J(d.duration)),d.repeatDelay&&(d.repeatDelay=J(d.repeatDelay)),!h||!f||Oo.current||r.type===!1||Gr.skipAnimations)return Mr(d);if(!s.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const m=Dr(e,t,d);if(m)return m}return Xt(d)};function Yt(t){return!!(O(t)&&t.add)}const ri=t=>/^\-?\d*\.?\d+$/.test(t);function Xe(t,e){t.indexOf(e)===-1&&t.push(e)}function Ye(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class qe{constructor(){this.subscriptions=[]}add(e){return Xe(this.subscriptions,e),()=>Ye(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Hr=t=>!isNaN(parseFloat(t));class $r{constructor(e,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{this.prev=this.current,this.current=s;const{delta:r,timestamp:o}=E;this.lastUpdated!==o&&(this.timeDelta=r,this.lastUpdated=o,A.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>A.postRender(this.velocityCheck),this.velocityCheck=({timestamp:s})=>{s!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Hr(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new qe);const s=this.events[e].add(n);return e==="change"?()=>{s(),A.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ei(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function yt(t,e){return new $r(t,e)}const ai=t=>e=>e.test(t),zr={test:t=>t==="auto",parse:t=>t},li=[ct,P,G,q,qi,Yi,zr],bt=t=>li.find(ai(t)),Kr=[...li,j,et],_r=t=>Kr.find(ai(t));function Xr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,yt(n))}function Yr(t,e){const n=se(t,e);let{transitionEnd:s={},transition:i={},...r}=n?t.makeTargetAnimatable(n,!1):{};r={...r,...s};for(const o in r){const a=ho(r[o]);Xr(t,o,a)}}function qr(t,e,n){var s,i;const r=Object.keys(e).filter(a=>!t.hasValue(a)),o=r.length;if(o)for(let a=0;a<o;a++){const l=r[a],c=e[l];let u=null;Array.isArray(c)&&(u=c[0]),u===null&&(u=(i=(s=n[l])!==null&&s!==void 0?s:t.readValue(l))!==null&&i!==void 0?i:e[l]),u!=null&&(typeof u=="string"&&(ri(u)||oi(u))?u=parseFloat(u):!_r(u)&&et.test(c)&&(u=ii(l,c)),t.addValue(l,yt(u,{owner:t})),n[l]===void 0&&(n[l]=u),u!==null&&t.setBaseTarget(l,u))}}function Zr(t,e){return e?(e[t]||e.default||e).from:void 0}function Qr(t,e,n){const s={};for(const i in t){const r=Zr(i,e);if(r!==void 0)s[i]=r;else{const o=n.getValue(i);o&&(s[i]=o.get())}}return s}function Jr({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function ta(t,e){const n=t.get();if(Array.isArray(e)){for(let s=0;s<e.length;s++)if(e[s]!==n)return!0}else return n!==e}function ci(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e);const l=t.getValue("willChange");s&&(r=s);const c=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const h in a){const f=t.getValue(h),d=a[h];if(!f||d===void 0||u&&Jr(u,h))continue;const m={delay:n,elapsed:0,...Ke(r||{},h)};if(window.HandoffAppearAnimations){const T=t.getProps()[us];if(T){const v=window.HandoffAppearAnimations(T,h,f,A);v!==null&&(m.elapsed=v,m.isHandoff=!0)}}let p=!m.isHandoff&&!ta(f,d);if(m.type==="spring"&&(f.getVelocity()||m.velocity)&&(p=!1),f.animation&&(p=!1),p)continue;f.start(_e(h,f,d,t.shouldReduceMotion&&lt.has(h)?{type:!1}:m));const y=f.animation;Yt(l)&&(l.add(h),y.then(()=>l.remove(h))),c.push(y)}return o&&Promise.all(c).then(()=>{o&&Yr(t,o)}),c}function Ve(t,e,n={}){const s=se(t,e,n.custom);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(ci(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(l=0)=>{const{delayChildren:c=0,staggerChildren:u,staggerDirection:h}=i;return ea(t,e,c+l,u,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,c]=a==="beforeChildren"?[r,o]:[o,r];return l().then(()=>c())}else return Promise.all([r(),o(n.delay)])}function ea(t,e,n=0,s=0,i=1,r){const o=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(na).forEach((c,u)=>{c.notify("AnimationStart",e),o.push(Ve(c,e,{...r,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(o)}function na(t,e){return t.sortNodePosition(e)}function sa(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Ve(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Ve(t,e,n);else{const i=typeof e=="function"?se(t,e,n.custom):e;s=Promise.all(ci(t,i,n))}return s.then(()=>t.notify("AnimationComplete",e))}const ia=[...Be].reverse(),oa=Be.length;function ra(t){return e=>Promise.all(e.map(({animation:n,options:s})=>sa(t,n,s)))}function aa(t){let e=ra(t);const n=ca();let s=!0;const i=(l,c)=>{const u=se(t,c);if(u){const{transition:h,transitionEnd:f,...d}=u;l={...l,...d,...f}}return l};function r(l){e=l(t)}function o(l,c){const u=t.getProps(),h=t.getVariantContext(!0)||{},f=[],d=new Set;let m={},p=1/0;for(let T=0;T<oa;T++){const v=ia[T],g=n[v],x=u[v]!==void 0?u[v]:h[v],V=Mt(x),R=v===c?g.isActive:null;R===!1&&(p=T);let C=x===h[v]&&x!==u[v]&&V;if(C&&s&&t.manuallyAnimateOnMount&&(C=!1),g.protectedKeys={...m},!g.isActive&&R===null||!x&&!g.prevProp||Jt(x)||typeof x=="boolean")continue;let M=la(g.prevProp,x)||v===c&&g.isActive&&!C&&V||T>p&&V,H=!1;const ut=Array.isArray(x)?x:[x];let W=ut.reduce(i,{});R===!1&&(W={});const{prevResolvedValues:$={}}=g,k={...$,...W},ht=F=>{M=!0,d.has(F)&&(H=!0,d.delete(F)),g.needsAnimating[F]=!0};for(const F in k){const Y=W[F],xt=$[F];if(m.hasOwnProperty(F))continue;let ft=!1;zt(Y)&&zt(xt)?ft=!Bs(Y,xt):ft=Y!==xt,ft?Y!==void 0?ht(F):d.add(F):Y!==void 0&&d.has(F)?ht(F):g.protectedKeys[F]=!0}g.prevProp=x,g.prevResolvedValues=W,g.isActive&&(m={...m,...W}),s&&t.blockInitialAnimation&&(M=!1),M&&(!C||H)&&f.push(...ut.map(F=>({animation:F,options:{type:v,...l}})))}if(d.size){const T={};d.forEach(v=>{const g=t.getBaseTarget(v);g!==void 0&&(T[v]=g)}),f.push({animation:T})}let y=!!f.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(y=!1),s=!1,y?e(f):Promise.resolve()}function a(l,c,u){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(d=>{var m;return(m=d.animationState)===null||m===void 0?void 0:m.setActive(l,c)}),n[l].isActive=c;const f=o(u,l);for(const d in n)n[d].protectedKeys={};return f}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n}}function la(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Bs(e,t):!1}function it(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ca(){return{animate:it(!0),whileInView:it(),whileHover:it(),whileTap:it(),whileDrag:it(),whileFocus:it(),exit:it()}}class ua extends nt{constructor(e){super(e),e.animationState||(e.animationState=aa(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),Jt(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let ha=0;class fa extends nt{constructor(){super(...arguments),this.id=ha++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n,custom:s}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const r=this.node.animationState.setActive("exit",!e,{custom:s??this.node.getProps().custom});n&&!e&&r.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const da={animation:{Feature:ua},exit:{Feature:fa}},Pn=(t,e)=>Math.abs(t-e);function ma(t,e){const n=Pn(t.x,e.x),s=Pn(t.y,e.y);return Math.sqrt(n**2+s**2)}class ui{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=me(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=ma(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=E;this.history.push({...m,timestamp:p});const{onStart:y,onMove:T}=this.handlers;f||(y&&y(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),T&&T(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=de(f,this.transformPagePoint),A.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=me(h.type==="pointercancel"?this.lastMoveEventInfo:de(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,y),m&&m(h,y)},!ws(e))return;this.dragSnapToOrigin=r,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const o=ne(e),a=de(o,this.transformPagePoint),{point:l}=a,{timestamp:c}=E;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,me(a,this.history)),this.removeListeners=Q(K(this.contextWindow,"pointermove",this.handlePointerMove),K(this.contextWindow,"pointerup",this.handlePointerUp),K(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),X(this.updatePoint)}}function de(t,e){return e?{point:e(t.point)}:t}function Tn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function me({point:t},e){return{point:t,delta:Tn(t,hi(e)),offset:Tn(t,pa(e)),velocity:ga(e,.1)}}function pa(t){return t[0]}function hi(t){return t[t.length-1]}function ga(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=hi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>J(e)));)n--;if(!s)return{x:0,y:0};const r=_(i.timestamp-s.timestamp);if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function I(t){return t.max-t.min}function Se(t,e=0,n=.01){return Math.abs(t-e)<=n}function bn(t,e,n,s=.5){t.origin=s,t.originPoint=D(e.min,e.max,t.origin),t.scale=I(n)/I(e),(Se(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=D(n.min,n.max,t.origin)-t.originPoint,(Se(t.translate)||isNaN(t.translate))&&(t.translate=0)}function wt(t,e,n,s){bn(t.x,e.x,n.x,s?s.originX:void 0),bn(t.y,e.y,n.y,s?s.originY:void 0)}function Vn(t,e,n){t.min=n.min+e.min,t.max=t.min+I(e)}function ya(t,e,n){Vn(t.x,e.x,n.x),Vn(t.y,e.y,n.y)}function Sn(t,e,n){t.min=e.min-n.min,t.max=t.min+I(e)}function Dt(t,e,n){Sn(t.x,e.x,n.x),Sn(t.y,e.y,n.y)}function va(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?D(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?D(n,t,s.max):Math.min(t,n)),t}function An(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function xa(t,{top:e,left:n,bottom:s,right:i}){return{x:An(t.x,n,i),y:An(t.y,e,s)}}function Cn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Pa(t,e){return{x:Cn(t.x,e.x),y:Cn(t.y,e.y)}}function Ta(t,e){let n=.5;const s=I(t),i=I(e);return i>s?n=Rt(e.min,e.max-s,t.min):s>i&&(n=Rt(t.min,t.max-i,e.min)),tt(0,1,n)}function ba(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Ae=.35;function Va(t=Ae){return t===!1?t=0:t===!0&&(t=Ae),{x:wn(t,"left","right"),y:wn(t,"top","bottom")}}function wn(t,e,n){return{min:Dn(t,e),max:Dn(t,n)}}function Dn(t,e){return typeof t=="number"?t:t[e]||0}const Mn=()=>({translate:0,scale:1,origin:0,originPoint:0}),pt=()=>({x:Mn(),y:Mn()}),Ln=()=>({min:0,max:0}),B=()=>({x:Ln(),y:Ln()});function N(t){return[t("x"),t("y")]}function fi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Sa({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Aa(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function pe(t){return t===void 0||t===1}function Ce({scale:t,scaleX:e,scaleY:n}){return!pe(t)||!pe(e)||!pe(n)}function ot(t){return Ce(t)||di(t)||t.z||t.rotate||t.rotateX||t.rotateY}function di(t){return Rn(t.x)||Rn(t.y)}function Rn(t){return t&&t!=="0%"}function qt(t,e,n){const s=t-n,i=e*s;return n+i}function Bn(t,e,n,s,i){return i!==void 0&&(t=qt(t,i,s)),qt(t,n,s)+e}function we(t,e=0,n=1,s,i){t.min=Bn(t.min,e,n,s,i),t.max=Bn(t.max,e,n,s,i)}function mi(t,{x:e,y:n}){we(t.x,e.translate,e.scale,e.originPoint),we(t.y,n.translate,n.scale,n.originPoint)}function Ca(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,o;for(let a=0;a<i;a++){r=n[a],o=r.projectionDelta;const l=r.instance;l&&l.style&&l.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&gt(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,mi(t,o)),s&&ot(r.latestValues)&&gt(t,r.latestValues))}e.x=Fn(e.x),e.y=Fn(e.y)}function Fn(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Z(t,e){t.min=t.min+e,t.max=t.max+e}function En(t,e,[n,s,i]){const r=e[i]!==void 0?e[i]:.5,o=D(t.min,t.max,r);we(t,e[n],e[s],o,e.scale)}const wa=["x","scaleX","originX"],Da=["y","scaleY","originY"];function gt(t,e){En(t.x,e,wa),En(t.y,e,Da)}function pi(t,e){return fi(Aa(t.getBoundingClientRect(),e))}function Ma(t,e,n){const s=pi(t,n),{scroll:i}=e;return i&&(Z(s.x,i.offset.x),Z(s.y,i.offset.y)),s}const gi=({current:t})=>t?t.ownerDocument.defaultView:null,La=new WeakMap;class Ra{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=B(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ne(u,"page").point)},r=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Ms(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),N(y=>{let T=this.getAxisMotionValue(y).get()||0;if(G.test(T)){const{projection:v}=this.visualElement;if(v&&v.layout){const g=v.layout.layoutBox[y];g&&(T=I(g)*(parseFloat(T)/100))}}this.originPoint[y]=T}),m&&A.update(()=>m(u,h),!1,!0);const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},o=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:y}=h;if(d&&this.currentDirection===null){this.currentDirection=Ba(y),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,y),this.updateAxis("y",h.point,y),this.visualElement.render(),p&&p(u,h)},a=(u,h)=>this.stop(u,h),l=()=>N(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new ui(e,{onSessionStart:i,onStart:r,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:gi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r&&A.update(()=>r(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Nt(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=va(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,r=this.constraints;n&&dt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=xa(i.layoutBox,n):this.constraints=!1,this.elastic=Va(s),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&N(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=ba(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!dt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=Ma(s,i.root,this.visualElement.getTransformPagePoint());let o=Pa(i.layout.layoutBox,r);if(n){const a=n(Sa(o));this.hasMutatedConstraints=!!a,a&&(o=fi(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=N(u=>{if(!Nt(u,n,this.currentDirection))return;let h=l&&l[u]||{};o&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...r,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(_e(e,s,0,n))}stopAnimation(){N(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){N(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n="_drag"+e.toUpperCase(),s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){N(n=>{const{drag:s}=this.getProps();if(!Nt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-D(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!dt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};N(o=>{const a=this.getAxisMotionValue(o);if(a){const l=a.get();i[o]=Ta({min:l,max:l},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),N(o=>{if(!Nt(o,e,null))return;const a=this.getAxisMotionValue(o),{min:l,max:c}=this.constraints[o];a.set(D(l,c,i[o]))})}addListeners(){if(!this.visualElement.current)return;La.set(this.visualElement,this);const e=this.visualElement.current,n=K(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();dt(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),s();const o=z(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(N(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{o(),n(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=Ae,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function Nt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Ba(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Fa extends nt{constructor(e){super(e),this.removeGroupControls=L,this.removeListeners=L,this.controls=new Ra(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||L}unmount(){this.removeGroupControls(),this.removeListeners()}}const kn=t=>(e,n)=>{t&&A.update(()=>t(e,n))};class Ea extends nt{constructor(){super(...arguments),this.removePointerDownListener=L}onPointerDown(e){this.session=new ui(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:gi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:kn(e),onStart:kn(n),onMove:s,onEnd:(r,o)=>{delete this.session,i&&A.update(()=>i(r,o))}}}mount(){this.removePointerDownListener=K(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function ka(){const t=b.useContext(Le);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=b.useId();return b.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}const Gt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function jn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Vt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(P.test(t))t=parseFloat(t);else return t;const n=jn(t,e.target.x),s=jn(t,e.target.y);return`${n}% ${s}%`}},ja={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=et.parse(t);if(i.length>5)return s;const r=et.createTransformer(t),o=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+o]/=a,i[1+o]/=l;const c=D(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=c),typeof i[3+o]=="number"&&(i[3+o]/=c),r(i)}};class Oa extends as.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;Gi(Ia),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),Gt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,o=s.projection;return o&&(o.isPresent=r,i||e.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?o.promote():o.relegate()||A.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function yi(t){const[e,n]=ka(),s=b.useContext(fs);return as.createElement(Oa,{...t,layoutGroup:s,switchLayoutGroup:b.useContext(ds),isPresent:e,safeToRemove:n})}const Ia={borderRadius:{...Vt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Vt,borderTopRightRadius:Vt,borderBottomLeftRadius:Vt,borderBottomRightRadius:Vt,boxShadow:ja},vi=["TopLeft","TopRight","BottomLeft","BottomRight"],Ua=vi.length,On=t=>typeof t=="string"?parseFloat(t):t,In=t=>typeof t=="number"||P.test(t);function Na(t,e,n,s,i,r){i?(t.opacity=D(0,n.opacity!==void 0?n.opacity:1,Wa(s)),t.opacityExit=D(e.opacity!==void 0?e.opacity:1,0,Ga(s))):r&&(t.opacity=D(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let o=0;o<Ua;o++){const a=`border${vi[o]}Radius`;let l=Un(e,a),c=Un(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||In(l)===In(c)?(t[a]=Math.max(D(On(l),On(c),s),0),(G.test(c)||G.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=D(e.rotate||0,n.rotate||0,s))}function Un(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Wa=xi(0,.5,Ws),Ga=xi(.5,.95,L);function xi(t,e,n){return s=>s<t?0:s>e?1:n(Rt(t,e,s))}function Nn(t,e){t.min=e.min,t.max=e.max}function U(t,e){Nn(t.x,e.x),Nn(t.y,e.y)}function Wn(t,e,n,s,i){return t-=e,t=qt(t,1/n,s),i!==void 0&&(t=qt(t,1/i,s)),t}function Ha(t,e=0,n=1,s=.5,i,r=t,o=t){if(G.test(e)&&(e=parseFloat(e),e=D(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=D(r.min,r.max,s);t===r&&(a-=e),t.min=Wn(t.min,e,n,a,i),t.max=Wn(t.max,e,n,a,i)}function Gn(t,e,[n,s,i],r,o){Ha(t,e[n],e[s],e[i],e.scale,r,o)}const $a=["x","scaleX","originX"],za=["y","scaleY","originY"];function Hn(t,e,n,s){Gn(t.x,e,$a,n?n.x:void 0,s?s.x:void 0),Gn(t.y,e,za,n?n.y:void 0,s?s.y:void 0)}function $n(t){return t.translate===0&&t.scale===1}function Pi(t){return $n(t.x)&&$n(t.y)}function Ka(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function Ti(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function zn(t){return I(t.x)/I(t.y)}class _a{constructor(){this.members=[]}add(e){Xe(this.members,e),e.scheduleRender()}remove(e){if(Ye(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Kn(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y;if((i||r)&&(s=`translate3d(${i}px, ${r}px, 0) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:l,rotateX:c,rotateY:u}=n;l&&(s+=`rotate(${l}deg) `),c&&(s+=`rotateX(${c}deg) `),u&&(s+=`rotateY(${u}deg) `)}const o=t.x.scale*e.x,a=t.y.scale*e.y;return(o!==1||a!==1)&&(s+=`scale(${o}, ${a})`),s||"none"}const Xa=(t,e)=>t.depth-e.depth;class Ya{constructor(){this.children=[],this.isDirty=!1}add(e){Xe(this.children,e),this.isDirty=!0}remove(e){Ye(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Xa),this.isDirty=!1,this.children.forEach(e)}}function qa(t,e){const n=performance.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(X(s),t(r-e))};return A.read(s,!0),()=>X(s)}function Za(t){window.MotionDebug&&window.MotionDebug.record(t)}function Qa(t){return t instanceof SVGElement&&t.tagName!=="svg"}function Ja(t,e,n){const s=O(t)?t:yt(t);return s.start(_e("",s,e,n)),s.animation}const _n=["","X","Y","Z"],tl={visibility:"hidden"},Xn=1e3;let el=0;const rt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function bi({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o={},a=e==null?void 0:e()){this.id=el++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rt.totalNodes=rt.resolvedTargetDeltas=rt.recalculatedProjection=0,this.nodes.forEach(il),this.nodes.forEach(cl),this.nodes.forEach(ul),this.nodes.forEach(ol),Za(rt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ya)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new qe),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Qa(o),this.instance=o;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=qa(f,250),Gt.hasAnimatedSinceResize&&(Gt.hasAnimatedSinceResize=!1,this.nodes.forEach(qn))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||pl,{onLayoutAnimationStart:y,onLayoutAnimationComplete:T}=u.getProps(),v=!this.targetLayout||!Ti(this.targetLayout,m)||d,g=!f&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(v||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,g);const x={...Ke(p,"layout"),onPlay:y,onComplete:T};(u.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x)}else f||qn(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,X(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(hl),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Yn);return}this.isUpdating||this.nodes.forEach(al),this.isUpdating=!1,this.nodes.forEach(ll),this.nodes.forEach(nl),this.nodes.forEach(sl),this.clearAllSnapshots();const a=performance.now();E.delta=tt(0,1e3/60,a-E.timestamp),E.timestamp=a,E.isProcessing=!0,oe.update.process(E),oe.preRender.process(E),oe.render.process(E),E.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(rl),this.sharedNodes.forEach(fl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=B(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Pi(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;o&&(a||ot(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),gl(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return B();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&(Z(a.x,l.offset.x),Z(a.y,l.offset.y)),a}removeElementScroll(o){const a=B();U(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:h}=c;if(c!==this.root&&u&&h.layoutScroll){if(u.isRoot){U(a,o);const{scroll:f}=this.root;f&&(Z(a.x,-f.offset.x),Z(a.y,-f.offset.y))}Z(a.x,u.offset.x),Z(a.y,u.offset.y)}}return a}applyTransform(o,a=!1){const l=B();U(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&gt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),ot(u.latestValues)&&gt(l,u.latestValues)}return ot(this.latestValues)&&gt(l,this.latestValues),l}removeTransform(o){const a=B();U(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!ot(c.latestValues))continue;Ce(c.latestValues)&&c.updateSnapshot();const u=B(),h=c.measurePageBox();U(u,h),Hn(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return ot(this.latestValues)&&Hn(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==E.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=E.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=B(),this.relativeTargetOrigin=B(),Dt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),U(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=B(),this.targetWithTransforms=B()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ya(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):U(this.target,this.layout.layoutBox),mi(this.target,this.targetDelta)):U(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=B(),this.relativeTargetOrigin=B(),Dt(this.relativeTargetOrigin,this.target,d.target),U(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ce(this.parent.latestValues)||di(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===E.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;U(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Ca(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:m}=a;if(!m){this.projectionTransform&&(this.projectionDelta=pt(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=pt(),this.projectionDeltaWithTransform=pt());const p=this.projectionTransform;wt(this.projectionDelta,this.layoutCorrected,m,this.latestValues),this.projectionTransform=Kn(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==f||this.treeScale.y!==d)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m)),rt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=pt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=B(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,y=this.getStack(),T=!y||y.members.length<=1,v=!!(p&&!T&&this.options.crossfade===!0&&!this.path.some(ml));this.animationProgress=0;let g;this.mixTargetDelta=x=>{const V=x/1e3;Zn(h.x,o.x,V),Zn(h.y,o.y,V),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),dl(this.relativeTarget,this.relativeTargetOrigin,f,V),g&&Ka(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=B()),U(g,this.relativeTarget)),p&&(this.animationValues=u,Na(u,c,this.latestValues,V,v,T)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=V},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(X(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.update(()=>{Gt.hasAnimatedSinceResize=!0,this.currentAnimation=Ja(0,Xn,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Xn),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=o;if(!(!a||!l||!c)){if(this!==o&&this.layout&&c&&Vi(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||B();const h=I(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+h;const f=I(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}U(a,l),gt(a,u),wt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new _a),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const c={};for(let u=0;u<_n.length;u++){const h="rotate"+_n[u];l[h]&&(c[h]=l[h],o.setStaticValue(h,0))}o.render();for(const u in c)o.setStaticValue(u,c[u]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return tl;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Wt(o==null?void 0:o.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const p={};return this.options.layoutId&&(p.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,p.pointerEvents=Wt(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!ot(this.latestValues)&&(p.transform=u?u({},""):"none",this.hasProjected=!1),p}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=Kn(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:d,y:m}=this.projectionDelta;c.transformOrigin=`${d.origin*100}% ${m.origin*100}% 0`,h.animationValues?c.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const p in Ht){if(f[p]===void 0)continue;const{correct:y,applyTo:T}=Ht[p],v=c.transform==="none"?f[p]:y(f[p],h);if(T){const g=T.length;for(let x=0;x<g;x++)c[T[x]]=v}else c[p]=v}return this.options.layoutId&&(c.pointerEvents=h===this?Wt(o==null?void 0:o.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Yn),this.root.sharedNodes.clear()}}}function nl(t){t.updateLayout()}function sl(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:r}=t.options,o=n.source!==t.layout.source;r==="size"?N(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],d=I(f);f.min=s[h].min,f.max=f.min+d}):Vi(r,n.layoutBox,s)&&N(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],d=I(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=pt();wt(a,s,n.layoutBox);const l=pt();o?wt(l,t.applyTransform(i,!0),n.measuredBox):wt(l,s,n.layoutBox);const c=!Pi(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=B();Dt(m,n.layoutBox,f.layoutBox);const p=B();Dt(p,s,d.layoutBox),Ti(m,p)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=p,t.relativeTargetOrigin=m,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function il(t){rt.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ol(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rl(t){t.clearSnapshot()}function Yn(t){t.clearMeasurements()}function al(t){t.isLayoutDirty=!1}function ll(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function qn(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function cl(t){t.resolveTargetDelta()}function ul(t){t.calcProjection()}function hl(t){t.resetRotation()}function fl(t){t.removeLeadSnapshot()}function Zn(t,e,n){t.translate=D(e.translate,0,n),t.scale=D(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Qn(t,e,n,s){t.min=D(e.min,n.min,s),t.max=D(e.max,n.max,s)}function dl(t,e,n,s){Qn(t.x,e.x,n.x,s),Qn(t.y,e.y,n.y,s)}function ml(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const pl={duration:.45,ease:[.4,0,.1,1]},Jn=t=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(t),ts=Jn("applewebkit/")&&!Jn("chrome/")?Math.round:L;function es(t){t.min=ts(t.min),t.max=ts(t.max)}function gl(t){es(t.x),es(t.y)}function Vi(t,e,n){return t==="position"||t==="preserve-aspect"&&!Se(zn(e),zn(n),.2)}const yl=bi({attachResizeListener:(t,e)=>z(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ge={current:void 0},Si=bi({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ge.current){const t=new yl({});t.mount(window),t.setOptions({layoutScroll:!0}),ge.current=t}return ge.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),vl={pan:{Feature:Ea},drag:{Feature:Fa,ProjectionNode:Si,MeasureLayout:yi}},xl=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Pl(t){const e=xl.exec(t);if(!e)return[,];const[,n,s]=e;return[n,s]}function De(t,e,n=1){const[s,i]=Pl(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return ri(o)?parseFloat(o):o}else return ye(i)?De(i,e,n+1):i}function Tl(t,{...e},n){const s=t.current;if(!(s instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(i=>{const r=i.get();if(!ye(r))return;const o=De(r,s);o&&i.set(o)});for(const i in e){const r=e[i];if(!ye(r))continue;const o=De(r,s);o&&(e[i]=o,n||(n={}),n[i]===void 0&&(n[i]=r))}return{target:e,transitionEnd:n}}const bl=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Ai=t=>bl.has(t),Vl=t=>Object.keys(t).some(Ai),ns=t=>t===ct||t===P,ss=(t,e)=>parseFloat(t.split(", ")[e]),is=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/);if(i)return ss(i[1],e);{const r=s.match(/^matrix\((.+)\)$/);return r?ss(r[1],t):0}},Sl=new Set(["x","y","z"]),Al=Bt.filter(t=>!Sl.has(t));function Cl(t){const e=[];return Al.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}const vt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:is(4,13),y:is(5,14)};vt.translateX=vt.x;vt.translateY=vt.y;const wl=(t,e,n)=>{const s=e.measureViewportBox(),i=e.current,r=getComputedStyle(i),{display:o}=r,a={};o==="none"&&e.setStaticValue("display",t.display||"block"),n.forEach(c=>{a[c]=vt[c](s,r)}),e.render();const l=e.measureViewportBox();return n.forEach(c=>{const u=e.getValue(c);u&&u.jump(a[c]),t[c]=vt[c](l,r)}),t},Dl=(t,e,n={},s={})=>{e={...e},s={...s};const i=Object.keys(e).filter(Ai);let r=[],o=!1;const a=[];if(i.forEach(l=>{const c=t.getValue(l);if(!t.hasValue(l))return;let u=n[l],h=bt(u);const f=e[l];let d;if(zt(f)){const m=f.length,p=f[0]===null?1:0;u=f[p],h=bt(u);for(let y=p;y<m&&f[y]!==null;y++)d?We(bt(f[y])===d):d=bt(f[y])}else d=bt(f);if(h!==d)if(ns(h)&&ns(d)){const m=c.get();typeof m=="string"&&c.set(parseFloat(m)),typeof f=="string"?e[l]=parseFloat(f):Array.isArray(f)&&d===P&&(e[l]=f.map(parseFloat))}else h!=null&&h.transform&&(d!=null&&d.transform)&&(u===0||f===0)?u===0?c.set(d.transform(u)):e[l]=h.transform(f):(o||(r=Cl(t),o=!0),a.push(l),s[l]=s[l]!==void 0?s[l]:e[l],c.jump(f))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,c=wl(e,t,a);return r.length&&r.forEach(([u,h])=>{t.getValue(u).set(h)}),t.render(),Qt&&l!==null&&window.scrollTo({top:l}),{target:c,transitionEnd:s}}else return{target:e,transitionEnd:s}};function Ml(t,e,n,s){return Vl(e)?Dl(t,e,n,s):{target:e,transitionEnd:s}}const Ll=(t,e,n,s)=>{const i=Tl(t,e,s);return e=i.target,s=i.transitionEnd,Ml(t,e,n,s)},Me={current:null},Ci={current:!1};function Rl(){if(Ci.current=!0,!!Qt)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Me.current=t.matches;t.addListener(e),e()}else Me.current=!1}function Bl(t,e,n){const{willChange:s}=e;for(const i in e){const r=e[i],o=n[i];if(O(r))t.addValue(i,r),Yt(s)&&s.add(i);else if(O(o))t.addValue(i,yt(r,{owner:t})),Yt(s)&&s.remove(i);else if(o!==r)if(t.hasValue(i)){const a=t.getValue(i);!a.hasAnimated&&a.set(r)}else{const a=t.getStaticValue(i);t.addValue(i,yt(a!==void 0?a:r,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const os=new WeakMap,wi=Object.keys(Lt),Fl=wi.length,rs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],El=Fe.length;class kl{constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>A.render(this.render,!1,!0);const{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=te(n),this.isVariantNode=hs(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:c,...u}=this.scrapeMotionValuesFromProps(n,{});for(const h in u){const f=u[h];a[h]!==void 0&&O(f)&&(f.set(a[h],!1),Yt(c)&&c.add(h))}}scrapeMotionValuesFromProps(e,n){return{}}mount(e){this.current=e,os.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Ci.current||Rl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Me.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){os.delete(this.current),this.projection&&this.projection.unmount(),X(this.notifyUpdate),X(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,n){const s=lt.has(e),i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&A.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),r()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},s,i,r){let o,a;for(let l=0;l<Fl;l++){const c=wi[l],{isEnabled:u,Feature:h,ProjectionNode:f,MeasureLayout:d}=Lt[c];f&&(o=f),u(n)&&(!this.features[c]&&h&&(this.features[c]=new h(this)),d&&(a=d))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:c,drag:u,dragConstraints:h,layoutScroll:f,layoutRoot:d}=n;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||h&&dt(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:r,layoutScroll:f,layoutRoot:d})}return a}updateFeatures(){for(const e in this.features){const n=this.features[e];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):B()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}makeTargetAnimatable(e,n=!0){return this.makeTargetAnimatableFromInstance(e,this.props,n)}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<rs.length;s++){const i=rs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Bl(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const s=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(s.initial=this.props.initial),s}const n={};for(let s=0;s<El;s++){const i=Fe[s],r=this.props[i];(Mt(r)||r===!1)&&(n[i]=r)}return n}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=yt(n,{owner:this}),this.addValue(e,s)),s}readValue(e){var n;return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props,i=typeof s=="string"||typeof s=="object"?(n=Ne(this.props,s))===null||n===void 0?void 0:n[e]:void 0;if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!O(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new qe),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Di extends kl{sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:n,...s},{transformValues:i},r){let o=Qr(s,e||{},this);if(i&&(n&&(n=i(n)),s&&(s=i(s)),o&&(o=i(o))),r){qr(this,s,o);const a=Ll(this,s,o,n);n=a.transitionEnd,s=a.target}return{transition:e,transitionEnd:n,...s}}}function jl(t){return window.getComputedStyle(t)}class Ol extends Di{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,n){if(lt.has(n)){const s=ze(n);return s&&s.default||0}else{const s=jl(e),i=(gs(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return pi(e,n)}build(e,n,s,i){ke(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,n){return Ue(e,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;O(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,s,i){bs(e,n,s,i)}}class Il extends Di{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(lt.has(n)){const s=ze(n);return s&&s.default||0}return n=Vs.has(n)?n:Re(n),e.getAttribute(n)}measureInstanceViewportBox(){return B()}scrapeMotionValuesFromProps(e,n){return As(e,n)}build(e,n,s,i){Oe(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){Ss(e,n,s,i)}mount(e){this.isSVGTag=Ie(e.tagName),super.mount(e)}}const Ul=(t,e)=>Ee(t)?new Il(e,{enableHardwareAcceleration:!1}):new Ol(e,{enableHardwareAcceleration:!0}),Nl={layout:{ProjectionNode:Si,MeasureLayout:yi}},Wl={...da,...Fo,...vl,...Nl},zl=Ni((t,e)=>Po(t,e,Wl,Ul));export{zl as m};
