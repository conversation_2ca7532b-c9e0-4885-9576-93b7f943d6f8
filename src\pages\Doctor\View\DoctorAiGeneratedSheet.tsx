import React, { useState, useEffect } from "react";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";

interface CheatsheetSubsection {
  subtitle: string;
  content: string;
}

interface CheatsheetSection {
  title: string;
  isExpanded: boolean;
  subsections: CheatsheetSubsection[];
}

const ChevronIcon = ({ isExpanded }: { isExpanded: boolean }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    className={`transform transition-transform ${
      isExpanded ? "rotate-180" : ""
    }`}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.29289 7.29289C5.68342 6.90237 6.31658 6.90237 6.70711 7.29289L10 10.5858L13.2929 7.29289C13.6834 6.90237 14.3166 6.90237 14.7071 7.29289C15.0976 7.68342 15.0976 8.31658 14.7071 8.70711L10.7071 12.7071C10.3166 13.0976 9.68342 13.0976 9.29289 12.7071L5.29289 8.70711C4.90237 8.31658 4.90237 7.68342 5.29289 7.29289Z"
      fill="#1F2937"
    />
  </svg>
);

const DoctorAiGeneratedSheet: React.FC = () => {
  const { sdk } = useSDK();
  const { tokenExpireError } = useContexts();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sections, setSections] = useState<CheatsheetSection[]>([]);

  const fetchCheatsheet = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await sdk.getDoctorCheatsheet();

      if (response.error) {
        throw new Error(response.message || "Failed to fetch cheatsheet");
      }

      // Validate response data
      if (!response.data || !Array.isArray(response.data.sections)) {
        throw new Error("Invalid response format from server");
      }

      // Transform the sections to include isExpanded property
      const transformedSections = response.data.sections.map(
        (section: any, index: number) => ({
          ...section,
          isExpanded: index === 0, // First section expanded by default
          subsections: Array.isArray(section.subsections)
            ? section.subsections
            : [],
        })
      );

      setSections(transformedSections);
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || err.message || "An error occurred";
      setError(errorMessage);
      tokenExpireError(errorMessage);
      // Set default sections in case of error
      setSections([
        {
          title: "Background Information",
          isExpanded: true,
          subsections: [
            {
              subtitle: "Error Loading Data",
              content:
                "Unable to load cheatsheet data. Please try again later.",
            },
          ],
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCheatsheet();
  }, []);

  const toggleSection = (index: number) => {
    setSections((prevSections) =>
      prevSections.map((section, i) =>
        i === index ? { ...section, isExpanded: !section.isExpanded } : section
      )
    );
  };

  if (loading) {
    return (
      <div className="flex items-center h-screen w-full justify-center">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-[95%] mx-auto bg-white rounded-lg shadow-sm">
          <div className="flex flex-col items-center justify-center h-64">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={fetchCheatsheet}
              className="bg-violet-600 text-white px-4 py-2 rounded-lg hover:bg-violet-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-[95%] mx-auto bg-white rounded-lg shadow-sm">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                AI-Generated Cheatsheet
              </h1>
              <p className="text-sm text-gray-600">
                Auto-generated guidelines from psychologist notes
              </p>
            </div>
            <div className="flex items-center text-sm text-blue-600">
              <span className="flex items-center gap-1">
                <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                Generated: {new Date().toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Sections */}
        <div className="divide-y divide-gray-200">
          {sections.map((section, index) => (
            <div key={index} className="border-gray-200">
              <button
                onClick={() => toggleSection(index)}
                className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <span className="text-base font-medium text-gray-900">
                    {section.title}
                  </span>
                </div>
                <ChevronIcon isExpanded={section.isExpanded} />
              </button>

              {section.isExpanded && (
                <div className="px-6 pb-4">
                  <div className="space-y-4">
                    {section.subsections.map((subsection, subIndex) => (
                      <div key={subIndex} className="space-y-2">
                        <h3 className="text-sm font-medium text-gray-500">
                          {subsection.subtitle}
                        </h3>
                        <div className="bg-gray-50 p-3 rounded-md">
                          <p className="text-sm text-gray-900">
                            {subsection.content}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-between">
          <button
            className="text-gray-600 hover:text-gray-900 font-medium flex items-center gap-2"
            onClick={() => window.history.back()}
          >
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path
                d="M10 16L4 10L10 4"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M4 10H16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Back to Report
          </button>
          <button
            onClick={fetchCheatsheet}
            className="bg-violet-600 text-white px-4 py-2 rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2"
          >
            Refresh
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path
                d="M4 10H16M16 10L10 4M16 10L10 16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DoctorAiGeneratedSheet;
