import{j as s}from"./@react-google-maps/api-5b2d83cc.js";import{r as y}from"./vendor-489b60f1.js";import{u as M}from"./react-hook-form-7e42b371.js";import{o as $}from"./yup-fe85ba88.js";import{c as z,a as v,b as O}from"./yup-5d8330af.js";import{I as R}from"./index-ec6e151a.js";import{a as U,u as F,T as k,c as I,S as T}from"./index-95f0e460.js";import{M as q}from"./index-235b3e94.js";import{M as C}from"./index-c6183aa1.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const ns=({isOpen:m=!1,onClose:r})=>{var u,f;const S=new T,{sdk:n}=U(),{tokenExpireError:d,showToast:i}=F(),N=z({password:v().required().min(8).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,{message:"Password must be at least 8 characters long, Contain at least one uppercase letter, One lowercase letter, One number, one number and one special character."}),confirm:v().oneOf([O("password")],"Passwords do not match")}).required(),{register:c,handleSubmit:P,setError:p,formState:{errors:e}}=M({resolver:$(N)}),E=y.useCallback(async a=>{var w,x,g,h,b;try{if(a!=null&&a.password&&((w=a==null?void 0:a.password)==null?void 0:w.length)>0){const t=await n.updatePassword(a.password);if(!t.error)i("Password Updated",5e3,k.SUCCESS);else if(t.validation){const o=Object.keys(t.validation);for(let l=0;l<o.length;l++){const j=o[l];p(j,{type:"manual",message:t.validation[j]})}}}}catch(t){const o=(g=(x=t==null?void 0:t.response)==null?void 0:x.data)!=null&&g.message?(b=(h=t==null?void 0:t.response)==null?void 0:h.data)==null?void 0:b.message:t==null?void 0:t.message;i(o,5e3,k.ERROR),d(o)}},[n,p,i,d]);return s.jsx(s.Fragment,{children:s.jsx(I,{isOpen:m,modalCloseClick:()=>r&&r(),title:"Edit Password",modalHeader:!0,classes:{modalDialog:"!grid grid-rows-[auto_90%] !gap-0 !w-full !px-0 md:!w-[25.125rem] !h-fit grid-rows-[auto_auto]",modalContent:"!z-10 !px-0 overflow-hidden !pt-0 !mt-0",modal:"h-full"},children:s.jsx("div",{className:"h-full min-h-full p-5",children:m?s.jsx(s.Fragment,{children:s.jsxs("form",{className:"relative mx-auto grid h-full max-h-full min-h-full w-full grow grid-cols-1 grid-rows-[75%_20%] gap-5 rounded text-start !font-inter leading-snug tracking-wide",onSubmit:P(E),children:[s.jsxs("div",{className:"flex w-full flex-col gap-5",children:[s.jsxs("div",{children:[s.jsx(C,{name:"password",label:"Password",className:"grow",register:c}),e&&(e==null?void 0:e.password)&&s.jsx("p",{className:"text-field-error m-auto mt-2 text-[.8rem] italic text-red-500",children:(u=e==null?void 0:e.password)==null?void 0:u.message})]}),s.jsxs("div",{children:[s.jsx(C,{name:"confirm",label:"Confirm Password",className:"grow",register:c}),e&&(e==null?void 0:e.confirm)&&s.jsx("p",{className:"text-field-error m-auto mt-2 text-[.8rem] italic text-red-500",children:S.Capitalize((f=e==null?void 0:e.confirm)==null?void 0:f.message,{separator:" "})})]})]}),s.jsxs("div",{className:"relative flex w-full gap-5",children:[s.jsx(q,{onClick:()=>r(),className:"!grow border !bg-white !text-black !shadow",children:"Cancel"}),s.jsx(R,{type:"submit",className:"!grow px-4 py-2 font-bold capitalize text-white bg-[black] !shadow",children:"Update Password"})]})]})}):null})})})};export{ns as default};
