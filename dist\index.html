
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/png"
      href="/icons/maskable-icon-512x512.png"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>Wireframe Tool V5 | Manaknight Digital</title>
    <!-- <script>window.global = window;</script>
    <meta http-equiv="Content-Security-Policy"
      content="script-src 'sha256-pQY0fuQAnnVQH5nQfjo80rzGkQzeN3JeAtAJ+1KcD4k=' 'self' blob: https://cdnjs.cloudflare.com;"> -->
    <!-- <meta http-equiv="Content-Security-Policy"
      content="script-src 'sha256-/AO8vAagk08SqUGxY96ci/dGyTDsuoetPOJYMn7sc+E=' 'self' blob:"> -->

      <!-- Hotjar Tracking Code for Site 5128711 (name missing) -->
<script>
  (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:5128711,hjsv:6};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
    <script type="module" crossorigin src="/assets/index-95f0e460.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-489b60f1.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-google-maps/api-5b2d83cc.js">
    <link rel="modulepreload" crossorigin href="/assets/html2pdf.js-82514bbc.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-15af3249.js">
    <link rel="modulepreload" crossorigin href="/assets/react-loading-skeleton-b6c0da5e.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-254ba2e2.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-78cc4a29.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-209b94d8.js">
    <link rel="modulepreload" crossorigin href="/assets/@tanstack/react-query-dc4b6186.js">
    <link rel="modulepreload" crossorigin href="/assets/@hotjar/browser-b90112fa.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-82da594a.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-a38012c9.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-2c021b6b.js">
    <link rel="stylesheet" href="/assets/index-d4c6ce51.css">
    <link rel="stylesheet" href="/assets/inter-07b8bb97.css">
    <link rel="stylesheet" href="/assets/roboto-mono-281fa166.css">
  <link rel="manifest" href="/manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script></head>
  <body style="width:100% !important;">
    <div id="root"></div>
    <!-- <div id="portal"></div> -->
    
    
  </body>
</html>
