import React from "react";

interface ITestProps {
  title: string;
  description?: string;
  onAction?: () => void;
}

const Test: React.FC<ITestProps> = ({ title, description, onAction }) => {
  return (
    <div className="p-4 rounded-lg shadow-md bg-white">
      <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
      {description && <p className="mt-2 text-gray-600">{description}</p>}
      {onAction && (
        <button
          onClick={onAction}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Click Me
        </button>
      )}
    </div>
  );
};

export default Test;
