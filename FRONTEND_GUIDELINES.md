# Frontend Development Guidelines

## Directory Structure

```
src/
├── pages/
│   ├── Admin/              # Admin portal pages
│   │   ├── Add/           # Creation pages
│   │   ├── Auth/          # Authentication pages
│   │   ├── Edit/          # Modification pages
│   │   ├── List/          # List/Table views
│   │   └── View/          # Detail views
│   └── User/              # User portal pages
│       ├── Add/
│       ├── Auth/
│       ├── Edit/
│       ├── List/
│       └── View/
├── components/            # Shared components
├── hooks/                # Custom hooks
├── context/              # React context
└── utils/               # Utilities
```

## Naming Conventions

### Files

- Pages: `[PageName]Page.tsx`
- Components: `[ComponentName].tsx`
- Types: `[Name]Types.ts`
- Utils: `[Name]Utils.ts`
- Constants: `[Name]Constants.ts`

### TypeScript

- Interfaces: `I[Name]`
- Types: `T[Name]`
- Enums: `[Name]Enum`

### Components

- React Components: PascalCase
- Props Interface: `I[ComponentName]Props`
- Hooks: `use[HookName]`

## Page Template Structure

```typescript
import React from 'react';
import { useContexts } from '@/hooks/useContexts';
import { I[PageName]Props } from './types';

interface I[PageName]PageProps {
  // props definition
}

const [PageName]Page: React.FC<I[PageName]PageProps> = () => {
  // component implementation
};

export default [PageName]Page;
```

## Routing Patterns

- Admin Routes: `/admin/[feature]/[action]`
- User Routes: `/user/[feature]/[action]`
- List Views: `/[portal]/[feature]/list`
- Add Views: `/[portal]/[feature]/add`
- Edit Views: `/[portal]/[feature]/edit/:id`
- View Details: `/[portal]/[feature]/view/:id`

## Component Guidelines

1. Use functional components
2. Implement proper TypeScript types
3. Follow existing component patterns
4. Use Tailwind CSS for styling
5. Keep components focused and single-responsibility
6. Use proper prop naming and typing

## State Management

1. Use React Query for API calls
2. Use Context for global state
3. Use local state for component-specific data
4. Follow existing patterns for auth and user state

## Styling Guidelines

1. Use Tailwind CSS classes
2. Follow responsive design patterns
3. Use existing color schemes and variables
4. Maintain consistent spacing
5. Follow accessibility guidelines

## Code Quality Checklist

- [ ] TypeScript types are properly defined
- [ ] Component follows naming conventions
- [ ] Proper error handling implemented
- [ ] Loading states handled
- [ ] Responsive design implemented
- [ ] Props are properly typed
- [ ] Code is properly formatted
- [ ] No unused imports or variables
- [ ] Follows existing patterns

## Quick Commands

1. Create new page:

   ```bash
   npx generate-page --name [name] --type [type] --portal [portal]
   ```

2. Create new component:
   ```bash
   npx generate-component --name [name]
   ```

## Common Patterns

1. Loading States:

   ```typescript
   const [isLoading, setIsLoading] = useState(false);
   // Use MkdLoader component for loading states
   ```

2. Error Handling:

   ```typescript
   const [error, setError] = useState<string | null>(null);
   // Use error boundary or error state handling
   ```

3. Form Handling:

   ```typescript
   // Use MKDForm component for forms
   // Implement proper validation
   ```

4. API Calls:
   ```typescript
   // Use React Query for data fetching
   // Follow existing API patterns
   ```

## Important Notes

1. Always follow existing patterns in the codebase
2. Maintain consistency with current implementation
3. Use existing shared components
4. Follow the directory structure
5. Implement proper error handling
6. Use TypeScript strictly
7. Keep components focused and maintainable
