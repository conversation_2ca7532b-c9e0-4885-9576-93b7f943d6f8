import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{a as e}from"./html2pdf.js-82514bbc.js";import{r as p}from"./vendor-489b60f1.js";import{E as a}from"./index-95f0e460.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const s=p.lazy(()=>e(()=>import("./EditWireframeTab-5b93aaa7.js"),["assets/EditWireframeTab-5b93aaa7.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"])),h=({handleClick:o,activeTab:i})=>t.jsx("div",{className:"flex items-center gap-x-5 border-b border-t text-sm border-gray-300 bg-white p-2 px-7 font-medium text-[#8D8D8D]",children:Object.keys(a).map((r,m)=>t.jsx(s,{activeTab:i,tab:r,handleClick:o},`${r}_${m}`))});export{h as default};
