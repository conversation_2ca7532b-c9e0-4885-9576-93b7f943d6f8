import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{S as x}from"./index-95f0e460.js";import{r as a}from"./vendor-489b60f1.js";import{a as b}from"./index.esm-1e38c052.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./react-icons-fe0a0adf.js";const g=({title:e,isOpen:l,zIndex:m,children:n,page:d="",modalHeader:p,modalCloseClick:f,disableCancel:c=!1,classes:o={modal:"h-full",modalDialog:"h-[90%]",modalContent:""}})=>{const s=a.useRef(null),u=new x;return a.useEffect(()=>{const i=document.querySelectorAll("body, .scrollable-container");return l?i.forEach(r=>{r.style.overflow="hidden"}):i.forEach(r=>{r.style.overflow="auto"}),()=>{i.forEach(r=>{r.style.overflow="auto"})}},[l]),t.jsx("div",{ref:s,style:{zIndex:m??999999999999,backgroundColor:"rgba(0, 0, 0, 0.5)"},className:`fixed bottom-0 left-0 right-0 top-0 flex w-full scale-0 items-center justify-center bg-[#00000099] p-[1.5rem] backdrop-blur-sm transition-all ${l?"scale-100":"scale-0"} ${o==null?void 0:o.modal}`,children:t.jsxs("div",{className:`border-primary-black border ${d==="ManagePermissionAddRole"?"w-fit":"w-[80%]"} bg-white relative overflow-auto rounded-lg pb-5 shadow ${o==null?void 0:o.modalDialog}`,children:[p&&t.jsxs("div",{style:{zIndex:1},className:"bg-white sticky inset-x-0 top-0 m-auto flex w-full justify-between border-b px-5 py-4",children:[t.jsx("div",{className:"font-iowan text-center text-[1.25rem] font-[700] capitalize leading-[1.5rem] tracking-[-1.5%]",children:["string"].includes(typeof e)?u.Capitalize(e,{separator:" "}):e}),c?null:t.jsx("button",{type:"button",className:"modal-close cursor-pointer",onClick:f,children:t.jsx(b,{className:"text-xl"})})]}),t.jsx("div",{className:`-z-10 mt-4 px-5 ${o==null?void 0:o.modalContent}`,children:n})]})})},B=a.memo(g);export{B as Modal};
