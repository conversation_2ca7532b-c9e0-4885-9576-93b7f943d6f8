import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as i,d as I}from"./vendor-489b60f1.js";import{u as _,E as R}from"./@tiptap/react-8d1cdd44.js";import{S as P}from"./@tiptap/starter-kit-8dc7719e.js";import{T as $}from"./@tiptap/extension-text-align-4d325d88.js";import{U as D}from"./@tiptap/extension-underline-5fc56973.js";import{T as U}from"./@tiptap/extension-table-a2bc9598.js";import{T as Z}from"./@tiptap/extension-table-row-159ab625.js";import{T as F}from"./@tiptap/extension-table-cell-b57d510e.js";import{T as O}from"./@tiptap/extension-table-header-412f6f9c.js";import{H as V}from"./@tiptap/extension-highlight-1b3a19b9.js";import{a as W,b as z}from"./index-95f0e460.js";import{f as J}from"./date-fns-66ee9ebe.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const K=({editor:t})=>{var o,r;return i.useState(!1),i.useState(!1),t?e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex items-center gap-2 border-b px-2 py-2 bg-white",children:[e.jsx("button",{type:"button","aria-label":"Bold","aria-pressed":t.isActive("bold"),className:`px-2 py-1 rounded ${t.isActive("bold")?"font-bold text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBold().run(),children:e.jsx("b",{children:"B"})}),e.jsx("button",{type:"button","aria-label":"Italic","aria-pressed":t.isActive("italic"),className:`px-2 py-1 rounded ${t.isActive("italic")?"italic text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleItalic().run(),children:e.jsx("i",{children:"I"})}),e.jsx("button",{type:"button","aria-label":"Underline","aria-pressed":t.isActive("underline"),className:`px-2 py-1 rounded ${t.isActive("underline")?"underline text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>{var d,x;return(x=(d=t.chain().focus()).toggleUnderline)==null?void 0:x.call(d).run()},disabled:!((r=(o=t.can()).toggleUnderline)!=null&&r.call(o)),children:e.jsx("u",{children:"U"})}),e.jsx("button",{type:"button","aria-label":"Bulleted List","aria-pressed":t.isActive("bulletList"),className:`px-2 py-1 rounded ${t.isActive("bulletList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBulletList().run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("circle",{cx:"4",cy:"5",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"9",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"13",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"12",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Numbered List","aria-pressed":t.isActive("orderedList"),className:`px-2 py-1 rounded ${t.isActive("orderedList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleOrderedList().run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("text",{x:"2",y:"7",fontSize:"6",fill:"currentColor",children:"1."}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("text",{x:"2",y:"13",fontSize:"6",fill:"currentColor",children:"2."}),e.jsx("rect",{x:"7",y:"10",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("span",{className:"mx-2 border-l h-6"}),e.jsx("button",{type:"button","aria-label":"Align Left","aria-pressed":t.isActive({textAlign:"left"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"left"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("left").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Center","aria-pressed":t.isActive({textAlign:"center"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"center"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("center").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"5",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Right","aria-pressed":t.isActive({textAlign:"right"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"right"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("right").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Justify","aria-pressed":t.isActive({textAlign:"justify"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"justify"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("justify").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})})]}),e.jsxs("div",{className:"flex items-center justify-between px-2 py-2",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{type:"button","aria-label":"Undo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>t.chain().focus().undo().run(),children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M7 4L3 8L7 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Undo"]}),e.jsxs("button",{type:"button","aria-label":"Redo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>t.chain().focus().redo().run(),children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M11 4L15 8L11 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Redo"]})]}),e.jsx("div",{className:"flex gap-6 items-center",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-blue-100 border border-blue-300"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"Original AI Text"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-gray-200 border border-gray-400"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"New Changes"})]})]})})]})]}):null},G=({onClose:t,reportSectionId:o})=>{const{sdk:r}=W(),[d,x]=i.useState([]),[C,v]=i.useState(!0),[N,u]=i.useState(!1),[f,m]=i.useState(null),[h,p]=i.useState("");i.useEffect(()=>{(async()=>{try{v(!0);const n=await r.getSectionComments({report_section_id:o});if(n.error)throw new Error(n.message||"Failed to fetch comments");x(n.data)}catch(n){m(n instanceof Error?n.message:"Failed to load comments")}finally{v(!1)}})()},[r,o]);const y=async()=>{if(h.trim())try{u(!0),m(null);const l=localStorage.getItem("user");if(!l)throw new Error("User ID not found");const n=await r.addSectionComment({report_section_id:o,user_id:l,text:h.trim()});if(n.error)throw new Error(n.message||"Failed to add comment");const j=await r.getSectionComments({report_section_id:o});if(j.error)throw new Error(j.message||"Failed to fetch updated comments");x(j.data),p("")}catch(l){m(l instanceof Error?l.message:"Failed to add comment")}finally{u(!1)}};return e.jsx("div",{className:"absolute bottom-[50px] left-0 z-50 flex items-center justify-center bg-black bg-opacity-30",children:e.jsxs("div",{className:"flex flex-col flex-shrink-0 justify-center items-start gap-4 p-4 w-96 h-[512px] rounded-lg border-0 border-gray-200 bg-white shadow-xl relative",children:[e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h2",{className:"text-gray-800 font-['Inter'] font-medium leading-6",children:"Comments"}),e.jsx("button",{onClick:t,className:"flex justify-center items-center w-3 h-4","aria-label":"Close comments",children:e.jsx("svg",{width:12,height:16,viewBox:"0 0 12 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z",fill:"#9CA3AF"})})})]}),e.jsx("div",{className:"flex flex-col gap-4 w-[22rem] h-[17.5rem] overflow-y-auto",children:C?e.jsx("div",{className:"flex justify-center items-center h-full",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):f?e.jsx("div",{className:"text-red-600 text-center p-4",children:f}):d.length===0?e.jsx("div",{className:"text-gray-500 text-center p-4",children:"No comments yet"}):d.map(l=>e.jsxs("div",{className:"flex flex-col gap-2 p-4 rounded-lg bg-gray-50",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-300",children:l.user.avatar&&e.jsx("img",{src:l.user.avatar,alt:l.user.name,className:"w-full h-full rounded-full object-cover"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"text-gray-800 font-['Inter'] font-medium",children:l.user.name}),e.jsxs("div",{className:"text-gray-500 font-['Inter'] text-sm",children:["Generated on:"," ",J(new Date(l.created_at),"MMM d, yyyy")]})]})]}),e.jsx("p",{className:"text-gray-600 font-['Inter'] leading-normal",children:l.text})]},l.id))}),e.jsxs("div",{className:"flex flex-col gap-2 w-full",children:[e.jsx("textarea",{value:h,onChange:l=>p(l.target.value),placeholder:"Add your comment...",className:"p-3 h-24 rounded-lg border border-gray-200 bg-white text-gray-600 font-['Inter'] leading-6 resize-none focus:outline-none focus:border-gray-300"}),e.jsx("button",{onClick:y,disabled:!h.trim()||N,className:"flex justify-center items-center gap-2 px-4 py-2 rounded-lg bg-gray-800 text-white disabled:opacity-50 disabled:cursor-not-allowed",children:N?e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):e.jsxs(e.Fragment,{children:[e.jsxs("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{clipPath:"url(#clip0_1_1221)",children:e.jsx("path",{d:"M15.5657 0.175119C15.8813 0.393869 16.047 0.771994 15.9876 1.15012L13.9876 14.1501C13.9407 14.4532 13.7563 14.7189 13.4876 14.8689C13.2188 15.0189 12.897 15.0376 12.6126 14.9189L8.87508 13.3657L6.73446 15.6814C6.45633 15.9845 6.01883 16.0845 5.63446 15.9345C5.25008 15.7845 5.00008 15.4126 5.00008 15.0001V12.3876C5.00008 12.2626 5.04696 12.1439 5.13133 12.0532L10.3688 6.33762C10.5501 6.14074 10.5438 5.83762 10.3563 5.65012C10.1688 5.46262 9.86571 5.45012 9.66883 5.62824L3.31258 11.2751L0.553206 9.89387C0.221956 9.72824 0.00945635 9.39699 8.13452e-05 9.02824C-0.00929365 8.65949 0.184456 8.31574 0.503206 8.13137L14.5032 0.131369C14.8376 -0.0592555 15.2501 -0.0405055 15.5657 0.175119Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_1221",children:e.jsx("path",{d:"M0 0H16V16H0V0Z",fill:"white"})})})]}),"Send Comment"]})})]})]})})},ve=()=>{var B,H;const[t,o]=i.useState([]),[r,d]=i.useState(0),[x,C]=i.useState(!1),[v,N]=i.useState(!0),[u,f]=i.useState(null),[m,h]=i.useState(""),p=I(),[y,l]=i.useState(!1),n=i.useRef(null),[j,L]=i.useState(!1);i.useEffect(()=>{(async()=>{var a;try{const c=localStorage.getItem("reportId");if(!c)throw new Error("No report ID found");const g=await new z().getReportSections(c);if(g.error)throw new Error(g.message||"Failed to fetch report data");if(!g.sections||!Array.isArray(g.sections))throw new Error("Invalid response format");const E=g.sections.sort((T,M)=>(T.order||0)-(M.order||0));o(E),h(((a=E[r])==null?void 0:a.body)||""),f(null)}catch(c){f(c instanceof Error?c.message:"An error occurred while fetching the report"),o([])}finally{N(!1)}})()},[]);const b=_({extensions:[P.configure({heading:{levels:[1,2,3]},bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}}),D,$.configure({types:["heading","paragraph"]}),U.configure({resizable:!0}),Z,F,O,V.configure({multicolor:!0,HTMLAttributes:{class:"bg-gray-200"}})],content:((B=t[r])==null?void 0:B.body)||"",onUpdate:({editor:s,transaction:a})=>{if(s.getHTML(),m&&a.docChanged){const{from:c,to:w}=s.state.selection;s.chain().focus().setTextSelection({from:c,to:w}).setHighlight({color:"#D1D5DB"}).run()}},onBlur:({editor:s})=>{const a=s.getHTML();o(c=>c.map((w,g)=>g===r?{...w,body:a}:w))},editorProps:{attributes:{class:"prose prose-sm max-w-none min-h-[400px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",spellCheck:"true","aria-label":`Edit ${((H=t[r])==null?void 0:H.title)||""}`}},parseOptions:{preserveWhitespace:"full"}});i.useEffect(()=>{var s;if(b&&t[r]){b.commands.setContent(t[r].body||"",!1);const a=localStorage.getItem("reportSections");if(a){const c=JSON.parse(a);h(((s=c[r])==null?void 0:s.body)||"")}}},[r,b,t]);const k=i.useCallback(s=>{s>=0&&s<t.length&&d(s)},[t.length]);i.useEffect(()=>{l(!1);const s=n.current;if(!s)return;if(s.scrollTop=0,s.scrollHeight<=s.clientHeight+1){l(!0);return}const a=()=>{s.scrollTop+s.clientHeight>=s.scrollHeight-10&&l(!0)};return s.addEventListener("scroll",a),()=>s.removeEventListener("scroll",a)},[r,t.length]);const S=s=>{if(!y){L(!0),setTimeout(()=>L(!1),3e3);return}k(s)},A=()=>{if(!y){L(!0),setTimeout(()=>L(!1),3e3);return}r===t.length-1?(localStorage.setItem("reportSections",JSON.stringify(t)),p("/member/review-changes")):k(r+1)};return v?e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsx("div",{className:"w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):u?e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsxs("div",{className:"text-red-600",children:["Error loading report: ",u]})}):t.length?e.jsxs("div",{className:"flex flex-col flex-shrink-0 items-center pb-[396px] w-full h-[1440px] border-0 border-gray-200 bg-gray-50",children:[j&&e.jsx("div",{className:"fixed top-3 left-1/2 -translate-x-1/2 bg-yellow-100 text-yellow-800 px-6 py-3 rounded shadow-lg z-50 border border-yellow-300",children:"You must read and verify the entire section first before proceeding to the next one!"}),e.jsx("div",{className:"flex flex-shrink-0 justify-center items-center p-6 w-[1184px] h-[1036px] border-0 border-gray-200 bg-black/0",children:e.jsxs("div",{className:"flex flex-col flex-shrink-0 justify-center items-start gap-6 w-[1136px] h-[988px] border-0 border-gray-200 bg-black/0",children:[e.jsxs("header",{className:"flex flex-shrink-0 justify-between items-center p-4 w-full h-[4.5rem] rounded-lg border-0 border-gray-200 bg-white",children:[e.jsx("h1",{className:"flex justify-center items-center  h-8 text-gray-800 font-['Inter'] text-2xl leading-[normal]",children:"Document Section Editor"}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("button",{type:"button",className:"flex items-center gap-2 pb-[0.4375rem] pr-[1.125rem] pt-2 pl-4 h-10 rounded-md border-0 border-gray-200 bg-gray-800 text-white",onClick:A,children:[e.jsx("svg",{width:13,height:16,viewBox:"0 0 13 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:e.jsx("g",{clipPath:"url(#clip0_1_619)",children:e.jsx("path",{d:"M6.875 0C5.56875 0 4.45625 0.834375 4.04688 2H2.875C1.77188 2 0.875 2.89687 0.875 4V14C0.875 15.1031 1.77188 16 2.875 16H10.875C11.9781 16 12.875 15.1031 12.875 14V4C12.875 2.89687 11.9781 2 10.875 2H9.70312C9.29375 0.834375 8.18125 0 6.875 0Z",fill:"white"})})}),"Proceed to Next Step"]})})]}),e.jsxs("div",{className:"w-full rounded-lg bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4 border-b",children:[e.jsxs("button",{className:"flex items-center gap-2 text-gray-600 disabled:opacity-40",onClick:()=>p(-1),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"12",viewBox:"0 0 14 12",fill:"none",children:e.jsx("path",{d:"M0.293701 5.29414C-0.0969238 5.68477 -0.0969238 6.31914 0.293701 6.70977L5.2937 11.7098C5.68433 12.1004 6.3187 12.1004 6.70933 11.7098C7.09995 11.3191 7.09995 10.6848 6.70933 10.2941L3.41245 7.00039H13C13.5531 7.00039 14 6.55352 14 6.00039C14 5.44727 13.5531 5.00039 13 5.00039H3.41558L6.7062 1.70664C7.09683 1.31602 7.09683 0.681641 6.7062 0.291016C6.31558 -0.0996094 5.6812 -0.0996094 5.29058 0.291016L0.290576 5.29102L0.293701 5.29414Z",fill:"#4B5563"})}),e.jsx("span",{className:"hidden sm:inline",children:"Back"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("button",{className:"flex border rounded-full p-3 items-center gap-2 text-gray-600 disabled:opacity-40 justify-end",onClick:()=>S(r-1),disabled:r===0,"aria-label":"Previous section",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"12",viewBox:"0 0 14 12",fill:"none",children:e.jsx("path",{d:"M0.293701 5.29414C-0.0969238 5.68477 -0.0969238 6.31914 0.293701 6.70977L5.2937 11.7098C5.68433 12.1004 6.3187 12.1004 6.70933 11.7098C7.09995 11.3191 7.09995 10.6848 6.70933 10.2941L3.41245 7.00039H13C13.5531 7.00039 14 6.55352 14 6.00039C14 5.44727 13.5531 5.00039 13 5.00039H3.41558L6.7062 1.70664C7.09683 1.31602 7.09683 0.681641 6.7062 0.291016C6.31558 -0.0996094 5.6812 -0.0996094 5.29058 0.291016L0.290576 5.29102L0.293701 5.29414Z",fill:"#4B5563"})})}),e.jsx("span",{className:"text-xl min-w-[300px] mx-4 font-medium text-gray-900 text-center flex-1",children:t[r].title}),e.jsx("button",{className:"flex border rounded-full p-3 items-center gap-2 text-gray-600 disabled:opacity-40 justify-end",onClick:()=>S(r+1),disabled:r===t.length-1,"aria-label":"Next section",children:e.jsx("svg",{style:{transform:"rotate(180deg)"},xmlns:"http://www.w3.org/2000/svg",width:"14",height:"12",viewBox:"0 0 14 12",fill:"none",children:e.jsx("path",{d:"M0.293701 5.29414C-0.0969238 5.68477 -0.0969238 6.31914 0.293701 6.70977L5.2937 11.7098C5.68433 12.1004 6.3187 12.1004 6.70933 11.7098C7.09995 11.3191 7.09995 10.6848 6.70933 10.2941L3.41245 7.00039H13C13.5531 7.00039 14 6.55352 14 6.00039C14 5.44727 13.5531 5.00039 13 5.00039H3.41558L6.7062 1.70664C7.09683 1.31602 7.09683 0.681641 6.7062 0.291016C6.31558 -0.0996094 5.6812 -0.0996094 5.29058 0.291016L0.290576 5.29102L0.293701 5.29414Z",fill:"#4B5563"})})})]}),e.jsx("div",{})]}),e.jsx("div",{className:"flex-1 mx-4",children:e.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:e.jsx("div",{className:"bg-gray-800 h-2 rounded-full",style:{width:`${(r+1)/t.length*100}%`}})})}),e.jsxs("div",{className:"flex items-center justify-between px-6 py-2",children:[e.jsxs("span",{className:"text-xs text-gray-500",children:["Section ",r+1," of ",t.length]}),e.jsxs("span",{className:"text-xs text-gray-500",children:[Math.round((r+1)/t.length*100),"% Reviewed"]})]})]}),e.jsx("div",{className:"flex justify-center items-center p-8 w-full rounded-lg bg-white",children:e.jsxs("div",{className:"w-[90%] h-full",children:[e.jsx(K,{editor:b}),e.jsx("div",{className:"w-full h-[811px] overflow-y-auto",ref:n,children:e.jsx(R,{editor:b})})]})}),e.jsxs("div",{className:"flex relative justify-between items-center w-full",children:[e.jsx("div",{className:"flex gap-4",children:e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 text-gray-600",onClick:()=>C(!0),children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("g",{clipPath:"url(#clip0_1_1187)",children:e.jsx("path",{d:"M16 7.5C16 11.0906 12.4187 14 7.99995 14C6.84058 14 5.74057 13.8 4.74682 13.4406C4.37495 13.7125 3.7687 14.0844 3.04995 14.3969C2.29995 14.7219 1.39683 15 0.49995 15C0.296825 15 0.115575 14.8781 0.0374502 14.6906C-0.0406748 14.5031 0.00307515 14.2906 0.1437 14.1469L0.153075 14.1375C0.16245 14.1281 0.17495 14.1156 0.1937 14.0938C0.228075 14.0562 0.2812 13.9969 0.346825 13.9156C0.47495 13.7594 0.646825 13.5281 0.821825 13.2406C1.13433 12.7219 1.4312 12.0406 1.49058 11.275C0.553075 10.2125 -4.98406e-05 8.90938 -4.98406e-05 7.5C-4.98406e-05 3.90937 3.5812 1 7.99995 1C12.4187 1 16 3.90937 16 7.5Z",fill:"#4B5563"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_1187",children:e.jsx("path",{d:"M0 0H16V16H0V0Z",fill:"white"})})})]}),"Add Comment"]})}),e.jsx("button",{className:"flex items-center gap-2 px-6 py-2 bg-gray-800 text-white rounded-md disabled:opacity-50",onClick:A,disabled:!y,children:r===t.length-1?"Proceed to Psychometrist Review":"Continue to Next Section"}),x&&e.jsx(G,{onClose:()=>C(!1),reportSectionId:t[r].id.toString()})]})]})})]}):e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsx("div",{className:"text-gray-600",children:"Report not found"})})};export{ve as default};
