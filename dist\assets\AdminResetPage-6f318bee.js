import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{c as k,a as i,b as N}from"./yup-5d8330af.js";import{r as P,d as L,L as S}from"./vendor-489b60f1.js";import{a as q,u as E,L as n}from"./index-95f0e460.js";import{u as I}from"./react-hook-form-7e42b371.js";import{o as R}from"./yup-fe85ba88.js";import{I as C}from"./index-ec6e151a.js";import{M}from"./index-e9605eb4.js";import{M as f}from"./index-c6183aa1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@hookform/resolvers-6b9dee20.js";const oe=({})=>{const{sdk:g}=q(),{tokenExpireError:h,showToast:m}=E(),[d,r]=P.useState(!1),w=window.location.search,l=new URLSearchParams(w).get("token")||"",b=k({code:i().required(),password:i().required(),confirmPassword:i().oneOf([N("password"),void 0],"Passwords must match")}).required(),j=L(),{register:t,handleSubmit:v,setError:c,formState:{errors:a}}=I({resolver:R(b)}),y=async p=>{if(!l)return m("Invalid token");try{r(!0);const s=await g.reset(l,p.code,p.password);if(!s.error)m("Password Reset"),setTimeout(()=>{j("/admin/login")},2e3);else if(s.validation){const u=Object.keys(s.validation);for(let o=0;o<u.length;o++){const x=u[o];c(x,{type:"manual",message:s.validation[x]})}}r(!1)}catch(s){r(!1),c("code",{type:"manual",message:s.response.data.message?s.response.data.message:s.message}),h(s.response.data.message?s.response.data.message:s.message)}};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[e.jsxs("form",{onSubmit:v(y),className:"mb-4 mt-8 rounded bg-white px-8 pb-8 pt-6 shadow-md ",children:[e.jsx("div",{className:"mb-4",children:e.jsx(n,{children:e.jsx(M,{name:"code",type:"text",errors:a,register:t,label:"Code",required:!0,placeholder:"Enter Code"})})}),e.jsx("div",{className:"mb-6",children:e.jsx(n,{children:e.jsx(f,{required:!0,name:"password",errors:a,label:"Password",register:t})})}),e.jsx("div",{className:"mb-6",children:e.jsx(n,{children:e.jsx(f,{required:!0,errors:a,register:t,name:"confirmPassword",label:"Confirm Password"})})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C,{className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:d,disabled:d,children:"Reset Password"}),e.jsx(S,{className:"inline-block align-baseline text-sm font-bold text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),e.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{oe as default};
