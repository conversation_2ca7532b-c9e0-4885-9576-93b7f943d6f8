import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{R as h,k as f,d as b}from"./vendor-489b60f1.js";import{u as w}from"./react-hook-form-7e42b371.js";import{o as j}from"./yup-fe85ba88.js";import{c as y,a as N}from"./yup-5d8330af.js";import{a as v,u as S}from"./index-95f0e460.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const H=()=>{var a,i;const{sdk:m}=v(),{showToast:l}=S(),[n,t]=h.useState(!1),s=f();b();const c=y({email:N().email().required()}).required(),{register:p,handleSubmit:u,setError:d,formState:{errors:r}}=w({resolver:j(c)}),g=async x=>{try{t(!0),(await m.magicLoginAttempt(x.email,(s==null?void 0:s.role)??"")).error||(t(!1),l("Please check your mail to complete login attempt"))}catch(o){t(!1),console.log("Error",o),d("email",{type:"manual",message:o.message})}};return e.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[e.jsxs("form",{onSubmit:u(g),className:"px-8 pt-6 pb-8 mt-8 mb-4 bg-white rounded shadow-md",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...p("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(a=r.email)!=null&&a.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(i=r.email)==null?void 0:i.message})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("button",{type:"submit",className:"px-4 py-2 font-bold text-white bg-blue-500 rounded focus:shadow-outline hover:bg-blue-700 focus:outline-none",children:[n?"Attempting Log In...":"Sign In"," "]})})]}),e.jsxs("p",{className:"text-xs text-center text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{H as default};
