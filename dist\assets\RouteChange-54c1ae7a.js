import{j as m}from"./@react-google-maps/api-5b2d83cc.js";import{r as i,L as s}from"./vendor-489b60f1.js";import{L as c}from"./index-95f0e460.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const v=({onClose:t,options:e=[{name:"",route:""}]})=>m.jsx(c,{children:m.jsx(i.Fragment,{children:m.jsx("div",{className:"grid grid-cols-2 flex-wrap gap-2 text-center",children:e==null?void 0:e.map((r,a)=>m.jsx(s,{onClick:t,to:r==null?void 0:r.route,className:"cursor-pointer",children:r==null?void 0:r.name},a))})})});export{v as RouteChange,v as default};
