import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import"./index-95f0e460.js";import{I as p}from"./index-ec6e151a.js";import"./vendor-489b60f1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const x=({className:t="",fill:s="#A8A8A8",onClick:i})=>e.jsx("svg",{className:`${t}`,onClick:i,width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M19.4059 16.6337C20.198 17.4257 20.198 18.6139 19.4059 19.4059C19.0099 19.802 18.5149 20 18.0198 20C17.5248 20 17.0297 19.802 16.6337 19.4059L10 12.7723L3.36634 19.4059C2.9703 19.802 2.47525 20 1.9802 20C1.48515 20 0.990099 19.802 0.594059 19.4059C-0.19802 18.6139 -0.19802 17.4257 0.594059 16.6337L7.22772 10L0.594059 3.36634C-0.19802 2.57426 -0.19802 1.38614 0.594059 0.594059C1.38614 -0.19802 2.57426 -0.19802 3.36634 0.594059L10 7.22772L16.6337 0.594059C17.4257 -0.19802 18.6139 -0.19802 19.4059 0.594059C20.198 1.38614 20.198 2.57426 19.4059 3.36634L12.7723 10L19.4059 16.6337Z",fill:s})}),z=({open:t,closeModalFunction:s,actionHandler:i,message:o,title:m,messageClasses:d,titleClasses:a,acceptText:l="",rejectText:c="Cancel",loading:r=!1,allowAccept:n=!0})=>e.jsx("aside",{className:`fixed inset-0 m-auto flex items-center justify-center backdrop-blur-sm transition-all ${t?"scale-100":"scale-0"}`,style:{backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:91e3},children:e.jsxs("section",{className:"flex w-[25rem] min-w-[25rem] flex-col  gap-6 rounded-md bg-white px-6 py-6",children:[e.jsxs("div",{className:"flex justify-between ",children:[e.jsx("div",{children:m?e.jsx("div",{className:` ${a}`,children:m}):null}),e.jsx("button",{disabled:r,onClick:s,children:e.jsx(x,{})})]}),o?e.jsx("div",{className:`text-[#667085] ${d}`,children:o}):null,e.jsxs("div",{className:"flex w-full justify-between gap-2 font-medium uppercase leading-[1.5rem] text-[base]",children:[e.jsx(p,{disabled:r,loading:r,className:`${n?"flex !w-1/2":"hidden w-0"}  h-[2.75rem] uppercase`,onClick:i,children:l&&l.toLowerCase()!=="yes"?`Yes ${l}`:"Yes"}),e.jsx("button",{disabled:r,className:`flex h-[2.75rem] items-center justify-center rounded-md  border border-[#d8dae5] text-[#667085] ${n?" w-1/2":"grow"}`,onClick:s,children:c})]})]})});export{z as default};
