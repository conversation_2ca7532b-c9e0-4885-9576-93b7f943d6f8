{"projectRules": {"structure": {"pages": {"baseDir": "src/pages", "portalTypes": ["Admin", "User"], "subDirectories": ["Add", "<PERSON><PERSON>", "Edit", "List", "View"], "pageNaming": "[PageName]Page.tsx", "componentNaming": "[PageName][ComponentType].tsx"}, "components": {"baseDir": "src/components", "naming": "PascalCase", "fileStructure": {"component": "[ComponentName]/index.tsx", "types": "[ComponentName]/types.ts", "utils": "[ComponentName]/utils.ts", "constants": "[ComponentName]/constants.ts"}}}, "coding": {"typescript": {"strict": true, "interfaceNaming": "I[Name]", "typeNaming": "T[Name]", "enumNaming": "[Name]Enum"}, "react": {"componentType": "functional", "propsInterface": "I[ComponentName]Props", "hooks": {"naming": "use[HookN<PERSON>]", "location": "src/hooks"}}, "styling": {"framework": "tailwindcss", "classNaming": {"container": "[component]-container", "wrapper": "[component]-wrapper", "item": "[component]-item"}}}, "routing": {"patterns": {"admin": {"base": "/admin", "auth": "/admin/auth/*", "feature": "/admin/:feature/*"}, "user": {"base": "/user", "auth": "/user/auth/*", "feature": "/user/:feature/*"}}, "naming": {"list": "/list", "add": "/add", "edit": "/edit/:id", "view": "/view/:id"}}, "fileTemplates": {"page": {"imports": ["import React from 'react'", "import { useContexts } from '@/hooks/useContexts'", "import { I[PageName]Props } from './types'"], "structure": ["interface", "component", "export"]}, "component": {"imports": ["import React from 'react'", "import { I[ComponentName]Props } from './types'"], "structure": ["interface", "component", "export"]}}}, "commands": {"createPage": {"template": "npx generate-page --name [name] --type [type] --portal [portal]", "types": ["add", "edit", "list", "view"]}, "createComponent": {"template": "npx generate-component --name [name]", "location": "src/components"}}}