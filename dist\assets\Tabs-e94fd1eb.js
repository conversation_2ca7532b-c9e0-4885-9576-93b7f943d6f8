import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{S as p}from"./index-95f0e460.js";import"./vendor-489b60f1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const f=({tab:r,setView:m,view:o,viewsMap:e,tabClassName:l})=>{var s,n,a,c;const i=new p;return t.jsxs("div",{onClick:()=>m(r),className:`flex h-full w-fit min-w-[6.8125rem] cursor-pointer items-center justify-center gap-1 border-b-2 py-2 ${l} ${o===((s=e[r])==null?void 0:s.value)?"border-black text-black":""}`,children:[i.Capitalize(r,{separator:" "}),(n=e[r])!=null&&n.hasCount?t.jsx("div",{className:`flex h-[1.25rem] w-[1.25rem] items-center justify-center rounded-full font-inter text-[.75rem] font-[600] leading-[1rem] ${o===((a=e[r])==null?void 0:a.value)?"bg-black text-white":"bg-weak-100 text-sub-500"}`,children:(c=e[r])==null?void 0:c.count}):null]})},E=({tabs:r=[],setView:m,view:o,viewsMap:e,tabClassName:l})=>t.jsx("div",{className:"scrollbar-hide flex !h-[3rem] w-full max-w-full overflow-x-auto shadow-sm shadow-soft-200 md:overflow-x-clip ",children:t.jsx("div",{className:"flex h-full shrink-0 items-center justify-between gap-[1.5rem]",children:r.map(i=>t.jsx(f,{tab:i,view:o,viewsMap:e,tabClassName:l,setView:s=>{m(s)}},i))})});export{E as default};
