import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{c as R,a as h}from"./yup-5d8330af.js";import{r as k,u as M,d as P,L as I}from"./vendor-489b60f1.js";import{u as F}from"./react-hook-form-7e42b371.js";import{a as q,u as A,L as g,R as D,T as w}from"./index-95f0e460.js";import{o as U}from"./yup-fe85ba88.js";import{I as O}from"./index-ec6e151a.js";import{M as T}from"./index-e9605eb4.js";import{M as _}from"./index-c6183aa1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@hookform/resolvers-6b9dee20.js";const ue=({role:j=D.SUPER_ADMIN})=>{const{sdk:y}=q(),{authDispatch:v,showToast:i}=A(),[r,t]=k.useState(!1),b=M(),S=new URLSearchParams(b.search).get("redirect_uri"),N=P(),E=R({email:h().email().required(),password:h().required()}).required(),{register:l,handleSubmit:C,setError:m,formState:{errors:o}}=F({resolver:U(E)}),L=async n=>{var d,c,u,x;try{t(!0);const s=await y.register(n.email,n.password,j);if(!s.error)v({type:"LOGIN",payload:s}),i("Succesfully Registered",4e3,w.SUCCESS),N(S??"/admin/dashboard");else if(t(!1),s.validation){const p=Object.keys(s.validation);for(let a=0;a<p.length;a++){const f=p[a];m(f,{type:"manual",message:s.validation[f]})}}}catch(s){t(!1),i(s==null?void 0:s.message,4e3,w.ERROR),m("email",{type:"manual",message:(c=(d=s==null?void 0:s.response)==null?void 0:d.data)!=null&&c.message?(x=(u=s==null?void 0:s.response)==null?void 0:u.data)==null?void 0:x.message:s==null?void 0:s.message})}};return e.jsx("div",{className:"m-auto flex justify-center items-center w-full h-full max-h-full min-h-full",children:e.jsxs("div",{className:"my-12 flex w-[90%] flex-col items-center rounded-lg border  border-[#a8a8a8] p-6 shadow-md md:w-[22.8125rem]",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",children:[e.jsx("path",{d:"M12.5 2C10.0147 2 8 4.01472 8 6.5C8 8.98528 10.0147 11 12.5 11C14.9853 11 17 8.98528 17 6.5C17 4.01472 14.9853 2 12.5 2Z",fill:"#4F46E5"}),e.jsx("path",{d:"M12.5004 12.5C8.3271 12.5 5.27345 15.2936 4.4402 19.0013C4.19057 20.112 5.10014 21 6.09882 21H18.902C19.9007 21 20.8102 20.112 20.5606 19.0013C19.7274 15.2936 16.6737 12.5 12.5004 12.5Z",fill:"#4F46E5"})]}),e.jsx("div",{className:"my-2 text-xl font-semibold text-[#262626]",children:"Register"}),e.jsxs("form",{className:"w-full max-w-full",onSubmit:C(L),children:[e.jsx("div",{className:"mb-6 flex flex-col text-sm",children:e.jsx(g,{children:e.jsx(T,{type:"email",label:"Email",name:"email",placeholder:"<EMAIL>",register:l,errors:o})})}),e.jsx("div",{className:"flex flex-col text-sm",children:e.jsx(g,{children:e.jsx(_,{required:!0,name:"password",label:"Password",errors:o,register:l})})}),e.jsx(O,{type:"submit",className:"my-12 flex w-full items-center justify-center rounded-md bg-[#4F46E5] py-2 tracking-wide text-white  outline-none focus:outline-none",loading:r,disabled:r,children:"Register"}),e.jsx("div",{children:e.jsxs("h3",{className:"text-center text-sm normal-case text-gray-800",children:["Already have an account?"," ",e.jsxs(I,{className:"my-text-gradient mb-8 self-end text-sm font-semibold",to:"/admin/login",children:["Login"," "]})," "]})})]}),e.jsx("div",{className:"oauth flex w-full max-w-md grow flex-col gap-4 px-6 text-[#344054]",children:e.jsxs("p",{className:"h-10 text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})})]})})};export{ue as default};
