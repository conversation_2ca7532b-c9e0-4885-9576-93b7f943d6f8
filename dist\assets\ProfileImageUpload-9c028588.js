import{j as r}from"./@react-google-maps/api-5b2d83cc.js";import{T as n}from"./index-fe4acb22.js";import{r as m}from"./vendor-489b60f1.js";import{a as d}from"./html2pdf.js-82514bbc.js";import{L as l}from"./index-95f0e460.js";import{M as x}from"./index-235b3e94.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const f=m.lazy(()=>d(()=>import("./index-95f0e460.js").then(t=>t.i),["assets/index-95f0e460.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-d4c6ce51.css","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),G=({image:t="",className:p="",title:c="Image Upload",onUpload:s,name:o="photo",multiple:a=!1})=>{const e=m.useRef(null);return r.jsx("div",{className:`grid w-full grid-cols-1 gap-[1.5rem] ${p}`,children:r.jsxs("div",{className:"flex w-full items-start justify-between gap-5 ",children:[t&&r.jsx("div",{className:"p-2",children:r.jsx(l,{children:r.jsx(f,{image:t,className:"h-[5rem] w-[5rem]"})})}),r.jsxs("div",{className:"grid grow grid-cols-1 gap-2",children:[r.jsx(n,{className:"!border-0 !p-0 !shadow-none ",children:c}),r.jsx("p",{className:"font-inter text-[.875rem] font-[400] leading-[1.25rem]  tracking-[-0.6%] text-black",children:"Min 400x400px, PNG or JPEG"}),r.jsx(l,{children:r.jsxs(x,{className:"!shadow !w-fit  !bg-white font-[700] !text-black",showPlus:!1,onClick:()=>{var i;(i=e==null?void 0:e.current)==null||i.click()},children:[r.jsx("input",{hidden:!0,ref:e,type:"file",id:o,name:o,accept:".jpg,.jpeg,.png",multiple:a,style:{display:"none"},onChange:i=>{s==null||s(o,i.target,a),e.current.value=""}}),"Upload"]})})]})]})})};export{G as default};
