import{R as T,r as I,a as ua,b as sa}from"./vendor-489b60f1.js";import{_ as rr,a as w,h as gt}from"./@mantine/core-d9c5c65c.js";import{b as mt,c as ca,a as da,d as pa}from"./redux-3b07d581.js";import{i as va}from"./@craftjs/core-ae02137e.js";function Fr(e,r){return Fr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},Fr(e,r)}function Jt(e,r){e.prototype=Object.create(r.prototype),e.prototype.constructor=e,Fr(e,r)}var Xt=T.createContext(null);function fa(e){e()}var Qt=fa,ga=function(r){return Qt=r},ma=function(){return Qt};function ba(){var e=ma(),r=null,t=null;return{clear:function(){r=null,t=null},notify:function(){e(function(){for(var i=r;i;)i.callback(),i=i.next})},get:function(){for(var i=[],a=r;a;)i.push(a),a=a.next;return i},subscribe:function(i){var a=!0,o=t={callback:i,next:null,prev:t};return o.prev?o.prev.next=o:r=o,function(){!a||r===null||(a=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:r=o.next)}}}}var bt={notify:function(){},get:function(){return[]}};function Zt(e,r){var t,n=bt;function i(s){return u(),n.subscribe(s)}function a(){n.notify()}function o(){p.onStateChange&&p.onStateChange()}function l(){return!!t}function u(){t||(t=r?r.addNestedSub(o):e.subscribe(o),n=ba())}function d(){t&&(t(),t=void 0,n.clear(),n=bt)}var p={addNestedSub:i,notifyNestedSubs:a,handleChangeWrapper:o,isSubscribed:l,trySubscribe:u,tryUnsubscribe:d,getListeners:function(){return n}};return p}var _t=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?I.useLayoutEffect:I.useEffect;function ha(e){var r=e.store,t=e.context,n=e.children,i=I.useMemo(function(){var l=Zt(r);return{store:r,subscription:l}},[r]),a=I.useMemo(function(){return r.getState()},[r]);_t(function(){var l=i.subscription;return l.onStateChange=l.notifyNestedSubs,l.trySubscribe(),a!==r.getState()&&l.notifyNestedSubs(),function(){l.tryUnsubscribe(),l.onStateChange=null}},[i,a]);var o=t||Xt;return T.createElement(o.Provider,{value:i},n)}var en={exports:{}},O={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cr=60103,dr=60106,Me=60107,Le=60108,Fe=60114,Ge=60109,We=60110,Ue=60112,ke=60113,Vr=60120,He=60115,qe=60116,rn=60121,tn=60122,nn=60117,an=60129,on=60131;if(typeof Symbol=="function"&&Symbol.for){var W=Symbol.for;cr=W("react.element"),dr=W("react.portal"),Me=W("react.fragment"),Le=W("react.strict_mode"),Fe=W("react.profiler"),Ge=W("react.provider"),We=W("react.context"),Ue=W("react.forward_ref"),ke=W("react.suspense"),Vr=W("react.suspense_list"),He=W("react.memo"),qe=W("react.lazy"),rn=W("react.block"),tn=W("react.server.block"),nn=W("react.fundamental"),an=W("react.debug_trace_mode"),on=W("react.legacy_hidden")}function ee(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case cr:switch(e=e.type,e){case Me:case Fe:case Le:case ke:case Vr:return e;default:switch(e=e&&e.$$typeof,e){case We:case Ue:case qe:case He:case Ge:return e;default:return r}}case dr:return r}}}var ya=Ge,Da=cr,xa=Ue,Ia=Me,Ca=qe,Sa=He,wa=dr,Ea=Fe,Pa=Le,Aa=ke;O.ContextConsumer=We;O.ContextProvider=ya;O.Element=Da;O.ForwardRef=xa;O.Fragment=Ia;O.Lazy=Ca;O.Memo=Sa;O.Portal=wa;O.Profiler=Ea;O.StrictMode=Pa;O.Suspense=Aa;O.isAsyncMode=function(){return!1};O.isConcurrentMode=function(){return!1};O.isContextConsumer=function(e){return ee(e)===We};O.isContextProvider=function(e){return ee(e)===Ge};O.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===cr};O.isForwardRef=function(e){return ee(e)===Ue};O.isFragment=function(e){return ee(e)===Me};O.isLazy=function(e){return ee(e)===qe};O.isMemo=function(e){return ee(e)===He};O.isPortal=function(e){return ee(e)===dr};O.isProfiler=function(e){return ee(e)===Fe};O.isStrictMode=function(e){return ee(e)===Le};O.isSuspense=function(e){return ee(e)===ke};O.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Me||e===Fe||e===an||e===Le||e===ke||e===Vr||e===on||typeof e=="object"&&e!==null&&(e.$$typeof===qe||e.$$typeof===He||e.$$typeof===Ge||e.$$typeof===We||e.$$typeof===Ue||e.$$typeof===nn||e.$$typeof===rn||e[0]===tn)};O.typeOf=ee;en.exports=O;var Ba=en.exports,Ra=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],Oa=["reactReduxForwardedRef"],Ta=[],Na=[null,null];function Ma(e,r){var t=e[1];return[r.payload,t+1]}function ht(e,r,t){_t(function(){return e.apply(void 0,r)},t)}function La(e,r,t,n,i,a,o){e.current=n,r.current=i,t.current=!1,a.current&&(a.current=null,o())}function Fa(e,r,t,n,i,a,o,l,u,d){if(e){var p=!1,s=null,c=function(){if(!p){var g=r.getState(),m,b;try{m=n(g,i.current)}catch(y){b=y,s=y}b||(s=null),m===a.current?o.current||u():(a.current=m,l.current=m,o.current=!0,d({type:"STORE_UPDATED",payload:{error:b}}))}};t.onStateChange=c,t.trySubscribe(),c();var v=function(){if(p=!0,t.tryUnsubscribe(),t.onStateChange=null,s)throw s};return v}}var Ga=function(){return[null,0]};function Wa(e,r){r===void 0&&(r={});var t=r,n=t.getDisplayName,i=n===void 0?function(x){return"ConnectAdvanced("+x+")"}:n,a=t.methodName,o=a===void 0?"connectAdvanced":a,l=t.renderCountProp,u=l===void 0?void 0:l,d=t.shouldHandleStateChanges,p=d===void 0?!0:d,s=t.storeKey,c=s===void 0?"store":s;t.withRef;var v=t.forwardRef,f=v===void 0?!1:v,g=t.context,m=g===void 0?Xt:g,b=rr(t,Ra),y=m;return function(D){var E=D.displayName||D.name||"Component",S=i(E),B=w({},b,{getDisplayName:i,methodName:o,renderCountProp:u,shouldHandleStateChanges:p,storeKey:c,displayName:S,wrappedComponentName:E,WrappedComponent:D}),N=b.pure;function P(L){return e(L.dispatch,B)}var M=N?I.useMemo:function(L){return L()};function R(L){var Y=I.useMemo(function(){var Ce=L.reactReduxForwardedRef,Sr=rr(L,Oa);return[L.context,Ce,Sr]},[L]),K=Y[0],pe=Y[1],re=Y[2],ve=I.useMemo(function(){return K&&K.Consumer&&Ba.isContextConsumer(T.createElement(K.Consumer,null))?K:y},[K,y]),Z=I.useContext(ve),ue=!!L.store&&!!L.store.getState&&!!L.store.dispatch;Z&&Z.store;var H=ue?L.store:Z.store,Ie=I.useMemo(function(){return P(H)},[H]),ze=I.useMemo(function(){if(!p)return Na;var Ce=Zt(H,ue?null:Z.subscription),Sr=Ce.notifyNestedSubs.bind(Ce);return[Ce,Sr]},[H,ue,Z]),te=ze[0],je=ze[1],Ye=I.useMemo(function(){return ue?Z:w({},Z,{subscription:te})},[ue,Z,te]),Ke=I.useReducer(Ma,Ta,Ga),yr=Ke[0],fe=yr[0],Dr=Ke[1];if(fe&&fe.error)throw fe.error;var vt=I.useRef(),xr=I.useRef(re),Je=I.useRef(),ft=I.useRef(!1),Ir=M(function(){return Je.current&&re===xr.current?Je.current:Ie(H.getState(),re)},[H,fe,re]);ht(La,[xr,vt,ft,re,Ir,Je,je]),ht(Fa,[p,H,te,Ie,xr,vt,ft,Je,je,Dr],[H,te,Ie]);var Cr=I.useMemo(function(){return T.createElement(D,w({},Ir,{ref:pe}))},[pe,D,Ir]),la=I.useMemo(function(){return p?T.createElement(ve.Provider,{value:Ye},Cr):Cr},[ve,Cr,Ye]);return la}var z=N?T.memo(R):R;if(z.WrappedComponent=D,z.displayName=R.displayName=S,f){var j=T.forwardRef(function(Y,K){return T.createElement(z,w({},Y,{reactReduxForwardedRef:K}))});return j.displayName=S,j.WrappedComponent=D,gt(j,D)}return gt(z,D)}}function yt(e,r){return e===r?e!==0||r!==0||1/e===1/r:e!==e&&r!==r}function wr(e,r){if(yt(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;var t=Object.keys(e),n=Object.keys(r);if(t.length!==n.length)return!1;for(var i=0;i<t.length;i++)if(!Object.prototype.hasOwnProperty.call(r,t[i])||!yt(e[t[i]],r[t[i]]))return!1;return!0}function Ua(e,r){var t={},n=function(o){var l=e[o];typeof l=="function"&&(t[o]=function(){return r(l.apply(void 0,arguments))})};for(var i in e)n(i);return t}function $r(e){return function(t,n){var i=e(t,n);function a(){return i}return a.dependsOnOwnProps=!1,a}}function Dt(e){return e.dependsOnOwnProps!==null&&e.dependsOnOwnProps!==void 0?!!e.dependsOnOwnProps:e.length!==1}function ln(e,r){return function(n,i){i.displayName;var a=function(l,u){return a.dependsOnOwnProps?a.mapToProps(l,u):a.mapToProps(l)};return a.dependsOnOwnProps=!0,a.mapToProps=function(l,u){a.mapToProps=e,a.dependsOnOwnProps=Dt(e);var d=a(l,u);return typeof d=="function"&&(a.mapToProps=d,a.dependsOnOwnProps=Dt(d),d=a(l,u)),d},a}}function ka(e){return typeof e=="function"?ln(e):void 0}function Ha(e){return e?void 0:$r(function(r){return{dispatch:r}})}function qa(e){return e&&typeof e=="object"?$r(function(r){return Ua(e,r)}):void 0}const Va=[ka,Ha,qa];function $a(e){return typeof e=="function"?ln(e):void 0}function za(e){return e?void 0:$r(function(){return{}})}const ja=[$a,za];function Ya(e,r,t){return w({},t,e,r)}function Ka(e){return function(t,n){n.displayName;var i=n.pure,a=n.areMergedPropsEqual,o=!1,l;return function(d,p,s){var c=e(d,p,s);return o?(!i||!a(c,l))&&(l=c):(o=!0,l=c),l}}}function Ja(e){return typeof e=="function"?Ka(e):void 0}function Xa(e){return e?void 0:function(){return Ya}}const Qa=[Ja,Xa];var Za=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function _a(e,r,t,n){return function(a,o){return t(e(a,o),r(n,o),o)}}function ei(e,r,t,n,i){var a=i.areStatesEqual,o=i.areOwnPropsEqual,l=i.areStatePropsEqual,u=!1,d,p,s,c,v;function f(x,D){return d=x,p=D,s=e(d,p),c=r(n,p),v=t(s,c,p),u=!0,v}function g(){return s=e(d,p),r.dependsOnOwnProps&&(c=r(n,p)),v=t(s,c,p),v}function m(){return e.dependsOnOwnProps&&(s=e(d,p)),r.dependsOnOwnProps&&(c=r(n,p)),v=t(s,c,p),v}function b(){var x=e(d,p),D=!l(x,s);return s=x,D&&(v=t(s,c,p)),v}function y(x,D){var E=!o(D,p),S=!a(x,d,D,p);return d=x,p=D,E&&S?g():E?m():S?b():v}return function(D,E){return u?y(D,E):f(D,E)}}function ri(e,r){var t=r.initMapStateToProps,n=r.initMapDispatchToProps,i=r.initMergeProps,a=rr(r,Za),o=t(e,a),l=n(e,a),u=i(e,a),d=a.pure?ei:_a;return d(o,l,u,e,a)}var ti=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function Er(e,r,t){for(var n=r.length-1;n>=0;n--){var i=r[n](e);if(i)return i}return function(a,o){throw new Error("Invalid value of type "+typeof e+" for "+t+" argument when connecting component "+o.wrappedComponentName+".")}}function ni(e,r){return e===r}function ai(e){var r=e===void 0?{}:e,t=r.connectHOC,n=t===void 0?Wa:t,i=r.mapStateToPropsFactories,a=i===void 0?ja:i,o=r.mapDispatchToPropsFactories,l=o===void 0?Va:o,u=r.mergePropsFactories,d=u===void 0?Qa:u,p=r.selectorFactory,s=p===void 0?ri:p;return function(v,f,g,m){m===void 0&&(m={});var b=m,y=b.pure,x=y===void 0?!0:y,D=b.areStatesEqual,E=D===void 0?ni:D,S=b.areOwnPropsEqual,B=S===void 0?wr:S,N=b.areStatePropsEqual,P=N===void 0?wr:N,M=b.areMergedPropsEqual,R=M===void 0?wr:M,z=rr(b,ti),j=Er(v,a,"mapStateToProps"),L=Er(f,l,"mapDispatchToProps"),Y=Er(g,d,"mergeProps");return n(s,w({methodName:"connect",getDisplayName:function(pe){return"Connect("+pe+")"},shouldHandleStateChanges:!!v,initMapStateToProps:j,initMapDispatchToProps:L,initMergeProps:Y,pure:x,areStatesEqual:E,areOwnPropsEqual:B,areStatePropsEqual:P,areMergedPropsEqual:R},z))}}const un=ai();ga(ua.unstable_batchedUpdates);function ii(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}function sn(e,r){var t=I.useState(function(){return{inputs:r,result:e()}})[0],n=I.useRef(!0),i=I.useRef(t),a=n.current||!!(r&&i.current.inputs&&ii(r,i.current.inputs)),o=a?i.current:{inputs:r,result:e()};return I.useEffect(function(){n.current=!1,i.current=o},[o]),o.result}function oi(e,r){return sn(function(){return e},r)}var A=sn,C=oi,_=function(r){var t=r.top,n=r.right,i=r.bottom,a=r.left,o=n-a,l=i-t,u={top:t,right:n,bottom:i,left:a,width:o,height:l,x:a,y:t,center:{x:(n+a)/2,y:(i+t)/2}};return u},zr=function(r,t){return{top:r.top-t.top,left:r.left-t.left,bottom:r.bottom+t.bottom,right:r.right+t.right}},xt=function(r,t){return{top:r.top+t.top,left:r.left+t.left,bottom:r.bottom-t.bottom,right:r.right-t.right}},li=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},Pr={top:0,right:0,bottom:0,left:0},jr=function(r){var t=r.borderBox,n=r.margin,i=n===void 0?Pr:n,a=r.border,o=a===void 0?Pr:a,l=r.padding,u=l===void 0?Pr:l,d=_(zr(t,i)),p=_(xt(t,o)),s=_(xt(p,u));return{marginBox:d,borderBox:_(t),paddingBox:p,contentBox:s,margin:i,border:o,padding:u}},J=function(r){var t=r.slice(0,-2),n=r.slice(-2);if(n!=="px")return 0;var i=Number(t);return isNaN(i)&&va(!1),i},ui=function(){return{x:window.pageXOffset,y:window.pageYOffset}},tr=function(r,t){var n=r.borderBox,i=r.border,a=r.margin,o=r.padding,l=li(n,t);return jr({borderBox:l,border:i,margin:a,padding:o})},nr=function(r,t){return t===void 0&&(t=ui()),tr(r,t)},cn=function(r,t){var n={top:J(t.marginTop),right:J(t.marginRight),bottom:J(t.marginBottom),left:J(t.marginLeft)},i={top:J(t.paddingTop),right:J(t.paddingRight),bottom:J(t.paddingBottom),left:J(t.paddingLeft)},a={top:J(t.borderTopWidth),right:J(t.borderRightWidth),bottom:J(t.borderBottomWidth),left:J(t.borderLeftWidth)};return jr({borderBox:r,margin:n,padding:i,border:a})},dn=function(r){var t=r.getBoundingClientRect(),n=window.getComputedStyle(r);return cn(t,n)},It=Number.isNaN||function(r){return typeof r=="number"&&r!==r};function si(e,r){return!!(e===r||It(e)&&It(r))}function ci(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(!si(e[t],r[t]))return!1;return!0}function F(e,r){r===void 0&&(r=ci);var t,n=[],i,a=!1;function o(){for(var l=[],u=0;u<arguments.length;u++)l[u]=arguments[u];return a&&t===this&&r(l,n)||(i=e.apply(this,l),a=!0,t=this,n=l),i}return o}var di=function(r){var t=[],n=null,i=function(){for(var o=arguments.length,l=new Array(o),u=0;u<o;u++)l[u]=arguments[u];t=l,!n&&(n=requestAnimationFrame(function(){n=null,r.apply(void 0,t)}))};return i.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},i};const Be=di;function pn(e,r){}pn.bind(null,"warn");pn.bind(null,"error");function ne(){}function pi(e,r){return w({},e,{},r)}function X(e,r,t){var n=r.map(function(i){var a=pi(t,i.options);return e.addEventListener(i.eventName,i.fn,a),function(){e.removeEventListener(i.eventName,i.fn,a)}});return function(){n.forEach(function(a){a()})}}var vi="Invariant failed";function ar(e){this.message=e}ar.prototype.toString=function(){return this.message};function h(e,r){if(!e)throw new ar(vi)}var fi=function(e){Jt(r,e);function r(){for(var n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,n.callbacks=null,n.unbind=ne,n.onWindowError=function(l){var u=n.getCallbacks();u.isDragging()&&u.tryAbort();var d=l.error;d instanceof ar&&l.preventDefault()},n.getCallbacks=function(){if(!n.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return n.callbacks},n.setCallbacks=function(l){n.callbacks=l},n}var t=r.prototype;return t.componentDidMount=function(){this.unbind=X(window,[{eventName:"error",fn:this.onWindowError}])},t.componentDidCatch=function(i){if(i instanceof ar){this.setState({});return}throw i},t.componentWillUnmount=function(){this.unbind()},t.render=function(){return this.props.children(this.setCallbacks)},r}(T.Component),gi=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,ir=function(r){return r+1},mi=function(r){return`
  You have lifted an item in position `+ir(r.source.index)+`
`},vn=function(r,t){var n=r.droppableId===t.droppableId,i=ir(r.index),a=ir(t.index);return n?`
      You have moved the item from position `+i+`
      to position `+a+`
    `:`
    You have moved the item from position `+i+`
    in list `+r.droppableId+`
    to list `+t.droppableId+`
    in position `+a+`
  `},fn=function(r,t,n){var i=t.droppableId===n.droppableId;return i?`
      The item `+r+`
      has been combined with `+n.draggableId:`
      The item `+r+`
      in list `+t.droppableId+`
      has been combined with `+n.draggableId+`
      in list `+n.droppableId+`
    `},bi=function(r){var t=r.destination;if(t)return vn(r.source,t);var n=r.combine;return n?fn(r.draggableId,r.source,n):"You are over an area that cannot be dropped on"},Ct=function(r){return`
  The item has returned to its starting position
  of `+ir(r.index)+`
`},hi=function(r){if(r.reason==="CANCEL")return`
      Movement cancelled.
      `+Ct(r.source)+`
    `;var t=r.destination,n=r.combine;return t?`
      You have dropped the item.
      `+vn(r.source,t)+`
    `:n?`
      You have dropped the item.
      `+fn(r.draggableId,r.source,n)+`
    `:`
    The item has been dropped while not over a drop area.
    `+Ct(r.source)+`
  `},er={dragHandleUsageInstructions:gi,onDragStart:mi,onDragUpdate:bi,onDragEnd:hi},G={x:0,y:0},U=function(r,t){return{x:r.x+t.x,y:r.y+t.y}},q=function(r,t){return{x:r.x-t.x,y:r.y-t.y}},ae=function(r,t){return r.x===t.x&&r.y===t.y},ye=function(r){return{x:r.x!==0?-r.x:0,y:r.y!==0?-r.y:0}},de=function(r,t,n){var i;return n===void 0&&(n=0),i={},i[r]=t,i[r==="x"?"y":"x"]=n,i},Re=function(r,t){return Math.sqrt(Math.pow(t.x-r.x,2)+Math.pow(t.y-r.y,2))},St=function(r,t){return Math.min.apply(Math,t.map(function(n){return Re(r,n)}))},gn=function(r){return function(t){return{x:r(t.x),y:r(t.y)}}},yi=function(e,r){var t=_({top:Math.max(r.top,e.top),right:Math.min(r.right,e.right),bottom:Math.min(r.bottom,e.bottom),left:Math.max(r.left,e.left)});return t.width<=0||t.height<=0?null:t},Ve=function(r,t){return{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x}},wt=function(r){return[{x:r.left,y:r.top},{x:r.right,y:r.top},{x:r.left,y:r.bottom},{x:r.right,y:r.bottom}]},Di={top:0,right:0,bottom:0,left:0},xi=function(r,t){return t?Ve(r,t.scroll.diff.displacement):r},Ii=function(r,t,n){if(n&&n.increasedBy){var i;return w({},r,(i={},i[t.end]=r[t.end]+n.increasedBy[t.line],i))}return r},Ci=function(r,t){return t&&t.shouldClipSubject?yi(t.pageMarginBox,r):_(r)},me=function(e){var r=e.page,t=e.withPlaceholder,n=e.axis,i=e.frame,a=xi(r.marginBox,i),o=Ii(a,n,t),l=Ci(o,i);return{page:r,withPlaceholder:t,active:l}},Yr=function(e,r){e.frame||h(!1);var t=e.frame,n=q(r,t.scroll.initial),i=ye(n),a=w({},t,{scroll:{initial:t.scroll.initial,current:r,diff:{value:n,displacement:i},max:t.scroll.max}}),o=me({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:a}),l=w({},e,{frame:a,subject:o});return l};function or(e){return Object.values?Object.values(e):Object.keys(e).map(function(r){return e[r]})}function Kr(e,r){if(e.findIndex)return e.findIndex(r);for(var t=0;t<e.length;t++)if(r(e[t]))return t;return-1}function le(e,r){if(e.find)return e.find(r);var t=Kr(e,r);if(t!==-1)return e[t]}function mn(e){return Array.prototype.slice.call(e)}var bn=F(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),hn=F(function(e){return e.reduce(function(r,t){return r[t.descriptor.id]=t,r},{})}),pr=F(function(e){return or(e)}),Si=F(function(e){return or(e)}),De=F(function(e,r){var t=Si(r).filter(function(n){return e===n.descriptor.droppableId}).sort(function(n,i){return n.descriptor.index-i.descriptor.index});return t});function Jr(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function vr(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var fr=F(function(e,r){return r.filter(function(t){return t.descriptor.id!==e.descriptor.id})}),wi=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,i=e.insideDestination,a=e.previousImpact;if(!n.isCombineEnabled)return null;var o=Jr(a);if(!o)return null;function l(g){var m={type:"COMBINE",combine:{draggableId:g,droppableId:n.descriptor.id}};return w({},a,{at:m})}var u=a.displaced.all,d=u.length?u[0]:null;if(r)return d?l(d):null;var p=fr(t,i);if(!d){if(!p.length)return null;var s=p[p.length-1];return l(s.descriptor.id)}var c=Kr(p,function(g){return g.descriptor.id===d});c===-1&&h(!1);var v=c-1;if(v<0)return null;var f=p[v];return l(f.descriptor.id)},xe=function(e,r){return e.descriptor.droppableId===r.descriptor.id},yn={point:G,value:0},Oe={invisible:{},visible:{},all:[]},Ei={displaced:Oe,displacedBy:yn,at:null},Q=function(e,r){return function(t){return e<=t&&t<=r}},Dn=function(e){var r=Q(e.top,e.bottom),t=Q(e.left,e.right);return function(n){var i=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);if(i)return!0;var a=r(n.top)||r(n.bottom),o=t(n.left)||t(n.right),l=a&&o;if(l)return!0;var u=n.top<e.top&&n.bottom>e.bottom,d=n.left<e.left&&n.right>e.right,p=u&&d;if(p)return!0;var s=u&&o||d&&a;return s}},Pi=function(e){var r=Q(e.top,e.bottom),t=Q(e.left,e.right);return function(n){var i=r(n.top)&&r(n.bottom)&&t(n.left)&&t(n.right);return i}},Xr={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},xn={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},Ai=function(e){return function(r){var t=Q(r.top,r.bottom),n=Q(r.left,r.right);return function(i){return e===Xr?t(i.top)&&t(i.bottom):n(i.left)&&n(i.right)}}},Bi=function(r,t){var n=t.frame?t.frame.scroll.diff.displacement:G;return Ve(r,n)},Ri=function(r,t,n){return t.subject.active?n(t.subject.active)(r):!1},Oi=function(r,t,n){return n(t)(r)},Qr=function(r){var t=r.target,n=r.destination,i=r.viewport,a=r.withDroppableDisplacement,o=r.isVisibleThroughFrameFn,l=a?Bi(t,n):t;return Ri(l,n,o)&&Oi(l,i,o)},Ti=function(r){return Qr(w({},r,{isVisibleThroughFrameFn:Dn}))},In=function(r){return Qr(w({},r,{isVisibleThroughFrameFn:Pi}))},Ni=function(r){return Qr(w({},r,{isVisibleThroughFrameFn:Ai(r.destination.axis)}))},Mi=function(r,t,n){if(typeof n=="boolean")return n;if(!t)return!0;var i=t.invisible,a=t.visible;if(i[r])return!1;var o=a[r];return o?o.shouldAnimate:!0};function Li(e,r){var t=e.page.marginBox,n={top:r.point.y,right:0,bottom:0,left:r.point.x};return _(zr(t,n))}function Te(e){var r=e.afterDragging,t=e.destination,n=e.displacedBy,i=e.viewport,a=e.forceShouldAnimate,o=e.last;return r.reduce(function(u,d){var p=Li(d,n),s=d.descriptor.id;u.all.push(s);var c=Ti({target:p,destination:t,viewport:i,withDroppableDisplacement:!0});if(!c)return u.invisible[d.descriptor.id]=!0,u;var v=Mi(s,o,a),f={draggableId:s,shouldAnimate:v};return u.visible[s]=f,u},{all:[],visible:{},invisible:{}})}function Fi(e,r){if(!e.length)return 0;var t=e[e.length-1].descriptor.index;return r.inHomeList?t:t+1}function Et(e){var r=e.insideDestination,t=e.inHomeList,n=e.displacedBy,i=e.destination,a=Fi(r,{inHomeList:t});return{displaced:Oe,displacedBy:n,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:a}}}}function lr(e){var r=e.draggable,t=e.insideDestination,n=e.destination,i=e.viewport,a=e.displacedBy,o=e.last,l=e.index,u=e.forceShouldAnimate,d=xe(r,n);if(l==null)return Et({insideDestination:t,inHomeList:d,displacedBy:a,destination:n});var p=le(t,function(g){return g.descriptor.index===l});if(!p)return Et({insideDestination:t,inHomeList:d,displacedBy:a,destination:n});var s=fr(r,t),c=t.indexOf(p),v=s.slice(c),f=Te({afterDragging:v,destination:n,displacedBy:a,last:o,viewport:i.frame,forceShouldAnimate:u});return{displaced:f,displacedBy:a,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function oe(e,r){return!!r.effected[e]}var Gi=function(e){var r=e.isMovingForward,t=e.destination,n=e.draggables,i=e.combine,a=e.afterCritical;if(!t.isCombineEnabled)return null;var o=i.draggableId,l=n[o],u=l.descriptor.index,d=oe(o,a);return d?r?u:u-1:r?u+1:u},Wi=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.insideDestination,i=e.location;if(!n.length)return null;var a=i.index,o=r?a+1:a-1,l=n[0].descriptor.index,u=n[n.length-1].descriptor.index,d=t?u:u+1;return o<l||o>d?null:o},Ui=function(e){var r=e.isMovingForward,t=e.isInHomeList,n=e.draggable,i=e.draggables,a=e.destination,o=e.insideDestination,l=e.previousImpact,u=e.viewport,d=e.afterCritical,p=l.at;if(p||h(!1),p.type==="REORDER"){var s=Wi({isMovingForward:r,isInHomeList:t,location:p.destination,insideDestination:o});return s==null?null:lr({draggable:n,insideDestination:o,destination:a,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:s})}var c=Gi({isMovingForward:r,destination:a,displaced:l.displaced,draggables:i,combine:p.combine,afterCritical:d});return c==null?null:lr({draggable:n,insideDestination:o,destination:a,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:c})},ki=function(e){var r=e.displaced,t=e.afterCritical,n=e.combineWith,i=e.displacedBy,a=!!(r.visible[n]||r.invisible[n]);return oe(n,t)?a?G:ye(i.point):a?i.point:G},Hi=function(e){var r=e.afterCritical,t=e.impact,n=e.draggables,i=vr(t);i||h(!1);var a=i.draggableId,o=n[a].page.borderBox.center,l=ki({displaced:t.displaced,afterCritical:r,combineWith:a,displacedBy:t.displacedBy});return U(o,l)},Cn=function(r,t){return t.margin[r.start]+t.borderBox[r.size]/2},qi=function(r,t){return t.margin[r.end]+t.borderBox[r.size]/2},Zr=function(r,t,n){return t[r.crossAxisStart]+n.margin[r.crossAxisStart]+n.borderBox[r.crossAxisSize]/2},Pt=function(r){var t=r.axis,n=r.moveRelativeTo,i=r.isMoving;return de(t.line,n.marginBox[t.end]+Cn(t,i),Zr(t,n.marginBox,i))},At=function(r){var t=r.axis,n=r.moveRelativeTo,i=r.isMoving;return de(t.line,n.marginBox[t.start]-qi(t,i),Zr(t,n.marginBox,i))},Vi=function(r){var t=r.axis,n=r.moveInto,i=r.isMoving;return de(t.line,n.contentBox[t.start]+Cn(t,i),Zr(t,n.contentBox,i))},$i=function(e){var r=e.impact,t=e.draggable,n=e.draggables,i=e.droppable,a=e.afterCritical,o=De(i.descriptor.id,n),l=t.page,u=i.axis;if(!o.length)return Vi({axis:u,moveInto:i.page,isMoving:l});var d=r.displaced,p=r.displacedBy,s=d.all[0];if(s){var c=n[s];if(oe(s,a))return At({axis:u,moveRelativeTo:c.page,isMoving:l});var v=tr(c.page,p.point);return At({axis:u,moveRelativeTo:v,isMoving:l})}var f=o[o.length-1];if(f.descriptor.id===t.descriptor.id)return l.borderBox.center;if(oe(f.descriptor.id,a)){var g=tr(f.page,ye(a.displacedBy.point));return Pt({axis:u,moveRelativeTo:g,isMoving:l})}return Pt({axis:u,moveRelativeTo:f.page,isMoving:l})},Gr=function(e,r){var t=e.frame;return t?U(r,t.scroll.diff.displacement):r},zi=function(r){var t=r.impact,n=r.draggable,i=r.droppable,a=r.draggables,o=r.afterCritical,l=n.page.borderBox.center,u=t.at;return!i||!u?l:u.type==="REORDER"?$i({impact:t,draggable:n,draggables:a,droppable:i,afterCritical:o}):Hi({impact:t,draggables:a,afterCritical:o})},gr=function(e){var r=zi(e),t=e.droppable,n=t?Gr(t,r):r;return n},Sn=function(e,r){var t=q(r,e.scroll.initial),n=ye(t),i=_({top:r.y,bottom:r.y+e.frame.height,left:r.x,right:r.x+e.frame.width}),a={frame:i,scroll:{initial:e.scroll.initial,max:e.scroll.max,current:r,diff:{value:t,displacement:n}}};return a};function Bt(e,r){return e.map(function(t){return r[t]})}function ji(e,r){for(var t=0;t<r.length;t++){var n=r[t].visible[e];if(n)return n}return null}var Yi=function(e){var r=e.impact,t=e.viewport,n=e.destination,i=e.draggables,a=e.maxScrollChange,o=Sn(t,U(t.scroll.current,a)),l=n.frame?Yr(n,U(n.frame.scroll.current,a)):n,u=r.displaced,d=Te({afterDragging:Bt(u.all,i),destination:n,displacedBy:r.displacedBy,viewport:o.frame,last:u,forceShouldAnimate:!1}),p=Te({afterDragging:Bt(u.all,i),destination:l,displacedBy:r.displacedBy,viewport:t.frame,last:u,forceShouldAnimate:!1}),s={},c={},v=[u,d,p];u.all.forEach(function(g){var m=ji(g,v);if(m){c[g]=m;return}s[g]=!0});var f=w({},r,{displaced:{all:u.all,invisible:s,visible:c}});return f},Ki=function(e,r){return U(e.scroll.diff.displacement,r)},_r=function(e){var r=e.pageBorderBoxCenter,t=e.draggable,n=e.viewport,i=Ki(n,r),a=q(i,t.page.borderBox.center);return U(t.client.borderBox.center,a)},wn=function(e){var r=e.draggable,t=e.destination,n=e.newPageBorderBoxCenter,i=e.viewport,a=e.withDroppableDisplacement,o=e.onlyOnMainAxis,l=o===void 0?!1:o,u=q(n,r.page.borderBox.center),d=Ve(r.page.borderBox,u),p={target:d,destination:t,withDroppableDisplacement:a,viewport:i};return l?Ni(p):In(p)},Ji=function(e){var r=e.isMovingForward,t=e.draggable,n=e.destination,i=e.draggables,a=e.previousImpact,o=e.viewport,l=e.previousPageBorderBoxCenter,u=e.previousClientSelection,d=e.afterCritical;if(!n.isEnabled)return null;var p=De(n.descriptor.id,i),s=xe(t,n),c=wi({isMovingForward:r,draggable:t,destination:n,insideDestination:p,previousImpact:a})||Ui({isMovingForward:r,isInHomeList:s,draggable:t,draggables:i,destination:n,insideDestination:p,previousImpact:a,viewport:o,afterCritical:d});if(!c)return null;var v=gr({impact:c,draggable:t,droppable:n,draggables:i,afterCritical:d}),f=wn({draggable:t,destination:n,newPageBorderBoxCenter:v,viewport:o.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});if(f){var g=_r({pageBorderBoxCenter:v,draggable:t,viewport:o});return{clientSelection:g,impact:c,scrollJumpRequest:null}}var m=q(v,l),b=Yi({impact:c,viewport:o,destination:n,draggables:i,maxScrollChange:m});return{clientSelection:u,impact:b,scrollJumpRequest:m}},k=function(r){var t=r.subject.active;return t||h(!1),t},Xi=function(e){var r=e.isMovingForward,t=e.pageBorderBoxCenter,n=e.source,i=e.droppables,a=e.viewport,o=n.subject.active;if(!o)return null;var l=n.axis,u=Q(o[l.start],o[l.end]),d=pr(i).filter(function(s){return s!==n}).filter(function(s){return s.isEnabled}).filter(function(s){return!!s.subject.active}).filter(function(s){return Dn(a.frame)(k(s))}).filter(function(s){var c=k(s);return r?o[l.crossAxisEnd]<c[l.crossAxisEnd]:c[l.crossAxisStart]<o[l.crossAxisStart]}).filter(function(s){var c=k(s),v=Q(c[l.start],c[l.end]);return u(c[l.start])||u(c[l.end])||v(o[l.start])||v(o[l.end])}).sort(function(s,c){var v=k(s)[l.crossAxisStart],f=k(c)[l.crossAxisStart];return r?v-f:f-v}).filter(function(s,c,v){return k(s)[l.crossAxisStart]===k(v[0])[l.crossAxisStart]});if(!d.length)return null;if(d.length===1)return d[0];var p=d.filter(function(s){var c=Q(k(s)[l.start],k(s)[l.end]);return c(t[l.line])});return p.length===1?p[0]:p.length>1?p.sort(function(s,c){return k(s)[l.start]-k(c)[l.start]})[0]:d.sort(function(s,c){var v=St(t,wt(k(s))),f=St(t,wt(k(c)));return v!==f?v-f:k(s)[l.start]-k(c)[l.start]})[0]},Rt=function(r,t){var n=r.page.borderBox.center;return oe(r.descriptor.id,t)?q(n,t.displacedBy.point):n},Qi=function(r,t){var n=r.page.borderBox;return oe(r.descriptor.id,t)?Ve(n,ye(t.displacedBy.point)):n},Zi=function(e){var r=e.pageBorderBoxCenter,t=e.viewport,n=e.destination,i=e.insideDestination,a=e.afterCritical,o=i.filter(function(l){return In({target:Qi(l,a),destination:n,viewport:t.frame,withDroppableDisplacement:!0})}).sort(function(l,u){var d=Re(r,Gr(n,Rt(l,a))),p=Re(r,Gr(n,Rt(u,a)));return d<p?-1:p<d?1:l.descriptor.index-u.descriptor.index});return o[0]||null},$e=F(function(r,t){var n=t[r.line];return{value:n,point:de(r.line,n)}}),_i=function(r,t,n){var i=r.axis;if(r.descriptor.mode==="virtual")return de(i.line,t[i.line]);var a=r.subject.page.contentBox[i.size],o=De(r.descriptor.id,n),l=o.reduce(function(p,s){return p+s.client.marginBox[i.size]},0),u=l+t[i.line],d=u-a;return d<=0?null:de(i.line,d)},En=function(r,t){return w({},r,{scroll:w({},r.scroll,{max:t})})},Pn=function(r,t,n){var i=r.frame;xe(t,r)&&h(!1),r.subject.withPlaceholder&&h(!1);var a=$e(r.axis,t.displaceBy).point,o=_i(r,a,n),l={placeholderSize:a,increasedBy:o,oldFrameMaxScroll:r.frame?r.frame.scroll.max:null};if(!i){var u=me({page:r.subject.page,withPlaceholder:l,axis:r.axis,frame:r.frame});return w({},r,{subject:u})}var d=o?U(i.scroll.max,o):i.scroll.max,p=En(i,d),s=me({page:r.subject.page,withPlaceholder:l,axis:r.axis,frame:p});return w({},r,{subject:s,frame:p})},eo=function(r){var t=r.subject.withPlaceholder;t||h(!1);var n=r.frame;if(!n){var i=me({page:r.subject.page,axis:r.axis,frame:null,withPlaceholder:null});return w({},r,{subject:i})}var a=t.oldFrameMaxScroll;a||h(!1);var o=En(n,a),l=me({page:r.subject.page,axis:r.axis,frame:o,withPlaceholder:null});return w({},r,{subject:l,frame:o})},ro=function(e){var r=e.previousPageBorderBoxCenter,t=e.moveRelativeTo,n=e.insideDestination,i=e.draggable,a=e.draggables,o=e.destination,l=e.viewport,u=e.afterCritical;if(!t){if(n.length)return null;var d={displaced:Oe,displacedBy:yn,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:0}}},p=gr({impact:d,draggable:i,droppable:o,draggables:a,afterCritical:u}),s=xe(i,o)?o:Pn(o,i,a),c=wn({draggable:i,destination:s,newPageBorderBoxCenter:p,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});return c?d:null}var v=r[o.axis.line]<=t.page.borderBox.center[o.axis.line],f=function(){var m=t.descriptor.index;return t.descriptor.id===i.descriptor.id||v?m:m+1}(),g=$e(o.axis,i.displaceBy);return lr({draggable:i,insideDestination:n,destination:o,viewport:l,displacedBy:g,last:Oe,index:f})},to=function(e){var r=e.isMovingForward,t=e.previousPageBorderBoxCenter,n=e.draggable,i=e.isOver,a=e.draggables,o=e.droppables,l=e.viewport,u=e.afterCritical,d=Xi({isMovingForward:r,pageBorderBoxCenter:t,source:i,droppables:o,viewport:l});if(!d)return null;var p=De(d.descriptor.id,a),s=Zi({pageBorderBoxCenter:t,viewport:l,destination:d,insideDestination:p,afterCritical:u}),c=ro({previousPageBorderBoxCenter:t,destination:d,draggable:n,draggables:a,moveRelativeTo:s,insideDestination:p,viewport:l,afterCritical:u});if(!c)return null;var v=gr({impact:c,draggable:n,droppable:d,draggables:a,afterCritical:u}),f=_r({pageBorderBoxCenter:v,draggable:n,viewport:l});return{clientSelection:f,impact:c,scrollJumpRequest:null}},V=function(e){var r=e.at;return r?r.type==="REORDER"?r.destination.droppableId:r.combine.droppableId:null},no=function(r,t){var n=V(r);return n?t[n]:null},ao=function(e){var r=e.state,t=e.type,n=no(r.impact,r.dimensions.droppables),i=!!n,a=r.dimensions.droppables[r.critical.droppable.id],o=n||a,l=o.axis.direction,u=l==="vertical"&&(t==="MOVE_UP"||t==="MOVE_DOWN")||l==="horizontal"&&(t==="MOVE_LEFT"||t==="MOVE_RIGHT");if(u&&!i)return null;var d=t==="MOVE_DOWN"||t==="MOVE_RIGHT",p=r.dimensions.draggables[r.critical.draggable.id],s=r.current.page.borderBoxCenter,c=r.dimensions,v=c.draggables,f=c.droppables;return u?Ji({isMovingForward:d,previousPageBorderBoxCenter:s,draggable:p,destination:o,draggables:v,viewport:r.viewport,previousClientSelection:r.current.client.selection,previousImpact:r.impact,afterCritical:r.afterCritical}):to({isMovingForward:d,previousPageBorderBoxCenter:s,draggable:p,isOver:o,draggables:v,droppables:f,viewport:r.viewport,afterCritical:r.afterCritical})};function ce(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function An(e){var r=Q(e.top,e.bottom),t=Q(e.left,e.right);return function(i){return r(i.y)&&t(i.x)}}function io(e,r){return e.left<r.right&&e.right>r.left&&e.top<r.bottom&&e.bottom>r.top}function oo(e){var r=e.pageBorderBox,t=e.draggable,n=e.candidates,i=t.page.borderBox.center,a=n.map(function(o){var l=o.axis,u=de(o.axis.line,r.center[l.line],o.page.borderBox.center[l.crossAxisLine]);return{id:o.descriptor.id,distance:Re(i,u)}}).sort(function(o,l){return l.distance-o.distance});return a[0]?a[0].id:null}function lo(e){var r=e.pageBorderBox,t=e.draggable,n=e.droppables,i=pr(n).filter(function(a){if(!a.isEnabled)return!1;var o=a.subject.active;if(!o||!io(r,o))return!1;if(An(o)(r.center))return!0;var l=a.axis,u=o.center[l.crossAxisLine],d=r[l.crossAxisStart],p=r[l.crossAxisEnd],s=Q(o[l.crossAxisStart],o[l.crossAxisEnd]),c=s(d),v=s(p);return!c&&!v?!0:c?d<u:p>u});return i.length?i.length===1?i[0].descriptor.id:oo({pageBorderBox:r,draggable:t,candidates:i}):null}var Bn=function(r,t){return _(Ve(r,t))},uo=function(e,r){var t=e.frame;return t?Bn(r,t.scroll.diff.value):r};function Rn(e){var r=e.displaced,t=e.id;return!!(r.visible[t]||r.invisible[t])}function so(e){var r=e.draggable,t=e.closest,n=e.inHomeList;return t?n&&t.descriptor.index>r.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}var co=function(e){var r=e.pageBorderBoxWithDroppableScroll,t=e.draggable,n=e.destination,i=e.insideDestination,a=e.last,o=e.viewport,l=e.afterCritical,u=n.axis,d=$e(n.axis,t.displaceBy),p=d.value,s=r[u.start],c=r[u.end],v=fr(t,i),f=le(v,function(m){var b=m.descriptor.id,y=m.page.borderBox.center[u.line],x=oe(b,l),D=Rn({displaced:a,id:b});return x?D?c<=y:s<y-p:D?c<=y+p:s<y}),g=so({draggable:t,closest:f,inHomeList:xe(t,n)});return lr({draggable:t,insideDestination:i,destination:n,viewport:o,last:a,displacedBy:d,index:g})},po=4,vo=function(e){var r=e.draggable,t=e.pageBorderBoxWithDroppableScroll,n=e.previousImpact,i=e.destination,a=e.insideDestination,o=e.afterCritical;if(!i.isCombineEnabled)return null;var l=i.axis,u=$e(i.axis,r.displaceBy),d=u.value,p=t[l.start],s=t[l.end],c=fr(r,a),v=le(c,function(g){var m=g.descriptor.id,b=g.page.borderBox,y=b[l.size],x=y/po,D=oe(m,o),E=Rn({displaced:n.displaced,id:m});return D?E?s>b[l.start]+x&&s<b[l.end]-x:p>b[l.start]-d+x&&p<b[l.end]-d-x:E?s>b[l.start]+d+x&&s<b[l.end]+d-x:p>b[l.start]+x&&p<b[l.end]-x});if(!v)return null;var f={displacedBy:u,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:v.descriptor.id,droppableId:i.descriptor.id}}};return f},On=function(e){var r=e.pageOffset,t=e.draggable,n=e.draggables,i=e.droppables,a=e.previousImpact,o=e.viewport,l=e.afterCritical,u=Bn(t.page.borderBox,r),d=lo({pageBorderBox:u,draggable:t,droppables:i});if(!d)return Ei;var p=i[d],s=De(p.descriptor.id,n),c=uo(p,u);return vo({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:a,destination:p,insideDestination:s,afterCritical:l})||co({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:p,insideDestination:s,last:a.displaced,viewport:o,afterCritical:l})},et=function(e,r){var t;return w({},e,(t={},t[r.descriptor.id]=r,t))},fo=function(r){var t=r.previousImpact,n=r.impact,i=r.droppables,a=V(t),o=V(n);if(!a||a===o)return i;var l=i[a];if(!l.subject.withPlaceholder)return i;var u=eo(l);return et(i,u)},go=function(e){var r=e.draggable,t=e.draggables,n=e.droppables,i=e.previousImpact,a=e.impact,o=fo({previousImpact:i,impact:a,droppables:n}),l=V(a);if(!l)return o;var u=n[l];if(xe(r,u)||u.subject.withPlaceholder)return o;var d=Pn(u,r,t);return et(o,d)},Pe=function(e){var r=e.state,t=e.clientSelection,n=e.dimensions,i=e.viewport,a=e.impact,o=e.scrollJumpRequest,l=i||r.viewport,u=n||r.dimensions,d=t||r.current.client.selection,p=q(d,r.initial.client.selection),s={offset:p,selection:d,borderBoxCenter:U(r.initial.client.borderBoxCenter,p)},c={selection:U(s.selection,l.scroll.current),borderBoxCenter:U(s.borderBoxCenter,l.scroll.current),offset:U(s.offset,l.scroll.diff.value)},v={client:s,page:c};if(r.phase==="COLLECTING")return w({phase:"COLLECTING"},r,{dimensions:u,viewport:l,current:v});var f=u.draggables[r.critical.draggable.id],g=a||On({pageOffset:c.offset,draggable:f,draggables:u.draggables,droppables:u.droppables,previousImpact:r.impact,viewport:l,afterCritical:r.afterCritical}),m=go({draggable:f,impact:g,previousImpact:r.impact,draggables:u.draggables,droppables:u.droppables}),b=w({},r,{current:v,dimensions:{draggables:u.draggables,droppables:m},impact:g,viewport:l,scrollJumpRequest:o||null,forceShouldAnimate:o?!1:null});return b};function mo(e,r){return e.map(function(t){return r[t]})}var Tn=function(e){var r=e.impact,t=e.viewport,n=e.draggables,i=e.destination,a=e.forceShouldAnimate,o=r.displaced,l=mo(o.all,n),u=Te({afterDragging:l,destination:i,displacedBy:r.displacedBy,viewport:t.frame,forceShouldAnimate:a,last:o});return w({},r,{displaced:u})},Nn=function(e){var r=e.impact,t=e.draggable,n=e.droppable,i=e.draggables,a=e.viewport,o=e.afterCritical,l=gr({impact:r,draggable:t,draggables:i,droppable:n,afterCritical:o});return _r({pageBorderBoxCenter:l,draggable:t,viewport:a})},Mn=function(e){var r=e.state,t=e.dimensions,n=e.viewport;r.movementMode!=="SNAP"&&h(!1);var i=r.impact,a=n||r.viewport,o=t||r.dimensions,l=o.draggables,u=o.droppables,d=l[r.critical.draggable.id],p=V(i);p||h(!1);var s=u[p],c=Tn({impact:i,viewport:a,destination:s,draggables:l}),v=Nn({impact:c,draggable:d,droppable:s,draggables:l,viewport:a,afterCritical:r.afterCritical});return Pe({impact:c,clientSelection:v,state:r,dimensions:o,viewport:a})},bo=function(e){return{index:e.index,droppableId:e.droppableId}},Ln=function(e){var r=e.draggable,t=e.home,n=e.draggables,i=e.viewport,a=$e(t.axis,r.displaceBy),o=De(t.descriptor.id,n),l=o.indexOf(r);l===-1&&h(!1);var u=o.slice(l+1),d=u.reduce(function(v,f){return v[f.descriptor.id]=!0,v},{}),p={inVirtualList:t.descriptor.mode==="virtual",displacedBy:a,effected:d},s=Te({afterDragging:u,destination:t,displacedBy:a,last:null,viewport:i.frame,forceShouldAnimate:!1}),c={displaced:s,displacedBy:a,at:{type:"REORDER",destination:bo(r.descriptor)}};return{impact:c,afterCritical:p}},ho=function(e,r){return{draggables:e.draggables,droppables:et(e.droppables,r)}},yo=function(e){var r=e.draggable,t=e.offset,n=e.initialWindowScroll,i=tr(r.client,t),a=nr(i,n),o=w({},r,{placeholder:w({},r.placeholder,{client:i}),client:i,page:a});return o},Do=function(e){var r=e.frame;return r||h(!1),r},xo=function(e){var r=e.additions,t=e.updatedDroppables,n=e.viewport,i=n.scroll.diff.value;return r.map(function(a){var o=a.descriptor.droppableId,l=t[o],u=Do(l),d=u.scroll.diff.value,p=U(i,d),s=yo({draggable:a,offset:p,initialWindowScroll:n.scroll.initial});return s})},Io=function(e){var r=e.state,t=e.published,n=t.modified.map(function(x){var D=r.dimensions.droppables[x.droppableId],E=Yr(D,x.scroll);return E}),i=w({},r.dimensions.droppables,{},bn(n)),a=hn(xo({additions:t.additions,updatedDroppables:i,viewport:r.viewport})),o=w({},r.dimensions.draggables,{},a);t.removals.forEach(function(x){delete o[x]});var l={droppables:i,draggables:o},u=V(r.impact),d=u?l.droppables[u]:null,p=l.draggables[r.critical.draggable.id],s=l.droppables[r.critical.droppable.id],c=Ln({draggable:p,home:s,draggables:o,viewport:r.viewport}),v=c.impact,f=c.afterCritical,g=d&&d.isCombineEnabled?r.impact:v,m=On({pageOffset:r.current.page.offset,draggable:l.draggables[r.critical.draggable.id],draggables:l.draggables,droppables:l.droppables,previousImpact:g,viewport:r.viewport,afterCritical:f}),b=w({phase:"DRAGGING"},r,{phase:"DRAGGING",impact:m,onLiftImpact:v,dimensions:l,afterCritical:f,forceShouldAnimate:!1});if(r.phase==="COLLECTING")return b;var y=w({phase:"DROP_PENDING"},b,{phase:"DROP_PENDING",reason:r.reason,isWaiting:!1});return y},Wr=function(r){return r.movementMode==="SNAP"},Ar=function(r,t,n){var i=ho(r.dimensions,t);return!Wr(r)||n?Pe({state:r,dimensions:i}):Mn({state:r,dimensions:i})};function Br(e){return e.isDragging&&e.movementMode==="SNAP"?w({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var Ot={phase:"IDLE",completed:null,shouldFlush:!1},Co=function(e,r){if(e===void 0&&(e=Ot),r.type==="FLUSH")return w({},Ot,{shouldFlush:!0});if(r.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&h(!1);var t=r.payload,n=t.critical,i=t.clientSelection,a=t.viewport,o=t.dimensions,l=t.movementMode,u=o.draggables[n.draggable.id],d=o.droppables[n.droppable.id],p={selection:i,borderBoxCenter:u.client.borderBox.center,offset:G},s={client:p,page:{selection:U(p.selection,a.scroll.initial),borderBoxCenter:U(p.selection,a.scroll.initial),offset:U(p.selection,a.scroll.diff.value)}},c=pr(o.droppables).every(function(Dr){return!Dr.isFixedOnPage}),v=Ln({draggable:u,home:d,draggables:o.draggables,viewport:a}),f=v.impact,g=v.afterCritical,m={phase:"DRAGGING",isDragging:!0,critical:n,movementMode:l,dimensions:o,initial:s,current:s,isWindowScrollAllowed:c,impact:f,afterCritical:g,onLiftImpact:f,viewport:a,scrollJumpRequest:null,forceShouldAnimate:null};return m}if(r.type==="COLLECTION_STARTING"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&h(!1);var b=w({phase:"COLLECTING"},e,{phase:"COLLECTING"});return b}if(r.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||h(!1),Io({state:e,published:r.payload});if(r.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;ce(e)||h(!1);var y=r.payload.client;return ae(y,e.current.client.selection)?e:Pe({state:e,clientSelection:y,impact:Wr(e)?e.impact:null})}if(r.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return Br(e);ce(e)||h(!1);var x=r.payload,D=x.id,E=x.newScroll,S=e.dimensions.droppables[D];if(!S)return e;var B=Yr(S,E);return Ar(e,B,!1)}if(r.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;ce(e)||h(!1);var N=r.payload,P=N.id,M=N.isEnabled,R=e.dimensions.droppables[P];R||h(!1),R.isEnabled===M&&h(!1);var z=w({},R,{isEnabled:M});return Ar(e,z,!0)}if(r.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;ce(e)||h(!1);var j=r.payload,L=j.id,Y=j.isCombineEnabled,K=e.dimensions.droppables[L];K||h(!1),K.isCombineEnabled===Y&&h(!1);var pe=w({},K,{isCombineEnabled:Y});return Ar(e,pe,!0)}if(r.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;ce(e)||h(!1),e.isWindowScrollAllowed||h(!1);var re=r.payload.newScroll;if(ae(e.viewport.scroll.current,re))return Br(e);var ve=Sn(e.viewport,re);return Wr(e)?Mn({state:e,viewport:ve}):Pe({state:e,viewport:ve})}if(r.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!ce(e))return e;var Z=r.payload.maxScroll;if(ae(Z,e.viewport.scroll.max))return e;var ue=w({},e.viewport,{scroll:w({},e.viewport.scroll,{max:Z})});return w({phase:"DRAGGING"},e,{viewport:ue})}if(r.type==="MOVE_UP"||r.type==="MOVE_DOWN"||r.type==="MOVE_LEFT"||r.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&h(!1);var H=ao({state:e,type:r.type});return H?Pe({state:e,impact:H.impact,clientSelection:H.clientSelection,scrollJumpRequest:H.scrollJumpRequest}):e}if(r.type==="DROP_PENDING"){var Ie=r.payload.reason;e.phase!=="COLLECTING"&&h(!1);var ze=w({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:Ie});return ze}if(r.type==="DROP_ANIMATE"){var te=r.payload,je=te.completed,Ye=te.dropDuration,Ke=te.newHomeClientOffset;e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||h(!1);var yr={phase:"DROP_ANIMATING",completed:je,dropDuration:Ye,newHomeClientOffset:Ke,dimensions:e.dimensions};return yr}if(r.type==="DROP_COMPLETE"){var fe=r.payload.completed;return{phase:"IDLE",completed:fe,shouldFlush:!1}}return e},So=function(r){return{type:"BEFORE_INITIAL_CAPTURE",payload:r}},wo=function(r){return{type:"LIFT",payload:r}},Eo=function(r){return{type:"INITIAL_PUBLISH",payload:r}},Po=function(r){return{type:"PUBLISH_WHILE_DRAGGING",payload:r}},Ao=function(){return{type:"COLLECTION_STARTING",payload:null}},Bo=function(r){return{type:"UPDATE_DROPPABLE_SCROLL",payload:r}},Ro=function(r){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:r}},Oo=function(r){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:r}},Fn=function(r){return{type:"MOVE",payload:r}},To=function(r){return{type:"MOVE_BY_WINDOW_SCROLL",payload:r}},No=function(r){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:r}},Mo=function(){return{type:"MOVE_UP",payload:null}},Lo=function(){return{type:"MOVE_DOWN",payload:null}},Fo=function(){return{type:"MOVE_RIGHT",payload:null}},Go=function(){return{type:"MOVE_LEFT",payload:null}},rt=function(){return{type:"FLUSH",payload:null}},Wo=function(r){return{type:"DROP_ANIMATE",payload:r}},tt=function(r){return{type:"DROP_COMPLETE",payload:r}},Gn=function(r){return{type:"DROP",payload:r}},Uo=function(r){return{type:"DROP_PENDING",payload:r}},Wn=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},ko=function(e){return function(r){var t=r.getState,n=r.dispatch;return function(i){return function(a){if(a.type!=="LIFT"){i(a);return}var o=a.payload,l=o.id,u=o.clientSelection,d=o.movementMode,p=t();p.phase==="DROP_ANIMATING"&&n(tt({completed:p.completed})),t().phase!=="IDLE"&&h(!1),n(rt()),n(So({draggableId:l,movementMode:d}));var s={shouldPublishImmediately:d==="SNAP"},c={draggableId:l,scrollOptions:s},v=e.startPublishing(c),f=v.critical,g=v.dimensions,m=v.viewport;n(Eo({critical:f,dimensions:g,clientSelection:u,movementMode:d,viewport:m}))}}}},Ho=function(e){return function(){return function(r){return function(t){t.type==="INITIAL_PUBLISH"&&e.dragging(),t.type==="DROP_ANIMATE"&&e.dropping(t.payload.completed.result.reason),(t.type==="FLUSH"||t.type==="DROP_COMPLETE")&&e.resting(),r(t)}}}},nt={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},Ne={opacity:{drop:0,combining:.7},scale:{drop:.75}},at={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},se=at.outOfTheWay+"s "+nt.outOfTheWay,Ae={fluid:"opacity "+se,snap:"transform "+se+", opacity "+se,drop:function(r){var t=r+"s "+nt.drop;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+se,placeholder:"height "+se+", width "+se+", margin "+se},Tt=function(r){return ae(r,G)?null:"translate("+r.x+"px, "+r.y+"px)"},Ur={moveTo:Tt,drop:function(r,t){var n=Tt(r);return n?t?n+" scale("+Ne.scale.drop+")":n:null}},kr=at.minDropTime,Un=at.maxDropTime,qo=Un-kr,Nt=1500,Vo=.6,$o=function(e){var r=e.current,t=e.destination,n=e.reason,i=Re(r,t);if(i<=0)return kr;if(i>=Nt)return Un;var a=i/Nt,o=kr+qo*a,l=n==="CANCEL"?o*Vo:o;return Number(l.toFixed(2))},zo=function(e){var r=e.impact,t=e.draggable,n=e.dimensions,i=e.viewport,a=e.afterCritical,o=n.draggables,l=n.droppables,u=V(r),d=u?l[u]:null,p=l[t.descriptor.droppableId],s=Nn({impact:r,draggable:t,draggables:o,afterCritical:a,droppable:d||p,viewport:i}),c=q(s,t.client.borderBox.center);return c},jo=function(e){var r=e.draggables,t=e.reason,n=e.lastImpact,i=e.home,a=e.viewport,o=e.onLiftImpact;if(!n.at||t!=="DROP"){var l=Tn({draggables:r,impact:o,destination:i,viewport:a,forceShouldAnimate:!0});return{impact:l,didDropInsideDroppable:!1}}if(n.at.type==="REORDER")return{impact:n,didDropInsideDroppable:!0};var u=w({},n,{displaced:Oe});return{impact:u,didDropInsideDroppable:!0}},Yo=function(e){var r=e.getState,t=e.dispatch;return function(n){return function(i){if(i.type!=="DROP"){n(i);return}var a=r(),o=i.payload.reason;if(a.phase==="COLLECTING"){t(Uo({reason:o}));return}if(a.phase!=="IDLE"){var l=a.phase==="DROP_PENDING"&&a.isWaiting;l&&h(!1),a.phase==="DRAGGING"||a.phase==="DROP_PENDING"||h(!1);var u=a.critical,d=a.dimensions,p=d.draggables[a.critical.draggable.id],s=jo({reason:o,lastImpact:a.impact,afterCritical:a.afterCritical,onLiftImpact:a.onLiftImpact,home:a.dimensions.droppables[a.critical.droppable.id],viewport:a.viewport,draggables:a.dimensions.draggables}),c=s.impact,v=s.didDropInsideDroppable,f=v?Jr(c):null,g=v?vr(c):null,m={index:u.draggable.index,droppableId:u.droppable.id},b={draggableId:p.descriptor.id,type:p.descriptor.type,source:m,reason:o,mode:a.movementMode,destination:f,combine:g},y=zo({impact:c,draggable:p,dimensions:d,viewport:a.viewport,afterCritical:a.afterCritical}),x={critical:a.critical,afterCritical:a.afterCritical,result:b,impact:c},D=!ae(a.current.client.offset,y)||!!b.combine;if(!D){t(tt({completed:x}));return}var E=$o({current:a.current.client.offset,destination:y,reason:o}),S={newHomeClientOffset:y,dropDuration:E,completed:x};t(Wo(S))}}}},kn=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function Ko(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}function Jo(e){var r=e.onWindowScroll;function t(){r(kn())}var n=Be(t),i=Ko(n),a=ne;function o(){return a!==ne}function l(){o()&&h(!1),a=X(window,[i])}function u(){o()||h(!1),n.cancel(),a(),a=ne}return{start:l,stop:u,isActive:o}}var Xo=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},Qo=function(e){var r=Jo({onWindowScroll:function(n){e.dispatch(To({newScroll:n}))}});return function(t){return function(n){!r.isActive()&&n.type==="INITIAL_PUBLISH"&&r.start(),r.isActive()&&Xo(n)&&r.stop(),t(n)}}},Zo=function(e){var r=!1,t=!1,n=setTimeout(function(){t=!0}),i=function(o){r||t||(r=!0,e(o),clearTimeout(n))};return i.wasCalled=function(){return r},i},_o=function(){var e=[],r=function(a){var o=Kr(e,function(d){return d.timerId===a});o===-1&&h(!1);var l=e.splice(o,1),u=l[0];u.callback()},t=function(a){var o=setTimeout(function(){return r(o)}),l={timerId:o,callback:a};e.push(l)},n=function(){if(e.length){var a=[].concat(e);e.length=0,a.forEach(function(o){clearTimeout(o.timerId),o.callback()})}};return{add:t,flush:n}},el=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.droppableId===t.droppableId&&r.index===t.index},rl=function(r,t){return r==null&&t==null?!0:r==null||t==null?!1:r.draggableId===t.draggableId&&r.droppableId===t.droppableId},tl=function(r,t){if(r===t)return!0;var n=r.draggable.id===t.draggable.id&&r.draggable.droppableId===t.draggable.droppableId&&r.draggable.type===t.draggable.type&&r.draggable.index===t.draggable.index,i=r.droppable.id===t.droppable.id&&r.droppable.type===t.droppable.type;return n&&i},Se=function(r,t){t()},Xe=function(r,t){return{draggableId:r.draggable.id,type:r.droppable.type,source:{droppableId:r.droppable.id,index:r.draggable.index},mode:t}},Rr=function(r,t,n,i){if(!r){n(i(t));return}var a=Zo(n),o={announce:a};r(t,o),a.wasCalled()||n(i(t))},nl=function(e,r){var t=_o(),n=null,i=function(c,v){n&&h(!1),Se("onBeforeCapture",function(){var f=e().onBeforeCapture;if(f){var g={draggableId:c,mode:v};f(g)}})},a=function(c,v){n&&h(!1),Se("onBeforeDragStart",function(){var f=e().onBeforeDragStart;f&&f(Xe(c,v))})},o=function(c,v){n&&h(!1);var f=Xe(c,v);n={mode:v,lastCritical:c,lastLocation:f.source,lastCombine:null},t.add(function(){Se("onDragStart",function(){return Rr(e().onDragStart,f,r,er.onDragStart)})})},l=function(c,v){var f=Jr(v),g=vr(v);n||h(!1);var m=!tl(c,n.lastCritical);m&&(n.lastCritical=c);var b=!el(n.lastLocation,f);b&&(n.lastLocation=f);var y=!rl(n.lastCombine,g);if(y&&(n.lastCombine=g),!(!m&&!b&&!y)){var x=w({},Xe(c,n.mode),{combine:g,destination:f});t.add(function(){Se("onDragUpdate",function(){return Rr(e().onDragUpdate,x,r,er.onDragUpdate)})})}},u=function(){n||h(!1),t.flush()},d=function(c){n||h(!1),n=null,Se("onDragEnd",function(){return Rr(e().onDragEnd,c,r,er.onDragEnd)})},p=function(){if(n){var c=w({},Xe(n.lastCritical,n.mode),{combine:null,destination:null,reason:"CANCEL"});d(c)}};return{beforeCapture:i,beforeStart:a,start:o,update:l,flush:u,drop:d,abort:p}},al=function(e,r){var t=nl(e,r);return function(n){return function(i){return function(a){if(a.type==="BEFORE_INITIAL_CAPTURE"){t.beforeCapture(a.payload.draggableId,a.payload.movementMode);return}if(a.type==="INITIAL_PUBLISH"){var o=a.payload.critical;t.beforeStart(o,a.payload.movementMode),i(a),t.start(o,a.payload.movementMode);return}if(a.type==="DROP_COMPLETE"){var l=a.payload.completed.result;t.flush(),i(a),t.drop(l);return}if(i(a),a.type==="FLUSH"){t.abort();return}var u=n.getState();u.phase==="DRAGGING"&&t.update(u.critical,u.impact)}}}},il=function(e){return function(r){return function(t){if(t.type!=="DROP_ANIMATION_FINISHED"){r(t);return}var n=e.getState();n.phase!=="DROP_ANIMATING"&&h(!1),e.dispatch(tt({completed:n.completed}))}}},ol=function(e){var r=null,t=null;function n(){t&&(cancelAnimationFrame(t),t=null),r&&(r(),r=null)}return function(i){return function(a){if((a.type==="FLUSH"||a.type==="DROP_COMPLETE"||a.type==="DROP_ANIMATION_FINISHED")&&n(),i(a),a.type==="DROP_ANIMATE"){var o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){var u=e.getState();u.phase==="DROP_ANIMATING"&&e.dispatch(Wn())}};t=requestAnimationFrame(function(){t=null,r=X(window,[o])})}}}},ll=function(e){return function(){return function(r){return function(t){(t.type==="DROP_COMPLETE"||t.type==="FLUSH"||t.type==="DROP_ANIMATE")&&e.stopPublishing(),r(t)}}}},ul=function(e){var r=!1;return function(){return function(t){return function(n){if(n.type==="INITIAL_PUBLISH"){r=!0,e.tryRecordFocus(n.payload.critical.draggable.id),t(n),e.tryRestoreFocusRecorded();return}if(t(n),!!r){if(n.type==="FLUSH"){r=!1,e.tryRestoreFocusRecorded();return}if(n.type==="DROP_COMPLETE"){r=!1;var i=n.payload.completed.result;i.combine&&e.tryShiftRecord(i.draggableId,i.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}},sl=function(r){return r.type==="DROP_COMPLETE"||r.type==="DROP_ANIMATE"||r.type==="FLUSH"},cl=function(e){return function(r){return function(t){return function(n){if(sl(n)){e.stop(),t(n);return}if(n.type==="INITIAL_PUBLISH"){t(n);var i=r.getState();i.phase!=="DRAGGING"&&h(!1),e.start(i);return}t(n),e.scroll(r.getState())}}}},dl=function(e){return function(r){return function(t){if(r(t),t.type==="PUBLISH_WHILE_DRAGGING"){var n=e.getState();n.phase==="DROP_PENDING"&&(n.isWaiting||e.dispatch(Gn({reason:n.reason})))}}}},pl=pa,vl=function(e){var r=e.dimensionMarshal,t=e.focusMarshal,n=e.styleMarshal,i=e.getResponders,a=e.announce,o=e.autoScroller;return ca(Co,pl(da(Ho(n),ll(r),ko(r),Yo,il,ol,dl,cl(o),Qo,ul(t),al(i,a))))},Or=function(){return{additions:{},removals:{},modified:{}}};function fl(e){var r=e.registry,t=e.callbacks,n=Or(),i=null,a=function(){i||(t.collectionStarting(),i=requestAnimationFrame(function(){i=null;var p=n,s=p.additions,c=p.removals,v=p.modified,f=Object.keys(s).map(function(b){return r.draggable.getById(b).getDimension(G)}).sort(function(b,y){return b.descriptor.index-y.descriptor.index}),g=Object.keys(v).map(function(b){var y=r.droppable.getById(b),x=y.callbacks.getScrollWhileDragging();return{droppableId:b,scroll:x}}),m={additions:f,removals:Object.keys(c),modified:g};n=Or(),t.publish(m)}))},o=function(p){var s=p.descriptor.id;n.additions[s]=p,n.modified[p.descriptor.droppableId]=!0,n.removals[s]&&delete n.removals[s],a()},l=function(p){var s=p.descriptor;n.removals[s.id]=!0,n.modified[s.droppableId]=!0,n.additions[s.id]&&delete n.additions[s.id],a()},u=function(){i&&(cancelAnimationFrame(i),i=null,n=Or())};return{add:o,remove:l,stop:u}}var Hn=function(e){var r=e.scrollHeight,t=e.scrollWidth,n=e.height,i=e.width,a=q({x:t,y:r},{x:i,y:n}),o={x:Math.max(0,a.x),y:Math.max(0,a.y)};return o},qn=function(){var e=document.documentElement;return e||h(!1),e},Vn=function(){var e=qn(),r=Hn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight});return r},gl=function(){var e=kn(),r=Vn(),t=e.y,n=e.x,i=qn(),a=i.clientWidth,o=i.clientHeight,l=n+a,u=t+o,d=_({top:t,left:n,right:l,bottom:u}),p={frame:d,scroll:{initial:e,current:e,max:r,diff:{value:G,displacement:G}}};return p},ml=function(e){var r=e.critical,t=e.scrollOptions,n=e.registry,i=gl(),a=i.scroll.current,o=r.droppable,l=n.droppable.getAllByType(o.type).map(function(s){return s.callbacks.getDimensionAndWatchScroll(a,t)}),u=n.draggable.getAllByType(r.draggable.type).map(function(s){return s.getDimension(a)}),d={draggables:hn(u),droppables:bn(l)},p={dimensions:d,critical:r,viewport:i};return p};function Mt(e,r,t){if(t.descriptor.id===r.id||t.descriptor.type!==r.type)return!1;var n=e.droppable.getById(t.descriptor.droppableId);return n.descriptor.mode==="virtual"}var bl=function(e,r){var t=null,n=fl({callbacks:{publish:r.publishWhileDragging,collectionStarting:r.collectionStarting},registry:e}),i=function(v,f){e.droppable.exists(v)||h(!1),t&&r.updateDroppableIsEnabled({id:v,isEnabled:f})},a=function(v,f){t&&(e.droppable.exists(v)||h(!1),r.updateDroppableIsCombineEnabled({id:v,isCombineEnabled:f}))},o=function(v,f){t&&(e.droppable.exists(v)||h(!1),r.updateDroppableScroll({id:v,newScroll:f}))},l=function(v,f){t&&e.droppable.getById(v).callbacks.scroll(f)},u=function(){if(t){n.stop();var v=t.critical.droppable;e.droppable.getAllByType(v.type).forEach(function(f){return f.callbacks.dragStopped()}),t.unsubscribe(),t=null}},d=function(v){t||h(!1);var f=t.critical.draggable;v.type==="ADDITION"&&Mt(e,f,v.value)&&n.add(v.value),v.type==="REMOVAL"&&Mt(e,f,v.value)&&n.remove(v.value)},p=function(v){t&&h(!1);var f=e.draggable.getById(v.draggableId),g=e.droppable.getById(f.descriptor.droppableId),m={draggable:f.descriptor,droppable:g.descriptor},b=e.subscribe(d);return t={critical:m,unsubscribe:b},ml({critical:m,registry:e,scrollOptions:v.scrollOptions})},s={updateDroppableIsEnabled:i,updateDroppableIsCombineEnabled:a,scrollDroppable:l,updateDroppableScroll:o,startPublishing:p,stopPublishing:u};return s},$n=function(e,r){return e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===r?!1:e.completed.result.reason==="DROP"},hl=function(e){window.scrollBy(e.x,e.y)},yl=F(function(e){return pr(e).filter(function(r){return!(!r.isEnabled||!r.frame)})}),Dl=function(r,t){var n=le(yl(t),function(i){return i.frame||h(!1),An(i.frame.pageMarginBox)(r)});return n},xl=function(e){var r=e.center,t=e.destination,n=e.droppables;if(t){var i=n[t];return i.frame?i:null}var a=Dl(r,n);return a},ie={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:function(r){return Math.pow(r,2)},durationDampening:{stopDampeningAt:1200,accelerateAt:360}},Il=function(e,r){var t=e[r.size]*ie.startFromPercentage,n=e[r.size]*ie.maxScrollAtPercentage,i={startScrollingFrom:t,maxScrollValueAt:n};return i},zn=function(e){var r=e.startOfRange,t=e.endOfRange,n=e.current,i=t-r;if(i===0)return 0;var a=n-r,o=a/i;return o},it=1,Cl=function(e,r){if(e>r.startScrollingFrom)return 0;if(e<=r.maxScrollValueAt)return ie.maxPixelScroll;if(e===r.startScrollingFrom)return it;var t=zn({startOfRange:r.maxScrollValueAt,endOfRange:r.startScrollingFrom,current:e}),n=1-t,i=ie.maxPixelScroll*ie.ease(n);return Math.ceil(i)},Lt=ie.durationDampening.accelerateAt,Ft=ie.durationDampening.stopDampeningAt,Sl=function(e,r){var t=r,n=Ft,i=Date.now(),a=i-t;if(a>=Ft)return e;if(a<Lt)return it;var o=zn({startOfRange:Lt,endOfRange:n,current:a}),l=e*ie.ease(o);return Math.ceil(l)},Gt=function(e){var r=e.distanceToEdge,t=e.thresholds,n=e.dragStartTime,i=e.shouldUseTimeDampening,a=Cl(r,t);return a===0?0:i?Math.max(Sl(a,n),it):a},Wt=function(e){var r=e.container,t=e.distanceToEdges,n=e.dragStartTime,i=e.axis,a=e.shouldUseTimeDampening,o=Il(r,i),l=t[i.end]<t[i.start];return l?Gt({distanceToEdge:t[i.end],thresholds:o,dragStartTime:n,shouldUseTimeDampening:a}):-1*Gt({distanceToEdge:t[i.start],thresholds:o,dragStartTime:n,shouldUseTimeDampening:a})},wl=function(e){var r=e.container,t=e.subject,n=e.proposedScroll,i=t.height>r.height,a=t.width>r.width;return!a&&!i?n:a&&i?null:{x:a?0:n.x,y:i?0:n.y}},El=gn(function(e){return e===0?0:e}),jn=function(e){var r=e.dragStartTime,t=e.container,n=e.subject,i=e.center,a=e.shouldUseTimeDampening,o={top:i.y-t.top,right:t.right-i.x,bottom:t.bottom-i.y,left:i.x-t.left},l=Wt({container:t,distanceToEdges:o,dragStartTime:r,axis:Xr,shouldUseTimeDampening:a}),u=Wt({container:t,distanceToEdges:o,dragStartTime:r,axis:xn,shouldUseTimeDampening:a}),d=El({x:u,y:l});if(ae(d,G))return null;var p=wl({container:t,subject:n,proposedScroll:d});return p?ae(p,G)?null:p:null},Pl=gn(function(e){return e===0?0:e>0?1:-1}),ot=function(){var e=function(t,n){return t<0?t:t>n?t-n:0};return function(r){var t=r.current,n=r.max,i=r.change,a=U(t,i),o={x:e(a.x,n.x),y:e(a.y,n.y)};return ae(o,G)?null:o}}(),Yn=function(r){var t=r.max,n=r.current,i=r.change,a={x:Math.max(n.x,t.x),y:Math.max(n.y,t.y)},o=Pl(i),l=ot({max:a,current:n,change:o});return!l||o.x!==0&&l.x===0||o.y!==0&&l.y===0},lt=function(r,t){return Yn({current:r.scroll.current,max:r.scroll.max,change:t})},Al=function(r,t){if(!lt(r,t))return null;var n=r.scroll.max,i=r.scroll.current;return ot({current:i,max:n,change:t})},ut=function(r,t){var n=r.frame;return n?Yn({current:n.scroll.current,max:n.scroll.max,change:t}):!1},Bl=function(r,t){var n=r.frame;return!n||!ut(r,t)?null:ot({current:n.scroll.current,max:n.scroll.max,change:t})},Rl=function(e){var r=e.viewport,t=e.subject,n=e.center,i=e.dragStartTime,a=e.shouldUseTimeDampening,o=jn({dragStartTime:i,container:r.frame,subject:t,center:n,shouldUseTimeDampening:a});return o&&lt(r,o)?o:null},Ol=function(e){var r=e.droppable,t=e.subject,n=e.center,i=e.dragStartTime,a=e.shouldUseTimeDampening,o=r.frame;if(!o)return null;var l=jn({dragStartTime:i,container:o.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:a});return l&&ut(r,l)?l:null},Ut=function(e){var r=e.state,t=e.dragStartTime,n=e.shouldUseTimeDampening,i=e.scrollWindow,a=e.scrollDroppable,o=r.current.page.borderBoxCenter,l=r.dimensions.draggables[r.critical.draggable.id],u=l.page.marginBox;if(r.isWindowScrollAllowed){var d=r.viewport,p=Rl({dragStartTime:t,viewport:d,subject:u,center:o,shouldUseTimeDampening:n});if(p){i(p);return}}var s=xl({center:o,destination:V(r.impact),droppables:r.dimensions.droppables});if(s){var c=Ol({dragStartTime:t,droppable:s,subject:u,center:o,shouldUseTimeDampening:n});c&&a(s.descriptor.id,c)}},Tl=function(e){var r=e.scrollWindow,t=e.scrollDroppable,n=Be(r),i=Be(t),a=null,o=function(p){a||h(!1);var s=a,c=s.shouldUseTimeDampening,v=s.dragStartTime;Ut({state:p,scrollWindow:n,scrollDroppable:i,dragStartTime:v,shouldUseTimeDampening:c})},l=function(p){a&&h(!1);var s=Date.now(),c=!1,v=function(){c=!0};Ut({state:p,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:v,scrollDroppable:v}),a={dragStartTime:s,shouldUseTimeDampening:c},c&&o(p)},u=function(){a&&(n.cancel(),i.cancel(),a=null)};return{start:l,stop:u,scroll:o}},Nl=function(e){var r=e.move,t=e.scrollDroppable,n=e.scrollWindow,i=function(d,p){var s=U(d.current.client.selection,p);r({client:s})},a=function(d,p){if(!ut(d,p))return p;var s=Bl(d,p);if(!s)return t(d.descriptor.id,p),null;var c=q(p,s);t(d.descriptor.id,c);var v=q(p,c);return v},o=function(d,p,s){if(!d||!lt(p,s))return s;var c=Al(p,s);if(!c)return n(s),null;var v=q(s,c);n(v);var f=q(s,v);return f},l=function(d){var p=d.scrollJumpRequest;if(p){var s=V(d.impact);s||h(!1);var c=a(d.dimensions.droppables[s],p);if(c){var v=d.viewport,f=o(d.isWindowScrollAllowed,v,c);f&&i(d,f)}}};return l},Ml=function(e){var r=e.scrollDroppable,t=e.scrollWindow,n=e.move,i=Tl({scrollWindow:t,scrollDroppable:r}),a=Nl({move:n,scrollWindow:t,scrollDroppable:r}),o=function(d){if(d.phase==="DRAGGING"){if(d.movementMode==="FLUID"){i.scroll(d);return}d.scrollJumpRequest&&a(d)}},l={scroll:o,start:i.start,stop:i.stop};return l},be="data-rbd",he=function(){var e=be+"-drag-handle";return{base:e,draggableId:e+"-draggable-id",contextId:e+"-context-id"}}(),Hr=function(){var e=be+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Ll=function(){var e=be+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),kt={contextId:be+"-scroll-container-context-id"},Fl=function(r){return function(t){return"["+t+'="'+r+'"]'}},we=function(r,t){return r.map(function(n){var i=n.styles[t];return i?n.selector+" { "+i+" }":""}).join(" ")},Gl="pointer-events: none;",Wl=function(e){var r=Fl(e),t=function(){var l=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:r(he.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:l,dragging:Gl,dropAnimating:l}}}(),n=function(){var l=`
      transition: `+Ae.outOfTheWay+`;
    `;return{selector:r(Hr.contextId),styles:{dragging:l,dropAnimating:l,userCancel:l}}}(),i={selector:r(Ll.contextId),styles:{always:"overflow-anchor: none;"}},a={selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}},o=[n,t,i,a];return{always:we(o,"always"),resting:we(o,"resting"),dragging:we(o,"dragging"),dropAnimating:we(o,"dropAnimating"),userCancel:we(o,"userCancel")}},$=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?I.useLayoutEffect:I.useEffect,Tr=function(){var r=document.querySelector("head");return r||h(!1),r},Ht=function(r){var t=document.createElement("style");return r&&t.setAttribute("nonce",r),t.type="text/css",t};function Ul(e,r){var t=A(function(){return Wl(e)},[e]),n=I.useRef(null),i=I.useRef(null),a=C(F(function(s){var c=i.current;c||h(!1),c.textContent=s}),[]),o=C(function(s){var c=n.current;c||h(!1),c.textContent=s},[]);$(function(){!n.current&&!i.current||h(!1);var s=Ht(r),c=Ht(r);return n.current=s,i.current=c,s.setAttribute(be+"-always",e),c.setAttribute(be+"-dynamic",e),Tr().appendChild(s),Tr().appendChild(c),o(t.always),a(t.resting),function(){var v=function(g){var m=g.current;m||h(!1),Tr().removeChild(m),g.current=null};v(n),v(i)}},[r,o,a,t.always,t.resting,e]);var l=C(function(){return a(t.dragging)},[a,t.dragging]),u=C(function(s){if(s==="DROP"){a(t.dropAnimating);return}a(t.userCancel)},[a,t.dropAnimating,t.userCancel]),d=C(function(){i.current&&a(t.resting)},[a,t.resting]),p=A(function(){return{dragging:l,dropping:u,resting:d}},[l,u,d]);return p}var Kn=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function mr(e){return e instanceof Kn(e).HTMLElement}function kl(e,r){var t="["+he.contextId+'="'+e+'"]',n=mn(document.querySelectorAll(t));if(!n.length)return null;var i=le(n,function(a){return a.getAttribute(he.draggableId)===r});return!i||!mr(i)?null:i}function Hl(e){var r=I.useRef({}),t=I.useRef(null),n=I.useRef(null),i=I.useRef(!1),a=C(function(c,v){var f={id:c,focus:v};return r.current[c]=f,function(){var m=r.current,b=m[c];b!==f&&delete m[c]}},[]),o=C(function(c){var v=kl(e,c);v&&v!==document.activeElement&&v.focus()},[e]),l=C(function(c,v){t.current===c&&(t.current=v)},[]),u=C(function(){n.current||i.current&&(n.current=requestAnimationFrame(function(){n.current=null;var c=t.current;c&&o(c)}))},[o]),d=C(function(c){t.current=null;var v=document.activeElement;v&&v.getAttribute(he.draggableId)===c&&(t.current=c)},[]);$(function(){return i.current=!0,function(){i.current=!1;var c=n.current;c&&cancelAnimationFrame(c)}},[]);var p=A(function(){return{register:a,tryRecordFocus:d,tryRestoreFocusRecorded:u,tryShiftRecord:l}},[a,d,u,l]);return p}function ql(){var e={draggables:{},droppables:{}},r=[];function t(s){return r.push(s),function(){var v=r.indexOf(s);v!==-1&&r.splice(v,1)}}function n(s){r.length&&r.forEach(function(c){return c(s)})}function i(s){return e.draggables[s]||null}function a(s){var c=i(s);return c||h(!1),c}var o={register:function(c){e.draggables[c.descriptor.id]=c,n({type:"ADDITION",value:c})},update:function(c,v){var f=e.draggables[v.descriptor.id];f&&f.uniqueId===c.uniqueId&&(delete e.draggables[v.descriptor.id],e.draggables[c.descriptor.id]=c)},unregister:function(c){var v=c.descriptor.id,f=i(v);f&&c.uniqueId===f.uniqueId&&(delete e.draggables[v],n({type:"REMOVAL",value:c}))},getById:a,findById:i,exists:function(c){return!!i(c)},getAllByType:function(c){return or(e.draggables).filter(function(v){return v.descriptor.type===c})}};function l(s){return e.droppables[s]||null}function u(s){var c=l(s);return c||h(!1),c}var d={register:function(c){e.droppables[c.descriptor.id]=c},unregister:function(c){var v=l(c.descriptor.id);v&&c.uniqueId===v.uniqueId&&delete e.droppables[c.descriptor.id]},getById:u,findById:l,exists:function(c){return!!l(c)},getAllByType:function(c){return or(e.droppables).filter(function(v){return v.descriptor.type===c})}};function p(){e.draggables={},e.droppables={},r.length=0}return{draggable:o,droppable:d,subscribe:t,clean:p}}function Vl(){var e=A(ql,[]);return I.useEffect(function(){return function(){requestAnimationFrame(e.clean)}},[e]),e}var st=T.createContext(null),ur=function(){var e=document.body;return e||h(!1),e},$l={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},zl=function(r){return"rbd-announcement-"+r};function jl(e){var r=A(function(){return zl(e)},[e]),t=I.useRef(null);I.useEffect(function(){var a=document.createElement("div");return t.current=a,a.id=r,a.setAttribute("aria-live","assertive"),a.setAttribute("aria-atomic","true"),w(a.style,$l),ur().appendChild(a),function(){setTimeout(function(){var u=ur();u.contains(a)&&u.removeChild(a),a===t.current&&(t.current=null)})}},[r]);var n=C(function(i){var a=t.current;if(a){a.textContent=i;return}},[]);return n}var Yl=0,Kl={separator:"::"};function ct(e,r){return r===void 0&&(r=Kl),A(function(){return""+e+r.separator+Yl++},[r.separator,e])}function Jl(e){var r=e.contextId,t=e.uniqueId;return"rbd-hidden-text-"+r+"-"+t}function Xl(e){var r=e.contextId,t=e.text,n=ct("hidden-text",{separator:"-"}),i=A(function(){return Jl({contextId:r,uniqueId:n})},[n,r]);return I.useEffect(function(){var o=document.createElement("div");return o.id=i,o.textContent=t,o.style.display="none",ur().appendChild(o),function(){var u=ur();u.contains(o)&&u.removeChild(o)}},[i,t]),i}var br=T.createContext(null);function Jn(e){var r=I.useRef(e);return I.useEffect(function(){r.current=e}),r}function Ql(){var e=null;function r(){return!!e}function t(o){return o===e}function n(o){e&&h(!1);var l={abandon:o};return e=l,l}function i(){e||h(!1),e=null}function a(){e&&(e.abandon(),i())}return{isClaimed:r,isActive:t,claim:n,release:i,tryAbandon:a}}var Zl=9,_l=13,dt=27,Xn=32,eu=33,ru=34,tu=35,nu=36,au=37,iu=38,ou=39,lu=40,Qe,uu=(Qe={},Qe[_l]=!0,Qe[Zl]=!0,Qe),Qn=function(e){uu[e.keyCode]&&e.preventDefault()},hr=function(){var e="visibilitychange";if(typeof document>"u")return e;var r=[e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],t=le(r,function(n){return"on"+n in document});return t||e}(),Zn=0,qt=5;function su(e,r){return Math.abs(r.x-e.x)>=qt||Math.abs(r.y-e.y)>=qt}var Vt={type:"IDLE"};function cu(e){var r=e.cancel,t=e.completed,n=e.getPhase,i=e.setPhase;return[{eventName:"mousemove",fn:function(o){var l=o.button,u=o.clientX,d=o.clientY;if(l===Zn){var p={x:u,y:d},s=n();if(s.type==="DRAGGING"){o.preventDefault(),s.actions.move(p);return}s.type!=="PENDING"&&h(!1);var c=s.point;if(su(c,p)){o.preventDefault();var v=s.actions.fluidLift(p);i({type:"DRAGGING",actions:v})}}}},{eventName:"mouseup",fn:function(o){var l=n();if(l.type!=="DRAGGING"){r();return}o.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:function(o){n().type==="DRAGGING"&&o.preventDefault(),r()}},{eventName:"keydown",fn:function(o){var l=n();if(l.type==="PENDING"){r();return}if(o.keyCode===dt){o.preventDefault(),r();return}Qn(o)}},{eventName:"resize",fn:r},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){n().type==="PENDING"&&r()}},{eventName:"webkitmouseforcedown",fn:function(o){var l=n();if(l.type==="IDLE"&&h(!1),l.actions.shouldRespectForcePress()){r();return}o.preventDefault()}},{eventName:hr,fn:r}]}function du(e){var r=I.useRef(Vt),t=I.useRef(ne),n=A(function(){return{eventName:"mousedown",fn:function(s){if(!s.defaultPrevented&&s.button===Zn&&!(s.ctrlKey||s.metaKey||s.shiftKey||s.altKey)){var c=e.findClosestDraggableId(s);if(c){var v=e.tryGetLock(c,o,{sourceEvent:s});if(v){s.preventDefault();var f={x:s.clientX,y:s.clientY};t.current(),d(v,f)}}}}}},[e]),i=A(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(s){if(!s.defaultPrevented){var c=e.findClosestDraggableId(s);if(c){var v=e.findOptionsForDraggable(c);v&&(v.shouldRespectForcePress||e.canGetLock(c)&&s.preventDefault())}}}}},[e]),a=C(function(){var s={passive:!1,capture:!0};t.current=X(window,[i,n],s)},[i,n]),o=C(function(){var p=r.current;p.type!=="IDLE"&&(r.current=Vt,t.current(),a())},[a]),l=C(function(){var p=r.current;o(),p.type==="DRAGGING"&&p.actions.cancel({shouldBlockNextClick:!0}),p.type==="PENDING"&&p.actions.abort()},[o]),u=C(function(){var s={capture:!0,passive:!1},c=cu({cancel:l,completed:o,getPhase:function(){return r.current},setPhase:function(f){r.current=f}});t.current=X(window,c,s)},[l,o]),d=C(function(s,c){r.current.type!=="IDLE"&&h(!1),r.current={type:"PENDING",point:c,actions:s},u()},[u]);$(function(){return a(),function(){t.current()}},[a])}var ge;function pu(){}var vu=(ge={},ge[ru]=!0,ge[eu]=!0,ge[nu]=!0,ge[tu]=!0,ge);function fu(e,r){function t(){r(),e.cancel()}function n(){r(),e.drop()}return[{eventName:"keydown",fn:function(a){if(a.keyCode===dt){a.preventDefault(),t();return}if(a.keyCode===Xn){a.preventDefault(),n();return}if(a.keyCode===lu){a.preventDefault(),e.moveDown();return}if(a.keyCode===iu){a.preventDefault(),e.moveUp();return}if(a.keyCode===ou){a.preventDefault(),e.moveRight();return}if(a.keyCode===au){a.preventDefault(),e.moveLeft();return}if(vu[a.keyCode]){a.preventDefault();return}Qn(a)}},{eventName:"mousedown",fn:t},{eventName:"mouseup",fn:t},{eventName:"click",fn:t},{eventName:"touchstart",fn:t},{eventName:"resize",fn:t},{eventName:"wheel",fn:t,options:{passive:!0}},{eventName:hr,fn:t}]}function gu(e){var r=I.useRef(pu),t=A(function(){return{eventName:"keydown",fn:function(a){if(a.defaultPrevented||a.keyCode!==Xn)return;var o=e.findClosestDraggableId(a);if(!o)return;var l=e.tryGetLock(o,p,{sourceEvent:a});if(!l)return;a.preventDefault();var u=!0,d=l.snapLift();r.current();function p(){u||h(!1),u=!1,r.current(),n()}r.current=X(window,fu(d,p),{capture:!0,passive:!1})}}},[e]),n=C(function(){var a={passive:!1,capture:!0};r.current=X(window,[t],a)},[t]);$(function(){return n(),function(){r.current()}},[n])}var Nr={type:"IDLE"},mu=120,bu=.15;function hu(e){var r=e.cancel,t=e.getPhase;return[{eventName:"orientationchange",fn:r},{eventName:"resize",fn:r},{eventName:"contextmenu",fn:function(i){i.preventDefault()}},{eventName:"keydown",fn:function(i){if(t().type!=="DRAGGING"){r();return}i.keyCode===dt&&i.preventDefault(),r()}},{eventName:hr,fn:r}]}function yu(e){var r=e.cancel,t=e.completed,n=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(a){var o=n();if(o.type!=="DRAGGING"){r();return}o.hasMoved=!0;var l=a.touches[0],u=l.clientX,d=l.clientY,p={x:u,y:d};a.preventDefault(),o.actions.move(p)}},{eventName:"touchend",fn:function(a){var o=n();if(o.type!=="DRAGGING"){r();return}a.preventDefault(),o.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:function(a){if(n().type!=="DRAGGING"){r();return}a.preventDefault(),r()}},{eventName:"touchforcechange",fn:function(a){var o=n();o.type==="IDLE"&&h(!1);var l=a.touches[0];if(l){var u=l.force>=bu;if(u){var d=o.actions.shouldRespectForcePress();if(o.type==="PENDING"){d&&r();return}if(d){if(o.hasMoved){a.preventDefault();return}r();return}a.preventDefault()}}}},{eventName:hr,fn:r}]}function Du(e){var r=I.useRef(Nr),t=I.useRef(ne),n=C(function(){return r.current},[]),i=C(function(v){r.current=v},[]),a=A(function(){return{eventName:"touchstart",fn:function(v){if(!v.defaultPrevented){var f=e.findClosestDraggableId(v);if(f){var g=e.tryGetLock(f,l,{sourceEvent:v});if(g){var m=v.touches[0],b=m.clientX,y=m.clientY,x={x:b,y};t.current(),s(g,x)}}}}}},[e]),o=C(function(){var v={capture:!0,passive:!1};t.current=X(window,[a],v)},[a]),l=C(function(){var c=r.current;c.type!=="IDLE"&&(c.type==="PENDING"&&clearTimeout(c.longPressTimerId),i(Nr),t.current(),o())},[o,i]),u=C(function(){var c=r.current;l(),c.type==="DRAGGING"&&c.actions.cancel({shouldBlockNextClick:!0}),c.type==="PENDING"&&c.actions.abort()},[l]),d=C(function(){var v={capture:!0,passive:!1},f={cancel:u,completed:l,getPhase:n},g=X(window,yu(f),v),m=X(window,hu(f),v);t.current=function(){g(),m()}},[u,n,l]),p=C(function(){var v=n();v.type!=="PENDING"&&h(!1);var f=v.actions.fluidLift(v.point);i({type:"DRAGGING",actions:f,hasMoved:!1})},[n,i]),s=C(function(v,f){n().type!=="IDLE"&&h(!1);var g=setTimeout(p,mu);i({type:"PENDING",point:f,actions:v,longPressTimerId:g}),d()},[d,n,i,p]);$(function(){return o(),function(){t.current();var f=n();f.type==="PENDING"&&(clearTimeout(f.longPressTimerId),i(Nr))}},[n,o,i]),$(function(){var v=X(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}]);return v},[])}var xu={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function _n(e,r){if(r==null)return!1;var t=!!xu[r.tagName.toLowerCase()];if(t)return!0;var n=r.getAttribute("contenteditable");return n==="true"||n===""?!0:r===e?!1:_n(e,r.parentElement)}function Iu(e,r){var t=r.target;return mr(t)?_n(e,t):!1}var Cu=function(e){return _(e.getBoundingClientRect()).center};function Su(e){return e instanceof Kn(e).Element}var wu=function(){var e="matches";if(typeof document>"u")return e;var r=[e,"msMatchesSelector","webkitMatchesSelector"],t=le(r,function(n){return n in Element.prototype});return t||e}();function ea(e,r){return e==null?null:e[wu](r)?e:ea(e.parentElement,r)}function Eu(e,r){return e.closest?e.closest(r):ea(e,r)}function Pu(e){return"["+he.contextId+'="'+e+'"]'}function Au(e,r){var t=r.target;if(!Su(t))return null;var n=Pu(e),i=Eu(t,n);return!i||!mr(i)?null:i}function Bu(e,r){var t=Au(e,r);return t?t.getAttribute(he.draggableId):null}function Ru(e,r){var t="["+Hr.contextId+'="'+e+'"]',n=mn(document.querySelectorAll(t)),i=le(n,function(a){return a.getAttribute(Hr.id)===r});return!i||!mr(i)?null:i}function Ou(e){e.preventDefault()}function Ze(e){var r=e.expected,t=e.phase,n=e.isLockActive;return e.shouldWarn,!(!n()||r!==t)}function ra(e){var r=e.lockAPI,t=e.store,n=e.registry,i=e.draggableId;if(r.isClaimed())return!1;var a=n.draggable.findById(i);return!(!a||!a.options.isEnabled||!$n(t.getState(),i))}function Tu(e){var r=e.lockAPI,t=e.contextId,n=e.store,i=e.registry,a=e.draggableId,o=e.forceSensorStop,l=e.sourceEvent,u=ra({lockAPI:r,store:n,registry:i,draggableId:a});if(!u)return null;var d=i.draggable.getById(a),p=Ru(t,d.descriptor.id);if(!p||l&&!d.options.canDragInteractiveElements&&Iu(p,l))return null;var s=r.claim(o||ne),c="PRE_DRAG";function v(){return d.options.shouldRespectForcePress}function f(){return r.isActive(s)}function g(S,B){Ze({expected:S,phase:c,isLockActive:f,shouldWarn:!0})&&n.dispatch(B())}var m=g.bind(null,"DRAGGING");function b(S){function B(){r.release(),c="COMPLETED"}c!=="PRE_DRAG"&&(B(),c!=="PRE_DRAG"&&h(!1)),n.dispatch(wo(S.liftActionArgs)),c="DRAGGING";function N(P,M){if(M===void 0&&(M={shouldBlockNextClick:!1}),S.cleanup(),M.shouldBlockNextClick){var R=X(window,[{eventName:"click",fn:Ou,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(R)}B(),n.dispatch(Gn({reason:P}))}return w({isActive:function(){return Ze({expected:"DRAGGING",phase:c,isLockActive:f,shouldWarn:!1})},shouldRespectForcePress:v,drop:function(M){return N("DROP",M)},cancel:function(M){return N("CANCEL",M)}},S.actions)}function y(S){var B=Be(function(P){m(function(){return Fn({client:P})})}),N=b({liftActionArgs:{id:a,clientSelection:S,movementMode:"FLUID"},cleanup:function(){return B.cancel()},actions:{move:B}});return w({},N,{move:B})}function x(){var S={moveUp:function(){return m(Mo)},moveRight:function(){return m(Fo)},moveDown:function(){return m(Lo)},moveLeft:function(){return m(Go)}};return b({liftActionArgs:{id:a,clientSelection:Cu(p),movementMode:"SNAP"},cleanup:ne,actions:S})}function D(){var S=Ze({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!0});S&&r.release()}var E={isActive:function(){return Ze({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!1})},shouldRespectForcePress:v,fluidLift:y,snapLift:x,abort:D};return E}var Nu=[du,gu,Du];function Mu(e){var r=e.contextId,t=e.store,n=e.registry,i=e.customSensors,a=e.enableDefaultSensors,o=[].concat(a?Nu:[],i||[]),l=I.useState(function(){return Ql()})[0],u=C(function(y,x){y.isDragging&&!x.isDragging&&l.tryAbandon()},[l]);$(function(){var y=t.getState(),x=t.subscribe(function(){var D=t.getState();u(y,D),y=D});return x},[l,t,u]),$(function(){return l.tryAbandon},[l.tryAbandon]);for(var d=C(function(b){return ra({lockAPI:l,registry:n,store:t,draggableId:b})},[l,n,t]),p=C(function(b,y,x){return Tu({lockAPI:l,registry:n,contextId:r,store:t,draggableId:b,forceSensorStop:y,sourceEvent:x&&x.sourceEvent?x.sourceEvent:null})},[r,l,n,t]),s=C(function(b){return Bu(r,b)},[r]),c=C(function(b){var y=n.draggable.findById(b);return y?y.options:null},[n.draggable]),v=C(function(){l.isClaimed()&&(l.tryAbandon(),t.getState().phase!=="IDLE"&&t.dispatch(rt()))},[l,t]),f=C(l.isClaimed,[l]),g=A(function(){return{canGetLock:d,tryGetLock:p,findClosestDraggableId:s,findOptionsForDraggable:c,tryReleaseLock:v,isLockClaimed:f}},[d,p,s,c,v,f]),m=0;m<o.length;m++)o[m](g)}var Lu=function(r){return{onBeforeCapture:r.onBeforeCapture,onBeforeDragStart:r.onBeforeDragStart,onDragStart:r.onDragStart,onDragEnd:r.onDragEnd,onDragUpdate:r.onDragUpdate}};function Ee(e){return e.current||h(!1),e.current}function Fu(e){var r=e.contextId,t=e.setCallbacks,n=e.sensors,i=e.nonce,a=e.dragHandleUsageInstructions,o=I.useRef(null),l=Jn(e),u=C(function(){return Lu(l.current)},[l]),d=jl(r),p=Xl({contextId:r,text:a}),s=Ul(r,i),c=C(function(P){Ee(o).dispatch(P)},[]),v=A(function(){return mt({publishWhileDragging:Po,updateDroppableScroll:Bo,updateDroppableIsEnabled:Ro,updateDroppableIsCombineEnabled:Oo,collectionStarting:Ao},c)},[c]),f=Vl(),g=A(function(){return bl(f,v)},[f,v]),m=A(function(){return Ml(w({scrollWindow:hl,scrollDroppable:g.scrollDroppable},mt({move:Fn},c)))},[g.scrollDroppable,c]),b=Hl(r),y=A(function(){return vl({announce:d,autoScroller:m,dimensionMarshal:g,focusMarshal:b,getResponders:u,styleMarshal:s})},[d,m,g,b,u,s]);o.current=y;var x=C(function(){var P=Ee(o),M=P.getState();M.phase!=="IDLE"&&P.dispatch(rt())},[]),D=C(function(){var P=Ee(o).getState();return P.isDragging||P.phase==="DROP_ANIMATING"},[]),E=A(function(){return{isDragging:D,tryAbort:x}},[D,x]);t(E);var S=C(function(P){return $n(Ee(o).getState(),P)},[]),B=C(function(){return ce(Ee(o).getState())},[]),N=A(function(){return{marshal:g,focus:b,contextId:r,canLift:S,isMovementAllowed:B,dragHandleUsageInstructionsId:p,registry:f}},[r,g,p,b,S,B,f]);return Mu({contextId:r,store:y,registry:f,customSensors:n,enableDefaultSensors:e.enableDefaultSensors!==!1}),I.useEffect(function(){return x},[x]),T.createElement(br.Provider,{value:N},T.createElement(ha,{context:st,store:y},e.children))}var Gu=0;function Wu(){return A(function(){return""+Gu++},[])}function Os(e){var r=Wu(),t=e.dragHandleUsageInstructions||er.dragHandleUsageInstructions;return T.createElement(fi,null,function(n){return T.createElement(Fu,{nonce:e.nonce,contextId:r,setCallbacks:n,dragHandleUsageInstructions:t,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)})}var ta=function(r){return function(t){return r===t}},Uu=ta("scroll"),ku=ta("auto"),$t=function(r,t){return t(r.overflowX)||t(r.overflowY)},Hu=function(r){var t=window.getComputedStyle(r),n={overflowX:t.overflowX,overflowY:t.overflowY};return $t(n,Uu)||$t(n,ku)},qu=function(){return!1},Vu=function e(r){return r==null?null:r===document.body?qu()?r:null:r===document.documentElement?null:Hu(r)?r:e(r.parentElement)},qr=function(e){return{x:e.scrollLeft,y:e.scrollTop}},$u=function e(r){if(!r)return!1;var t=window.getComputedStyle(r);return t.position==="fixed"?!0:e(r.parentElement)},zu=function(e){var r=Vu(e),t=$u(e);return{closestScrollable:r,isFixedOnPage:t}},ju=function(e){var r=e.descriptor,t=e.isEnabled,n=e.isCombineEnabled,i=e.isFixedOnPage,a=e.direction,o=e.client,l=e.page,u=e.closest,d=function(){if(!u)return null;var v=u.scrollSize,f=u.client,g=Hn({scrollHeight:v.scrollHeight,scrollWidth:v.scrollWidth,height:f.paddingBox.height,width:f.paddingBox.width});return{pageMarginBox:u.page.marginBox,frameClient:f,scrollSize:v,shouldClipSubject:u.shouldClipSubject,scroll:{initial:u.scroll,current:u.scroll,max:g,diff:{value:G,displacement:G}}}}(),p=a==="vertical"?Xr:xn,s=me({page:l,withPlaceholder:null,axis:p,frame:d}),c={descriptor:r,isCombineEnabled:n,isFixedOnPage:i,axis:p,isEnabled:t,client:o,page:l,frame:d,subject:s};return c},Yu=function(r,t){var n=dn(r);if(!t||r!==t)return n;var i=n.paddingBox.top-t.scrollTop,a=n.paddingBox.left-t.scrollLeft,o=i+t.scrollHeight,l=a+t.scrollWidth,u={top:i,right:l,bottom:o,left:a},d=zr(u,n.border),p=jr({borderBox:d,margin:n.margin,border:n.border,padding:n.padding});return p},Ku=function(e){var r=e.ref,t=e.descriptor,n=e.env,i=e.windowScroll,a=e.direction,o=e.isDropDisabled,l=e.isCombineEnabled,u=e.shouldClipSubject,d=n.closestScrollable,p=Yu(r,d),s=nr(p,i),c=function(){if(!d)return null;var f=dn(d),g={scrollHeight:d.scrollHeight,scrollWidth:d.scrollWidth};return{client:f,page:nr(f,i),scroll:qr(d),scrollSize:g,shouldClipSubject:u}}(),v=ju({descriptor:t,isEnabled:!o,isCombineEnabled:l,isFixedOnPage:n.isFixedOnPage,direction:a,client:p,page:s,closest:c});return v},Ju={passive:!1},Xu={passive:!0},zt=function(e){return e.shouldPublishImmediately?Ju:Xu};function sr(e){var r=I.useContext(e);return r||h(!1),r}var _e=function(r){return r&&r.env.closestScrollable||null};function Qu(e){var r=I.useRef(null),t=sr(br),n=ct("droppable"),i=t.registry,a=t.marshal,o=Jn(e),l=A(function(){return{id:e.droppableId,type:e.type,mode:e.mode}},[e.droppableId,e.mode,e.type]),u=I.useRef(l),d=A(function(){return F(function(D,E){r.current||h(!1);var S={x:D,y:E};a.updateDroppableScroll(l.id,S)})},[l.id,a]),p=C(function(){var D=r.current;return!D||!D.env.closestScrollable?G:qr(D.env.closestScrollable)},[]),s=C(function(){var D=p();d(D.x,D.y)},[p,d]),c=A(function(){return Be(s)},[s]),v=C(function(){var D=r.current,E=_e(D);D&&E||h(!1);var S=D.scrollOptions;if(S.shouldPublishImmediately){s();return}c()},[c,s]),f=C(function(D,E){r.current&&h(!1);var S=o.current,B=S.getDroppableRef();B||h(!1);var N=zu(B),P={ref:B,descriptor:l,env:N,scrollOptions:E};r.current=P;var M=Ku({ref:B,descriptor:l,env:N,windowScroll:D,direction:S.direction,isDropDisabled:S.isDropDisabled,isCombineEnabled:S.isCombineEnabled,shouldClipSubject:!S.ignoreContainerClipping}),R=N.closestScrollable;return R&&(R.setAttribute(kt.contextId,t.contextId),R.addEventListener("scroll",v,zt(P.scrollOptions))),M},[t.contextId,l,v,o]),g=C(function(){var D=r.current,E=_e(D);return D&&E||h(!1),qr(E)},[]),m=C(function(){var D=r.current;D||h(!1);var E=_e(D);r.current=null,E&&(c.cancel(),E.removeAttribute(kt.contextId),E.removeEventListener("scroll",v,zt(D.scrollOptions)))},[v,c]),b=C(function(D){var E=r.current;E||h(!1);var S=_e(E);S||h(!1),S.scrollTop+=D.y,S.scrollLeft+=D.x},[]),y=A(function(){return{getDimensionAndWatchScroll:f,getScrollWhileDragging:g,dragStopped:m,scroll:b}},[m,f,g,b]),x=A(function(){return{uniqueId:n,descriptor:l,callbacks:y}},[y,l,n]);$(function(){return u.current=x.descriptor,i.droppable.register(x),function(){r.current&&m(),i.droppable.unregister(x)}},[y,l,m,x,a,i.droppable]),$(function(){r.current&&a.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)},[e.isDropDisabled,a]),$(function(){r.current&&a.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)},[e.isCombineEnabled,a])}function Mr(){}var jt={width:0,height:0,margin:Di},Zu=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,i=r.animate;return t||i==="close"?jt:{height:n.client.borderBox.height,width:n.client.borderBox.width,margin:n.client.margin}},_u=function(r){var t=r.isAnimatingOpenOnMount,n=r.placeholder,i=r.animate,a=Zu({isAnimatingOpenOnMount:t,placeholder:n,animate:i});return{display:n.display,boxSizing:"border-box",width:a.width,height:a.height,marginTop:a.margin.top,marginRight:a.margin.right,marginBottom:a.margin.bottom,marginLeft:a.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:i!=="none"?Ae.placeholder:null}};function es(e){var r=I.useRef(null),t=C(function(){r.current&&(clearTimeout(r.current),r.current=null)},[]),n=e.animate,i=e.onTransitionEnd,a=e.onClose,o=e.contextId,l=I.useState(e.animate==="open"),u=l[0],d=l[1];I.useEffect(function(){return u?n!=="open"?(t(),d(!1),Mr):r.current?Mr:(r.current=setTimeout(function(){r.current=null,d(!1)}),t):Mr},[n,u,t]);var p=C(function(c){c.propertyName==="height"&&(i(),n==="close"&&a())},[n,a,i]),s=_u({isAnimatingOpenOnMount:u,animate:e.animate,placeholder:e.placeholder});return T.createElement(e.placeholder.tagName,{style:s,"data-rbd-placeholder-context-id":o,onTransitionEnd:p,ref:e.innerRef})}var rs=T.memo(es),pt=T.createContext(null),ts=function(e){Jt(r,e);function r(){for(var n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=e.call.apply(e,[this].concat(a))||this,n.state={isVisible:!!n.props.on,data:n.props.on,animate:n.props.shouldAnimate&&n.props.on?"open":"none"},n.onClose=function(){n.state.animate==="close"&&n.setState({isVisible:!1})},n}r.getDerivedStateFromProps=function(i,a){return i.shouldAnimate?i.on?{isVisible:!0,data:i.on,animate:"open"}:a.isVisible?{isVisible:!0,data:a.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!i.on,data:i.on,animate:"none"}};var t=r.prototype;return t.render=function(){if(!this.state.isVisible)return null;var i={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(i)},r}(T.PureComponent),Yt={dragging:5e3,dropAnimating:4500},ns=function(r,t){return t?Ae.drop(t.duration):r?Ae.snap:Ae.fluid},as=function(r,t){return r?t?Ne.opacity.drop:Ne.opacity.combining:null},is=function(r){return r.forceShouldAnimate!=null?r.forceShouldAnimate:r.mode==="SNAP"};function os(e){var r=e.dimension,t=r.client,n=e.offset,i=e.combineWith,a=e.dropping,o=!!i,l=is(e),u=!!a,d=u?Ur.drop(n,o):Ur.moveTo(n),p={position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:ns(l,a),transform:d,opacity:as(o,u),zIndex:u?Yt.dropAnimating:Yt.dragging,pointerEvents:"none"};return p}function ls(e){return{transform:Ur.moveTo(e.offset),transition:e.shouldAnimateDisplacement?null:"none"}}function us(e){return e.type==="DRAGGING"?os(e):ls(e)}function ss(e,r,t){t===void 0&&(t=G);var n=window.getComputedStyle(r),i=r.getBoundingClientRect(),a=cn(i,n),o=nr(a,t),l={client:a,tagName:r.tagName.toLowerCase(),display:n.display},u={x:a.marginBox.width,y:a.marginBox.height},d={descriptor:e,placeholder:l,displaceBy:u,client:a,page:o};return d}function cs(e){var r=ct("draggable"),t=e.descriptor,n=e.registry,i=e.getDraggableRef,a=e.canDragInteractiveElements,o=e.shouldRespectForcePress,l=e.isEnabled,u=A(function(){return{canDragInteractiveElements:a,shouldRespectForcePress:o,isEnabled:l}},[a,l,o]),d=C(function(v){var f=i();return f||h(!1),ss(t,f,v)},[t,i]),p=A(function(){return{uniqueId:r,descriptor:t,options:u,getDimension:d}},[t,d,u,r]),s=I.useRef(p),c=I.useRef(!0);$(function(){return n.draggable.register(s.current),function(){return n.draggable.unregister(s.current)}},[n.draggable]),$(function(){if(c.current){c.current=!1;return}var v=s.current;s.current=p,n.draggable.update(p,v)},[p,n.draggable])}function ds(e){e.preventDefault()}function ps(e){var r=I.useRef(null),t=C(function(P){r.current=P},[]),n=C(function(){return r.current},[]),i=sr(br),a=i.contextId,o=i.dragHandleUsageInstructionsId,l=i.registry,u=sr(pt),d=u.type,p=u.droppableId,s=A(function(){return{id:e.draggableId,index:e.index,type:d,droppableId:p}},[e.draggableId,e.index,d,p]),c=e.children,v=e.draggableId,f=e.isEnabled,g=e.shouldRespectForcePress,m=e.canDragInteractiveElements,b=e.isClone,y=e.mapped,x=e.dropAnimationFinished;if(!b){var D=A(function(){return{descriptor:s,registry:l,getDraggableRef:n,canDragInteractiveElements:m,shouldRespectForcePress:g,isEnabled:f}},[s,l,n,m,g,f]);cs(D)}var E=A(function(){return f?{tabIndex:0,role:"button","aria-describedby":o,"data-rbd-drag-handle-draggable-id":v,"data-rbd-drag-handle-context-id":a,draggable:!1,onDragStart:ds}:null},[a,o,v,f]),S=C(function(P){y.type==="DRAGGING"&&y.dropping&&P.propertyName==="transform"&&x()},[x,y]),B=A(function(){var P=us(y),M=y.type==="DRAGGING"&&y.dropping?S:null,R={innerRef:t,draggableProps:{"data-rbd-draggable-context-id":a,"data-rbd-draggable-id":v,style:P,onTransitionEnd:M},dragHandleProps:E};return R},[a,E,v,y,S,t]),N=A(function(){return{draggableId:s.id,type:s.type,source:{index:s.index,droppableId:s.droppableId}}},[s.droppableId,s.id,s.index,s.type]);return c(B,y.snapshot,N)}var na=function(e,r){return e===r},aa=function(e){var r=e.combine,t=e.destination;return t?t.droppableId:r?r.droppableId:null},vs=function(r){return r.combine?r.combine.draggableId:null},fs=function(r){return r.at&&r.at.type==="COMBINE"?r.at.combine.draggableId:null};function gs(){var e=F(function(i,a){return{x:i,y:a}}),r=F(function(i,a,o,l,u){return{isDragging:!0,isClone:a,isDropAnimating:!!u,dropAnimation:u,mode:i,draggingOver:o,combineWith:l,combineTargetFor:null}}),t=F(function(i,a,o,l,u,d,p){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:u,combineWith:d,mode:a,offset:i,dimension:o,forceShouldAnimate:p,snapshot:r(a,l,u,d,null)}}}),n=function(a,o){if(a.isDragging){if(a.critical.draggable.id!==o.draggableId)return null;var l=a.current.client.offset,u=a.dimensions.draggables[o.draggableId],d=V(a.impact),p=fs(a.impact),s=a.forceShouldAnimate;return t(e(l.x,l.y),a.movementMode,u,o.isClone,d,p,s)}if(a.phase==="DROP_ANIMATING"){var c=a.completed;if(c.result.draggableId!==o.draggableId)return null;var v=o.isClone,f=a.dimensions.draggables[o.draggableId],g=c.result,m=g.mode,b=aa(g),y=vs(g),x=a.dropDuration,D={duration:x,curve:nt.drop,moveTo:a.newHomeClientOffset,opacity:y?Ne.opacity.drop:null,scale:y?Ne.scale.drop:null};return{mapped:{type:"DRAGGING",offset:a.newHomeClientOffset,dimension:f,dropping:D,draggingOver:b,combineWith:y,mode:m,forceShouldAnimate:null,snapshot:r(m,v,b,y,D)}}}return null};return n}function ia(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var ms={mapped:{type:"SECONDARY",offset:G,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:ia(null)}};function bs(){var e=F(function(o,l){return{x:o,y:l}}),r=F(ia),t=F(function(o,l,u){return l===void 0&&(l=null),{mapped:{type:"SECONDARY",offset:o,combineTargetFor:l,shouldAnimateDisplacement:u,snapshot:r(l)}}}),n=function(l){return l?t(G,l,!0):null},i=function(l,u,d,p){var s=d.displaced.visible[l],c=!!(p.inVirtualList&&p.effected[l]),v=vr(d),f=v&&v.draggableId===l?u:null;if(!s){if(!c)return n(f);if(d.displaced.invisible[l])return null;var g=ye(p.displacedBy.point),m=e(g.x,g.y);return t(m,f,!0)}if(c)return n(f);var b=d.displacedBy.point,y=e(b.x,b.y);return t(y,f,s.shouldAnimate)},a=function(l,u){if(l.isDragging)return l.critical.draggable.id===u.draggableId?null:i(u.draggableId,l.critical.draggable.id,l.impact,l.afterCritical);if(l.phase==="DROP_ANIMATING"){var d=l.completed;return d.result.draggableId===u.draggableId?null:i(u.draggableId,d.result.draggableId,d.impact,d.afterCritical)}return null};return a}var hs=function(){var r=gs(),t=bs(),n=function(a,o){return r(a,o)||t(a,o)||ms};return n},ys={dropAnimationFinished:Wn},Ds=un(hs,ys,null,{context:st,pure:!0,areStatePropsEqual:na})(ps);function oa(e){var r=sr(pt),t=r.isUsingCloneFor;return t===e.draggableId&&!e.isClone?null:T.createElement(Ds,e)}function Ts(e){var r=typeof e.isDragDisabled=="boolean"?!e.isDragDisabled:!0,t=!!e.disableInteractiveElementBlocking,n=!!e.shouldRespectForcePress;return T.createElement(oa,w({},e,{isClone:!1,isEnabled:r,canDragInteractiveElements:t,shouldRespectForcePress:n}))}function xs(e){var r=I.useContext(br);r||h(!1);var t=r.contextId,n=r.isMovementAllowed,i=I.useRef(null),a=I.useRef(null),o=e.children,l=e.droppableId,u=e.type,d=e.mode,p=e.direction,s=e.ignoreContainerClipping,c=e.isDropDisabled,v=e.isCombineEnabled,f=e.snapshot,g=e.useClone,m=e.updateViewportMaxScroll,b=e.getContainerForClone,y=C(function(){return i.current},[]),x=C(function(R){i.current=R},[]);C(function(){return a.current},[]);var D=C(function(R){a.current=R},[]),E=C(function(){n()&&m({maxScroll:Vn()})},[n,m]);Qu({droppableId:l,type:u,mode:d,direction:p,isDropDisabled:c,isCombineEnabled:v,ignoreContainerClipping:s,getDroppableRef:y});var S=T.createElement(ts,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(R){var z=R.onClose,j=R.data,L=R.animate;return T.createElement(rs,{placeholder:j,onClose:z,innerRef:D,animate:L,contextId:t,onTransitionEnd:E})}),B=A(function(){return{innerRef:x,placeholder:S,droppableProps:{"data-rbd-droppable-id":l,"data-rbd-droppable-context-id":t}}},[t,l,S,x]),N=g?g.dragging.draggableId:null,P=A(function(){return{droppableId:l,type:u,isUsingCloneFor:N}},[l,N,u]);function M(){if(!g)return null;var R=g.dragging,z=g.render,j=T.createElement(oa,{draggableId:R.draggableId,index:R.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(L,Y){return z(L,Y,R)});return sa.createPortal(j,b())}return T.createElement(pt.Provider,{value:P},o(B,f),M())}var Lr=function(r,t){return r===t.droppable.type},Kt=function(r,t){return t.draggables[r.draggable.id]},Is=function(){var r={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=w({},r,{shouldAnimatePlaceholder:!1}),n=F(function(o){return{draggableId:o.id,type:o.type,source:{index:o.index,droppableId:o.droppableId}}}),i=F(function(o,l,u,d,p,s){var c=p.descriptor.id,v=p.descriptor.droppableId===o;if(v){var f=s?{render:s,dragging:n(p.descriptor)}:null,g={isDraggingOver:u,draggingOverWith:u?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!1,snapshot:g,useClone:f}}if(!l)return t;if(!d)return r;var m={isDraggingOver:u,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:p.placeholder,shouldAnimatePlaceholder:!0,snapshot:m,useClone:null}}),a=function(l,u){var d=u.droppableId,p=u.type,s=!u.isDropDisabled,c=u.renderClone;if(l.isDragging){var v=l.critical;if(!Lr(p,v))return t;var f=Kt(v,l.dimensions),g=V(l.impact)===d;return i(d,s,g,g,f,c)}if(l.phase==="DROP_ANIMATING"){var m=l.completed;if(!Lr(p,m.critical))return t;var b=Kt(m.critical,l.dimensions);return i(d,s,aa(m.result)===d,V(m.impact)===d,b,c)}if(l.phase==="IDLE"&&l.completed&&!l.shouldFlush){var y=l.completed;if(!Lr(p,y.critical))return t;var x=V(y.impact)===d,D=!!(y.impact.at&&y.impact.at.type==="COMBINE"),E=y.critical.droppable.id===d;return x?D?r:t:E?r:t}return t};return a},Cs={updateViewportMaxScroll:No};function Ss(){return document.body||h(!1),document.body}var ws={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:Ss},Es=un(Is,Cs,null,{context:st,pure:!0,areStatePropsEqual:na})(xs);Es.defaultProps=ws;export{Es as C,Os as D,Ts as P};
