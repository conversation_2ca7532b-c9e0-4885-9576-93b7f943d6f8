import { lazy } from "react";

export const AdminForgotPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminLoginPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminSignUpPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

// User Auth Pages
export const UserLoginPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserLoginPage");
  __import.finally(() => {});
  return __import;
});

export const UserSignUpPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const UserForgotPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserForgotPage");
  __import.finally(() => {});
  return __import;
});

export const UserResetPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserResetPage");
  __import.finally(() => {});
  return __import;
});

// User Pages
export const ProjectsPage = lazy(() => {
  const __import = import("@/pages/User/View/ProjectsPage");
  __import.finally(() => {});
  return __import;
});

export const DocumentUploadPage = lazy(() => {
  const __import = import("@/pages/User/View/DocumentUploadPage");
  __import.finally(() => {});
  return __import;
});

export const DocumentAnalysisResultsPage = lazy(() => {
  const __import = import("@/pages/User/View/DocumentAnalysisResultsPage");
  __import.finally(() => {});
  return __import;
});

export const AIReportPage = lazy(() => {
  const __import = import("@/pages/User/View/AIReportPage");
  __import.finally(() => {});
  return __import;
});

export const DocumentSectionEditorPage = lazy(() => {
  const __import = import("@/pages/User/View/DocumentSectionEditorPage");
  __import.finally(() => {});
  return __import;
});

export const ReviewChangesPage = lazy(() => {
  const __import = import("@/pages/User/View/ReviewChangesPage");
  __import.finally(() => {});
  return __import;
});

export const UserProfilePage = lazy(() => {
  const __import = import("@/pages/User/View/UserProfilePage");
  __import.finally(() => {});
  return __import;
});

// OTHERS

export const TestComponents = lazy(() => {
  const __import = import("@/pages/PG/Custom/TestComponents");
  __import.finally(() => {});
  return __import;
});

export const DoctorLoginPage = lazy(() => {
  const __import = import("@/pages/Doctor/Auth/DoctorLoginPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorForgotPage = lazy(() => {
  const __import = import("@/pages/Doctor/Auth/DoctorForgotPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorResetPage = lazy(() => {
  const __import = import("@/pages/Doctor/Auth/DoctorResetPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorSignUpPage = lazy(() => {
  const __import = import("@/pages/Doctor/Auth/DoctorSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorAiGeneratedSheet = lazy(() => {
  const __import = import("@/pages/Doctor/View/DoctorAiGeneratedSheet");
  __import.finally(() => {});
  return __import;
});

export const DoctorReportPage = lazy(() => {
  const __import = import("@/pages/Doctor/View/DoctorReportPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorSectionEditorPage = lazy(() => {
  const __import = import("@/pages/Doctor/View/DoctorSectionEditorPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorReportOverviewPage = lazy(() => {
  const __import = import("@/pages/Doctor/View/DoctorReportOverviewPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorProjectsPage = lazy(() => {
  const __import = import("@/pages/Doctor/View/ProjectsPage");
  __import.finally(() => {});
  return __import;
});

export const DoctorProfilePage = lazy(() => {
  const __import = import("@/pages/Doctor/Profile/DoctorProfilePage");
  __import.finally(() => {});
  return __import;
});
