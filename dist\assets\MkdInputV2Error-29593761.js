import{j as m}from"./@react-google-maps/api-5b2d83cc.js";import{u as p}from"./MkdInputV2Context-f6333041.js";import{S as s}from"./index-95f0e460.js";import"./vendor-489b60f1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const h=({className:o="",...e})=>{const{name:t,errors:r}=p(),i=new s;return!t||!r||!r[t]||!r[t].message?null:m.jsx("p",{className:`text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500 ${o}`,...e,children:i.Capitalize(r[t].message,{separator:" "})})};export{h as default};
