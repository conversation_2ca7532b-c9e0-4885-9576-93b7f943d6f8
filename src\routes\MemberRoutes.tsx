import React, { memo, useEffect, lazy } from "react";
import { Navigate } from "react-router";
import metadataJSON from "@/utils/metadata.json";
import { StringCaser } from "@/utils/utils";
import { useContexts } from "@/hooks/useContexts";

interface MemberRouteProps {
  path: string;
  children: React.ReactNode;
}

const MemberRoute: React.FC<MemberRouteProps> = ({ path, children }) => {
  const { authState } = useContexts();
  const stringCaser = new StringCaser();

  const { isAuthenticated } = authState;

  useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = metadata?.title
        ? stringCaser.Capitalize(metadata?.title, {
            separator: " ",
          })
        : "";
    } else {
      document.title = "";
    }
  }, [path]);

  return (
    <>
      {isAuthenticated ? (
        <>{children}</>
      ) : (
        <Navigate to="/user/login" replace />
      )}
    </>
  );
};

export default memo(MemberRoute);
