import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{a as I,u as v,T as N,e as O}from"./index-95f0e460.js";import{I as f}from"./index-ec6e151a.js";import{F as A,a as J,D,b as P,S as E}from"./index.esm-05b2469a.js";import{r as o}from"./vendor-489b60f1.js";import{b as Q,c as U}from"./@tanstack/react-query-dc4b6186.js";import{q as w,M as z}from"./models-4d813338.js";import{u as H}from"./react-hook-form-7e42b371.js";import{o as Y}from"./yup-fe85ba88.js";import{c as G,a as S}from"./yup-5d8330af.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./react-icons-fe0a0adf.js";import"./@hookform/resolvers-6b9dee20.js";const V=a=>{const{tdk:s}=I(),{showToast:C,tokenExpireError:t}=v(),c=Q();return U({mutationFn:async n=>await s.create(a,n),onSuccess:()=>{var n,g,h,x,j,d,m,y;c.invalidateQueries({queryKey:[(g=(n=w)==null?void 0:n[a])==null?void 0:g.all,a]}),c.invalidateQueries({queryKey:[(x=(h=w)==null?void 0:h[a])==null?void 0:x.list,a]}),c.invalidateQueries({queryKey:[(d=(j=w)==null?void 0:j[a])==null?void 0:d.many,a]}),c.invalidateQueries({queryKey:[(y=(m=w)==null?void 0:m[a])==null?void 0:y.paginate,a]}),C("Created successfully",5e3,N.SUCCESS)},onError:n=>{C(n.message,5e3,N.ERROR),t(n.message),console.error(n)}})},Z=({onSuccess:a})=>{const{mutateAsync:s,isPending:C}=V(z.PROJECT),{showToast:t,tokenExpireError:c}=v(),i=G({name:S().matches(/^[a-zA-Z0-9 ]*$/,"Only strings and numbers are allowed").required("name is required"),hostname:S().required("hostname is required"),slug:S().required("slug is required")}).required(),{register:n,handleSubmit:g,setError:h,setValue:x,watch:j,formState:{errors:d}}=H({resolver:Y(i)}),{slug:m}=j(),y=async u=>{try{const p={name:u.name,slug:u.slug,hostname:u.hostname},l=await s(p);if(l!=null&&l.error)throw new Error(l==null?void 0:l.message);a&&a()}catch(p){h("slug",{type:"manual",message:p.message}),t(p.message,4e3,N.ERROR),c(p.message)}};return o.useEffect(()=>{const u=`${m||"<slug>"}.${O}`;x("hostname",u)},[m]),{errors:d,register:n,handleSubmit:g,onSubmit:y,isPending:C}},Se=({})=>{const{globalState:{project:a}}=v();Z({});const[s,C]=o.useState({}),[t,c]=o.useState({state:!1,target:"page"}),[i,n]=o.useState([]);o.useState([]);const[g,h]=o.useState("");o.useState(""),o.useState("front-end"),o.useState(!1),o.useState({});const[x,j]=o.useState(!1),[d,m]=o.useState({state:!1,platform:""}),y=async r=>{},u=async()=>{},p=async()=>{},l=async r=>{},B=()=>{},$=(r="")=>{const _=r.split("_");if(_.length<=1)return r;const b=new Date(Number(_[1])),R=b.getFullYear(),q=b.getMonth()+1,M=b.getDate(),T=b.getHours(),K=b.getMinutes(),L=b.getSeconds();return`${_[0]} - Commit ${R}-${q.toString().padStart(2,"0")}-${M.toString().padStart(2,"0")} ${T.toString().padStart(2,"0")}:${K.toString().padStart(2,"0")}:${L.toString().padStart(2,"0")}`},F=[{id:1,name:"Deployment",actionBtn:()=>e.jsx(f,{className:"flex items-center rounded-md px-3 py-2 shadow-sm !cursor-default border border-[#C6C6C6] !bg-green-500",loading:t.state==!0&&t.target=="initializing",disabled:!0,children:"Initialized"}),icon:()=>e.jsx(A,{className:"h-6 w-6 text-green-500"})},{id:6,name:"Domain",actionBtn:()=>s.has_domain?s.has_domain?e.jsx(f,{className:`flex cursor-pointer items-center rounded-md border border-[#C6C6C6] !bg-[#DC2626]  px-3 py-2 shadow-sm ${t.state==!0&&t.target=="delete_domain"?"!cursor-not-allowed !bg-yellow-700":""} `,loading:t.state==!0&&t.target=="delete_domain",disabled:t.state==!0&&t.target=="delete_domain",onClick:r=>p(),children:"Delete"}):null:e.jsx(f,{className:`flex cursor-pointer items-center rounded-md bg-primaryBlue px-3 py-2 text-white shadow-sm ${t.state==!0&&t.target=="create_domain"?"!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700":""} `,loading:t.state==!0&&t.target=="create_domain",disabled:t.state==!0&&t.target=="create_domain",onClick:r=>u(),children:"Create"}),icon:()=>e.jsx(J,{className:"h-6 w-6 text-yellow-700"})},{id:2,name:"React repository",actionBtn:()=>e.jsxs("div",{className:"flex items-center gap-4",children:[s.has_fe_repo?e.jsx(D,{text:`http://23.29.118.76:3000/mkdlabs/${a.slug}_frontend.git`}):"",e.jsx(f,{className:`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${s.has_fe_repo?"!cursor-not-allowed border border-[#C6C6C6] !bg-green-500":t.state===!0&&t.target==="create_frontend_repo"?"!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700":"bg-primaryBlue text-white"} `,loading:t.state==!0&&t.target=="create_frontend_repo",disabled:s.has_fe_repo||t.state===!0&&t.target==="create_frontend_repo",onClick:()=>y(),children:s.has_fe_repo?"Created":"Create"})]}),icon:()=>e.jsx(P,{className:"h-6 w-6 text-blue-500"})},{id:9,name:"React Deploy",actionBtn:()=>e.jsxs("div",{className:"flex items-center gap-4",children:[i.length>0&&e.jsxs("select",{name:"",id:"",className:"rounded-md border border-[#C6C6C6] px-3 py-1.5 shadow-sm",onChange:r=>h(r.target.value),children:[e.jsx("option",{disabled:!0,children:"Choose branch to deploy"}),i.map(r=>e.jsx("option",{value:r.name,children:$(r.name)},r.name))]}),e.jsx(f,{className:`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${i.length==0?"!cursor-not-allowed border border-[#C6C6C6] !bg-black":s.fe_deployed?"!cursor-not-allowed border border-[#C6C6C6] !bg-green-500":t.state==!0&&t.target=="deploy_frontend"?"!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700":"bg-primaryBlue text-white"} `,loading:t.state==!0&&t.target=="deploy_frontend",disabled:i.length==0||!s.has_domain||s.fe_deployed||t.state==!0&&t.target=="deploy_frontend"||d.state==!0&&d.platform=="frontend",onClick:r=>B(),children:s.fe_deployed?"Deployed":d.state==!0&&d.platform=="frontend"?"Fetching branches...":i.length==0?"No branch to deploy":"Deploy"})]}),icon:()=>e.jsx(E,{className:"h-6 w-6 text-purple-900"})},{id:7,name:"React Jenkins job",actionBtn:()=>e.jsxs("div",{className:"flex items-center gap-4",children:[s.has_fe_job?e.jsx(D,{text:`http://23.29.118.76:8080/job/${a.slug}_frontend/`}):"",e.jsx(f,{className:`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${s.has_fe_job?"!cursor-not-allowed border border-[#C6C6C6] !bg-green-500":t.state==!0&&t.target=="create_frontend_job"?"!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700":"bg-primaryBlue text-white"} `,loading:t.state==!0&&t.target=="create_frontend_job",disabled:s.has_fe_job||t.state==!0&&t.target=="create_frontend_job",onClick:r=>l(),children:s.has_fe_job?"Created":"Create"})]}),icon:()=>e.jsx(E,{className:"h-6 w-6 text-blue-900"})}],k=[];return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"relative px-10 pb-6 pt-3 flex w-full rounded-b-md border-t-0  bg-white py-2 text-sm text-gray-700 ",children:e.jsxs("ol",{className:"list-decimal",children:[e.jsx("li",{children:"Create Domain"}),e.jsx("li",{children:"Create React repository"}),e.jsx("li",{children:"Click to Deploy"}),e.jsx("li",{children:"Create React Jenkins job"})]})}),e.jsx("div",{className:"flex justify-between border-t border-[#E0E0E0] bg-[#f9f9f9] px-8",children:e.jsx("div",{className:"mb-4 mt-6 w-full rounded-md border border-[#E0E0E0] bg-white px-5 py-3",children:e.jsxs("div",{className:"divide-y",children:[t.state==!0&&t.target=="page"?e.jsx(e.Fragment,{children:["","","","","","","","",""].map((r,_)=>e.jsx("div",{className:" rounded-md border-b-0 border-l-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ",children:e.jsx("div",{className:"my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"})},_))}):F.map(r=>e.jsxs("div",{className:"flex items-center justify-between py-6 text-sm text-[#393939]",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{children:r.icon()}),e.jsx("span",{children:r.name})]}),e.jsx("div",{children:r.actionBtn()})]},r.id)),x?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:" rounded-md border-b-0 border-r-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ",children:e.jsx("div",{className:"my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"})}),e.jsx("div",{className:" rounded-md border-b-0 border-l-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ",children:e.jsx("div",{className:"my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"})})]}):k.map(r=>e.jsxs("div",{className:"flex items-center justify-between py-6 text-sm text-[#393939]",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{children:r.icon()}),e.jsx("span",{children:r.name})]}),e.jsx("div",{children:r.actionBtn()})]},r.id))]})})})]})};export{Se as default};
