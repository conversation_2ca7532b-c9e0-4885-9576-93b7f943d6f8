import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as c,R as B,d as M}from"./vendor-489b60f1.js";import{u as S,E as R}from"./@tiptap/react-8d1cdd44.js";import{S as I}from"./@tiptap/starter-kit-8dc7719e.js";import{T as V}from"./@tiptap/extension-text-align-4d325d88.js";import{U as T}from"./@tiptap/extension-underline-5fc56973.js";import{T as E}from"./@tiptap/extension-table-a2bc9598.js";import{T as D}from"./@tiptap/extension-table-row-159ab625.js";import{T as Z}from"./@tiptap/extension-table-cell-b57d510e.js";import{T as P}from"./@tiptap/extension-table-header-412f6f9c.js";import{b as N}from"./index-95f0e460.js";import{D as O,C as _,P as $}from"./react-beautiful-dnd-047ecf8c.js";import"./@tiptap/extension-highlight-1b3a19b9.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@mantine/core-d9c5c65c.js";import"./redux-3b07d581.js";import"./react-select-8c03feb0.js";import"./@craftjs/core-ae02137e.js";const U=({open:t,onClose:m,onBeginReview:f,sections:h})=>{const[o,r]=c.useState(h);B.useEffect(()=>{r(h)},[h]);const j=d=>{if(!d.destination)return;const x=Array.from(o),[w]=x.splice(d.source.index,1);x.splice(d.destination.index,0,w),r(x.map((g,y)=>({...g,order:y+1})))};return t?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md p-6 flex flex-col gap-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl text-gray-800 font-semibold",children:"Review Order"}),e.jsx("button",{className:"p-1","aria-label":"Close",onClick:m,children:e.jsx("svg",{width:12,height:16,viewBox:"0 0 12 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z",fill:"#4B5563"})})})]}),e.jsx("div",{className:"text-gray-600",children:"Arrange sections in order of review priority. Drag to reorder."}),e.jsx(O,{onDragEnd:j,children:e.jsx(_,{droppableId:"sections",children:d=>e.jsxs("div",{...d.droppableProps,ref:d.innerRef,className:"flex flex-col gap-2 max-h-[300px] overflow-y-auto",children:[o.map((x,w)=>e.jsx($,{draggableId:x.id,index:w,children:g=>e.jsxs("div",{ref:g.innerRef,...g.draggableProps,className:"flex items-center gap-3 p-3 w-full h-12 rounded-md bg-gray-50",children:[e.jsx("div",{...g.dragHandleProps,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"10",height:"14",viewBox:"0 0 10 14",fill:"none",children:e.jsx("path",{d:"M1.25 10H2.75C3.44062 10 4 10.5594 4 11.25V12.75C4 13.4406 3.44062 14 2.75 14H1.25C0.559375 14 0 13.4406 0 12.75V11.25C0 10.5594 0.559375 10 1.25 10ZM7.25 10H8.75C9.44063 10 10 10.5594 10 11.25V12.75C10 13.4406 9.44063 14 8.75 14H7.25C6.55937 14 6 13.4406 6 12.75V11.25C6 10.5594 6.55937 10 7.25 10ZM1.25 9C0.559375 9 0 8.44063 0 7.75V6.25C0 5.55937 0.559375 5 1.25 5H2.75C3.44062 5 4 5.55937 4 6.25V7.75C4 8.44063 3.44062 9 2.75 9H1.25ZM7.25 5H8.75C9.44063 5 10 5.55937 10 6.25V7.75C10 8.44063 9.44063 9 8.75 9H7.25C6.55937 9 6 8.44063 6 7.75V6.25C6 5.55937 6.55937 5 7.25 5ZM1.25 4C0.559375 4 0 3.44062 0 2.75V1.25C0 0.559375 0.559375 0 1.25 0H2.75C3.44062 0 4 0.559375 4 1.25V2.75C4 3.44062 3.44062 4 2.75 4H1.25ZM7.25 0H8.75C9.44063 0 10 0.559375 10 1.25V2.75C10 3.44062 9.44063 4 8.75 4H7.25C6.55937 4 6 3.44062 6 2.75V1.25C6 0.559375 6.55937 0 7.25 0Z",fill:"#9CA3AF"})})}),e.jsx("span",{className:"text-gray-800",children:x.title}),e.jsx("svg",{className:"ml-auto",width:10,height:16,viewBox:"0 0 10 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.70615 0.293945C5.31553 -0.0966797 4.68115 -0.0966797 4.29053 0.293945L1.29053 3.29395C0.899902 3.68457 0.899902 4.31895 1.29053 4.70957C1.68115 5.1002 2.31553 5.1002 2.70615 4.70957L3.9999 3.41582V12.5846L2.70615 11.2939C2.31553 10.9033 1.68115 10.9033 1.29053 11.2939C0.899902 11.6846 0.899902 12.3189 1.29053 12.7096L4.29053 15.7096C4.68115 16.1002 5.31553 16.1002 5.70615 15.7096L8.70615 12.7096C9.09678 12.3189 9.09678 11.6846 8.70615 11.2939C8.31553 10.9033 7.68115 10.9033 7.29053 11.2939L5.9999 12.5846V3.41582L7.29365 4.70957C7.68428 5.1002 8.31865 5.1002 8.70928 4.70957C9.0999 4.31895 9.0999 3.68457 8.70928 3.29395L5.70928 0.293945H5.70615Z",fill:"#9CA3AF"})})]})},x.id)),d.placeholder]})})}),e.jsxs("div",{className:"flex justify-end gap-3 mt-4",children:[e.jsx("button",{className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-md",onClick:m,children:"Cancel"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 bg-gray-800 text-white rounded-md",onClick:()=>f(o),children:[e.jsx("svg",{width:13,height:16,viewBox:"0 0 13 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.39062 0C5.08437 0 3.97187 0.834375 3.5625 2H2.39062C1.2875 2 0.390625 2.89687 0.390625 4V14C0.390625 15.1031 1.2875 16 2.39062 16H10.3906C11.4937 16 12.3906 15.1031 12.3906 14V4C12.3906 2.89687 11.4937 2 10.3906 2H9.21875C8.80937 0.834375 7.69688 0 6.39062 0Z",fill:"white"})}),"Begin Review"]})]}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"This is for editing purposes only and won't be the final report."})]})}):null},W=({editor:t})=>{var m,f;return c.useState(!1),c.useState(!1),t?e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex items-center gap-2 border-b px-2 py-2 bg-white opacity-50 pointer-events-none",children:[e.jsx("button",{type:"button","aria-label":"Bold","aria-pressed":t.isActive("bold"),className:`px-2 py-1 rounded ${t.isActive("bold")?"font-bold text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBold().run(),disabled:!0,children:e.jsx("b",{children:"B"})}),e.jsx("button",{type:"button","aria-label":"Italic","aria-pressed":t.isActive("italic"),className:`px-2 py-1 rounded ${t.isActive("italic")?"italic text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleItalic().run(),disabled:!0,children:e.jsx("i",{children:"I"})}),e.jsx("button",{type:"button","aria-label":"Underline","aria-pressed":t.isActive("underline"),className:`px-2 py-1 rounded ${t.isActive("underline")?"underline text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>{var h,o;return(o=(h=t.chain().focus()).toggleUnderline)==null?void 0:o.call(h).run()},disabled:!((f=(m=t.can()).toggleUnderline)!=null&&f.call(m)),children:e.jsx("u",{children:"U"})}),e.jsx("button",{type:"button","aria-label":"Bulleted List","aria-pressed":t.isActive("bulletList"),className:`px-2 py-1 rounded ${t.isActive("bulletList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBulletList().run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("circle",{cx:"4",cy:"5",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"9",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"13",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"12",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Numbered List","aria-pressed":t.isActive("orderedList"),className:`px-2 py-1 rounded ${t.isActive("orderedList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleOrderedList().run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("text",{x:"2",y:"7",fontSize:"6",fill:"currentColor",children:"1."}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("text",{x:"2",y:"13",fontSize:"6",fill:"currentColor",children:"2."}),e.jsx("rect",{x:"7",y:"10",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("span",{className:"mx-2 border-l h-6"}),e.jsx("button",{type:"button","aria-label":"Align Left","aria-pressed":t.isActive({textAlign:"left"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"left"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("left").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Center","aria-pressed":t.isActive({textAlign:"center"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"center"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("center").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"5",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Right","aria-pressed":t.isActive({textAlign:"right"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"right"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("right").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Justify","aria-pressed":t.isActive({textAlign:"justify"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"justify"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("justify").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})})]}),e.jsxs("div",{className:"flex items-center justify-between px-2 py-2",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{type:"button","aria-label":"Undo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>t.chain().focus().undo().run(),disabled:!0,children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M7 4L3 8L7 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Undo"]}),e.jsxs("button",{type:"button","aria-label":"Redo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>t.chain().focus().redo().run(),disabled:!0,children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M11 4L15 8L11 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Redo"]})]}),e.jsx("div",{className:"flex gap-6 items-center",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-blue-100 border border-blue-300"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"Original AI Text"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-gray-200 border border-gray-400"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"New Changes"})]})]})})]})]}):null},F=()=>e.jsx("svg",{width:13,height:16,viewBox:"0 0 13 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_1_1864)",children:e.jsx("path",{d:"M2.54688 0C1.44375 0 0.546875 0.896875 0.546875 2V14C0.546875 15.1031 1.44375 16 2.54688 16H10.5469C11.65 16 12.5469 15.1031 12.5469 14V5H8.54688C7.99375 5 7.54688 4.55312 7.54688 4V0H2.54688ZM8.54688 0V4H12.5469L8.54688 0ZM4.04688 8H9.04688C9.32187 8 9.54688 8.225 9.54688 8.5C9.54688 8.775 9.32187 9 9.04688 9H4.04688C3.77187 9 3.54688 8.775 3.54688 8.5C3.54688 8.225 3.77187 8 4.04688 8Z",fill:"white"})})}),z=()=>e.jsx("svg",{width:14,height:14,viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_1_1820)",children:e.jsx("path",{d:"M7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14Z",fill:"#10B981"})})}),K=()=>e.jsx("svg",{width:15,height:14,viewBox:"0 0 15 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_1_1825)",children:e.jsx("path",{d:"M7.15625 0C9.01277 0 10.7932 0.737498 12.106 2.05025C13.4188 3.36301 14.1562 5.14348 14.1562 7C14.1562 8.85652 13.4188 10.637 12.106 11.9497C10.7932 13.2625 9.01277 14 7.15625 14C5.29973 14 3.51926 13.2625 2.2065 11.9497C0.893748 10.637 0.15625 8.85652 0.15625 7C0.15625 5.14348 0.893748 3.36301 2.2065 2.05025C3.51926 0.737498 5.29973 0 7.15625 0Z",fill:"#3B82F6"})})}),ke=()=>{var C,v;const t=M(),[m,f]=c.useState(!1),[h,o]=c.useState(!1),[r,j]=c.useState(null),[d,x]=c.useState(!0),[w,g]=c.useState(null),y={name:localStorage.getItem("patientName")||"Mr. ClientLastName",age:"40 years"},s=S({extensions:[I.configure({heading:{levels:[1,2,3]},bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}}),T,V.configure({types:["heading","paragraph"]}),E.configure({resizable:!0}),D,Z,P],content:((C=r==null?void 0:r[0])==null?void 0:C.body)||"",editable:!1,onBlur:({editor:i})=>{const a=i.getHTML();j(n=>(n==null?void 0:n.map((u,l)=>l===0?{...u,body:a}:u))||[])},editorProps:{attributes:{class:"prose prose-sm max-w-none min-h-[400px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",spellCheck:"true","aria-label":`View ${((v=r==null?void 0:r[0])==null?void 0:v.title)||"Report"}`}},parseOptions:{preserveWhitespace:"full"}});c.useEffect(()=>{const i=async()=>{try{const n=localStorage.getItem("reportId");if(!n)throw new Error("No report ID found");const l=await new N().getReportSections(n);if(l.error)throw new Error(l.message||"Failed to fetch report data");if(!l.sections||!Array.isArray(l.sections))throw new Error("Invalid response format");j(l.sections),f(!0),g(null);const b=l.sections.map(p=>`${p.body}`).join("");s==null||s.commands.setContent(b)}catch(n){g(n instanceof Error?n.message:"An error occurred while fetching the report"),j(null)}finally{x(!1)}},a=setTimeout(()=>{i()},3500);return()=>clearTimeout(a)},[s]),c.useEffect(()=>{if(s&&r){const i=()=>{const n=s.getHTML().split("<h2>").filter(Boolean),u=r.map((l,b)=>{const H=(n[b]||"").replace(/^.*?<\/h2>/,"").trim();return{...l,body:H}});j(u)};return s.on("update",i),()=>{s.off("update",i)}}},[s,r]);const k=()=>o(!0),L=()=>o(!1),A=async i=>{try{const a=localStorage.getItem("reportId");if(!a)throw new Error("No report ID found");const u=i.map(p=>({id:parseInt(p.id),title:p.title,order:p.order,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),body:"",report_id:a})).map(p=>p.id.toString()),b=await new N().updateReportSectionOrder({report_id:a,order:u});if(b.error)throw new Error(b.message||"Failed to update section order");o(!1),t("/doctor/section-editor")}catch(a){console.error("Error updating section order:",a)}};return d?e.jsx("div",{className:"flex items-center h-screen w-full justify-center",children:e.jsx("div",{className:"w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):w?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-red-600",children:["Error loading report: ",w]})}):!r||r.length===0?e.jsx("div",{className:"flex min-h-screen items-center justify-center h-full",children:e.jsx("div",{className:"text-gray-600",children:"No report data available"})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-[1280px] mx-auto p-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-800",children:"Psychological Assessment Report"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>{t("/doctor/cheatsheet")},className:"flex items-center gap-2 bg-white text-violet-600 border border-violet-600 px-6 py-2 rounded-lg hover:bg-violet-50",children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14 2H2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V3C15 2.44772 14.5523 2 14 2Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5 5H11",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5 8H11",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5 11H8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),e.jsx("span",{children:"Generate Cheatsheet"})]}),e.jsxs("button",{onClick:k,className:"flex items-center gap-2 bg-violet-600 text-white px-6 py-2 rounded-lg hover:bg-violet-700",children:[e.jsx(F,{}),e.jsx("span",{children:"Proceed to Review"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{}),e.jsx("span",{children:"Verified by Psychometrist"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(K,{}),e.jsx("span",{children:"Last modified: May 7, 2025"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg",children:[e.jsx("div",{className:"bg-violet-50 p-4 m-4 rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Patient Name"}),e.jsx("p",{className:"font-medium text-gray-800",children:y.name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Age"}),e.jsx("p",{className:"font-medium text-gray-800",children:y.age})]})]})}),e.jsx("div",{className:"flex justify-center items-center w-full min-h-[900px] overflow-y-auto rounded-lg bg-white",children:e.jsxs("div",{className:"min-h-[811px]",children:[(s==null?void 0:s.isEditable)&&e.jsx(W,{editor:s}),e.jsx(R,{editor:s})]})})]})]})}),e.jsx(U,{open:h,onClose:L,onBeginReview:A,sections:(r==null?void 0:r.map(i=>({id:i.id.toString(),title:i.title,order:i.order||0})))||[]})]})};export{ke as default};
