import{j as n}from"./@react-google-maps/api-5b2d83cc.js";import{r as v}from"./vendor-489b60f1.js";import{M as I}from"./MkdInputV2Context-f6333041.js";const V=({name:s,type:r="text",value:l=null,onChange:o,register:t=null,errors:p=null,disabled:d=!1,required:i=!1,placeholder:u,options:e=[],mapping:a,customField:f=!1,children:m,className:c})=>{const x=v.useId();(r==="dropdown"||r==="select")&&!(e!=null&&e.length)&&console.error(`MkdInputV2: options prop is required for type="${r}". Please provide an array of options.`),r==="mapping"&&!a&&console.error('MkdInputV2: mapping prop is required for type="mapping". Please provide a mapping object.');const g=j=>{o&&o(j)};return n.jsx(I.Provider,{value:{id:x,name:s,type:r,value:l,onChange:g,register:t,errors:p,disabled:d,required:i,placeholder:u,options:e,mapping:a,customField:f},children:n.jsx("div",{className:c,children:m})})};export{V as default};
