import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import"./vendor-489b60f1.js";const a=({className:t="",loaderStroke:n="#4F46E5",checkStroke:r="#059669",onClick:s,title:i,icon:o="check"})=>e.jsxs("div",{title:i,children:[o==="loader"?e.jsx("svg",{className:`animate animate-spin ${t}`,onClick:s,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:e.jsx("path",{d:"M1.3335 6.66667C1.3335 6.66667 2.67015 4.84548 3.75605 3.75883C4.84196 2.67218 6.34256 2 8.00016 2C11.3139 2 14.0002 4.68629 14.0002 8C14.0002 11.3137 11.3139 14 8.00016 14C5.26477 14 2.9569 12.1695 2.23467 9.66667M1.3335 6.66667V2.66667M1.3335 6.66667H5.3335",stroke:n,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}):null,o==="check"?e.jsx("svg",{className:`${t}`,onClick:s,xmlns:"http://www.w3.org/2000/svg",width:"19",height:"18",viewBox:"0 0 19 18",fill:"none",children:e.jsx("path",{d:"M15.5 4.5L7.25 12.75L3.5 9",stroke:r,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}):null]});export{a as default};
