import{j as o}from"./@react-google-maps/api-5b2d83cc.js";import{L as r}from"./index-95f0e460.js";import{a as t}from"./html2pdf.js-82514bbc.js";import{r as i}from"./vendor-489b60f1.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const m=i.lazy(()=>t(()=>import("./DoctorProfile-ee13b354.js"),["assets/DoctorProfile-ee13b354.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index-95f0e460.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-d4c6ce51.css","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css","assets/index-e9605eb4.js","assets/index-32ecee74.js","assets/index-fe4acb22.js","assets/index-235b3e94.js"])),v=()=>o.jsx(r,{children:o.jsx(m,{})});export{v as default};
