import React, { useState } from "react";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum, RoleEnum } from "@/utils/Enums";
import { useSDK } from "@/hooks/useSDK";

interface IUserLoginPageProps {
  role?: string;
}

const UserLoginPage: React.FC<IUserLoginPageProps> = ({
  role = RoleEnum.USER,
}) => {
  const { sdk } = useSDK();
  const { authDispatch: dispatch, showToast } = useContexts();
  const [submitLoading, setSubmitLoading] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const schema = yup
    .object({
      email: yup.string().email().required(),
      password: yup.string().required(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(data.email, data.password, role);

      if (!result.error) {
        dispatch({
          type: "LOGIN",
          payload: result as any,
        });
        showToast("Successfully Logged In", 4000, ToastStatusEnum.SUCCESS);
        navigate(redirect_uri ?? "/projects");
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field as "email" | "password", {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error: any) {
      setSubmitLoading(false);
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
      });
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header Section */}
        <header className="text-center">
          <div className="flex justify-center">
            <div className="w-16 h-16 rounded-full bg-gray-800 flex items-center justify-center">
              <svg
                width={30}
                height={30}
                viewBox="0 0 30 30"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
              >
                <g clipPath="url(#clip0_1_28)">
                  <path
                    d="M10.7812 0C12.5918 0 14.0625 1.4707 14.0625 3.28125V26.7188C14.0625 28.5293 12.5918 30 10.7812 30C9.08789 30 7.69336 28.7168 7.51758 27.0645C7.21289 27.1465 6.89062 27.1875 6.5625 27.1875C4.49414 27.1875 2.8125 25.5059 2.8125 23.4375C2.8125 23.0039 2.88867 22.582 3.02344 22.1953C1.25391 21.5273 0 19.8164 0 17.8125C0 15.9434 1.0957 14.3262 2.68359 13.5762C2.17383 12.9375 1.875 12.1289 1.875 11.25C1.875 9.45117 3.14063 7.95117 4.82812 7.58203C4.73438 7.25977 4.6875 6.91406 4.6875 6.5625C4.6875 4.81055 5.89453 3.33398 7.51758 2.92383C7.69336 1.2832 9.08789 0 10.7812 0ZM19.2188 0C20.9121 0 22.3008 1.2832 22.4824 2.92383C24.1113 3.33398 25.3125 4.80469 25.3125 6.5625C25.3125 6.91406 25.2656 7.25977 25.1719 7.58203C26.8594 7.94531 28.125 9.45117 28.125 11.25C28.125 12.1289 27.8262 12.9375 27.3164 13.5762C28.9043 14.3262 30 15.9434 30 17.8125C30 19.8164 28.7461 21.5273 26.9766 22.1953C27.1113 22.582 27.1875 23.0039 27.1875 23.4375C27.1875 25.5059 25.5059 27.1875 23.4375 27.1875C23.1094 27.1875 22.7871 27.1465 22.4824 27.0645C22.3066 28.7168 20.9121 30 19.2188 30C17.4082 30 15.9375 28.5293 15.9375 26.7188V3.28125C15.9375 1.4707 17.4082 0 19.2188 0Z"
                    fill="white"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_28">
                    <path d="M0 0H30V30H0V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <h2 className="mt-6 text-2xl font-semibold text-gray-800">
            Psychometrist Portal
          </h2>
          <p className="mt-2 text-sm text-gray-500">Sign in to your account</p>
        </header>

        {/* Form Section */}
        <form
          className="mt-8 bg-white p-8 rounded-xl shadow-sm"
          autoComplete="off"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="space-y-6">
            {/* Email Field */}
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email Address
              </label>
              <div className="mt-1">
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2">
                    <svg
                      width={16}
                      height={16}
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2 3.5C1.725 3.5 1.5 3.725 1.5 4V4.69063L6.89062 9.11563C7.5375 9.64688 8.46562 9.64688 9.1125 9.11563L14.5 4.69063V4C14.5 3.725 14.275 3.5 14 3.5H2ZM1.5 6.63125V12C1.5 12.275 1.725 12.5 2 12.5H14C14.275 12.5 14.5 12.275 14.5 12V6.63125L10.0625 10.275C8.8625 11.2594 7.13438 11.2594 5.9375 10.275L1.5 6.63125ZM0 4C0 2.89688 0.896875 2 2 2H14C15.1031 2 16 2.89688 16 4V12C16 13.1031 15.1031 14 14 14H2C0.896875 14 0 13.1031 0 12V4Z"
                        fill="#9CA3AF"
                      />
                    </svg>
                  </span>
                  <input
                    {...register("email")}
                    type="email"
                    required
                    placeholder="<EMAIL>"
                    className={`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${
                      errors.email
                        ? "border-red-500 focus:ring-red-500"
                        : "focus:ring-gray-500"
                    }`}
                  />
                </div>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.email.message}
                  </p>
                )}
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Password
              </label>
              <div className="mt-1">
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                    >
                      <g clip-path="url(#clip0_1_66)">
                        <path
                          d="M4.89062 13.9906L4.49687 14.9125C3.9125 14.6156 3.375 14.25 2.8875 13.8219L3.59687 13.1125C3.9875 13.4531 4.42188 13.75 4.89062 13.9906ZM1.26875 8.5H0.265625C0.309375 9.1625 0.434375 9.80313 0.63125 10.4094L1.5625 10.0375C1.40937 9.54688 1.30625 9.03125 1.26875 8.5ZM1.26875 7.5C1.3125 6.9125 1.43125 6.34375 1.61563 5.80937L0.69375 5.41563C0.459375 6.07188 0.3125 6.77187 0.265625 7.5H1.26875ZM2.00938 4.89062C2.25313 4.425 2.54688 3.99062 2.8875 3.59375L2.17812 2.88438C1.75 3.37188 1.38125 3.90938 1.0875 4.49375L2.00938 4.89062ZM12.4062 13.1125C11.9719 13.4875 11.4875 13.8094 10.9656 14.0625L11.3375 14.9937C11.9844 14.6844 12.5813 14.2875 13.1156 13.8188L12.4062 13.1125ZM3.59375 2.8875C4.02813 2.5125 4.5125 2.19062 5.03438 1.9375L4.6625 1.00625C4.01562 1.31562 3.41875 1.7125 2.8875 2.18125L3.59375 2.8875ZM13.9906 11.1094C13.7469 11.575 13.4531 12.0094 13.1125 12.4062L13.8219 13.1156C14.25 12.6281 14.6188 12.0875 14.9125 11.5063L13.9906 11.1094ZM14.7312 8.5C14.6875 9.0875 14.5687 9.65625 14.3844 10.1906L15.3062 10.5844C15.5406 9.925 15.6875 9.225 15.7312 8.49687H14.7312V8.5ZM10.0375 14.4375C9.54688 14.5938 9.03125 14.6937 8.5 14.7312V15.7344C9.1625 15.6906 9.80313 15.5656 10.4094 15.3687L10.0375 14.4375ZM7.5 14.7312C6.9125 14.6875 6.34375 14.5687 5.80937 14.3844L5.41563 15.3062C6.075 15.5406 6.775 15.6875 7.50313 15.7312V14.7312H7.5ZM14.4375 5.9625C14.5938 6.45312 14.6937 6.96875 14.7312 7.5H15.7344C15.6906 6.8375 15.5656 6.19687 15.3687 5.59062L14.4375 5.9625ZM2.8875 12.4062C2.5125 11.9719 2.19062 11.4875 1.9375 10.9656L1.00625 11.3375C1.31562 11.9844 1.7125 12.5813 2.18125 13.1156L2.8875 12.4062ZM8.5 1.26875C9.0875 1.3125 9.65312 1.43125 10.1906 1.61563L10.5844 0.69375C9.92813 0.459375 9.22812 0.3125 8.5 0.265625V1.26875ZM5.9625 1.5625C6.45312 1.40625 6.96875 1.30625 7.5 1.26875V0.265625C6.8375 0.309375 6.19687 0.434375 5.59062 0.63125L5.9625 1.5625ZM13.8219 2.88438L13.1125 3.59375C13.4875 4.02813 13.8094 4.5125 14.0656 5.03438L14.9969 4.6625C14.6875 4.01562 14.2906 3.41875 13.8219 2.88438ZM12.4062 2.8875L13.1156 2.17812C12.6281 1.75 12.0906 1.38125 11.5063 1.0875L11.1125 2.00938C11.575 2.25313 12.0125 2.54688 12.4062 2.8875Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M8 12.25C8.48325 12.25 8.875 11.8582 8.875 11.375C8.875 10.8918 8.48325 10.5 8 10.5C7.51675 10.5 7.125 10.8918 7.125 11.375C7.125 11.8582 7.51675 12.25 8 12.25Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M8.24069 9.75H7.74069C7.53444 9.75 7.36569 9.58125 7.36569 9.375C7.36569 7.15625 9.78444 7.37812 9.78444 6.00625C9.78444 5.38125 9.22819 4.75 7.99069 4.75C7.08132 4.75 6.60632 5.05 6.14069 5.64687C6.01882 5.80312 5.79382 5.83438 5.63444 5.72188L5.22507 5.43437C5.05007 5.3125 5.00944 5.06562 5.14382 4.89687C5.80632 4.04687 6.59382 3.5 7.99382 3.5C9.62819 3.5 11.0376 4.43125 11.0376 6.00625C11.0376 8.11875 8.61882 7.99062 8.61882 9.375C8.61569 9.58125 8.44694 9.75 8.24069 9.75Z"
                          fill="#9CA3AF"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_1_66">
                          <path d="M0 0H16V16H0V0Z" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                  <input
                    {...register("password")}
                    type="password"
                    required
                    placeholder="••••••••"
                    className={`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${
                      errors.password
                        ? "border-red-500 focus:ring-red-500"
                        : "focus:ring-gray-500"
                    }`}
                  />
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
              </div>
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label
                  htmlFor="remember"
                  className="ml-2 text-sm text-gray-600"
                >
                  Remember me
                </label>
              </div>
              <Link
                to="/user/forgot-password"
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                Forgot password?
              </Link>
            </div>

            {/* Sign In Button */}
            <button
              type="submit"
              disabled={submitLoading}
              className={`w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${
                submitLoading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              <span className="text-white">
                {submitLoading ? "Signing in..." : "Sign in"}
              </span>
              {!submitLoading && (
                <svg
                  className="ml-2 h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </button>
          </div>
          <div className="text-center mt-4">
            <Link
              to="/user/signup"
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Don't have an account? Register
            </Link>
          </div>
        </form>

        {/* Help Section */}
        <div className="text-center mt-4">
          <a href="#" className="text-sm text-gray-500 hover:text-gray-700">
            Need help?
          </a>
          <div className="mt-4 border-t border-gray-200 pt-4">
            <p className="text-sm text-gray-600">
              Contact your administrator for support
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserLoginPage;
