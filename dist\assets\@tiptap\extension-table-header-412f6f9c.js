import{f as a,m as l}from"./extension-highlight-1b3a19b9.js";const o=a.create({name:"tableHeader",addOptions(){return{HTMLAttributes:{}}},content:"block+",addAttributes(){return{colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:t=>{const e=t.getAttribute("colwidth");return e?e.split(",").map(r=>parseInt(r,10)):null}}}},tableRole:"header_cell",isolating:!0,parseHTML(){return[{tag:"th"}]},renderHTML({HTMLAttributes:t}){return["th",l(this.options.HTMLAttributes,t),0]}});export{o as T};
