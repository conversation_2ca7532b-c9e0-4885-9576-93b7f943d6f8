import{R as c,r as l,b as z}from"../vendor-489b60f1.js";import{j as B}from"./extension-highlight-1b3a19b9.js";function U(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var V={exports:{}},w={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var j;function P(){if(j)return w;j=1;var i=c;function t(s,u){return s===u&&(s!==0||1/s===1/u)||s!==s&&u!==u}var e=typeof Object.is=="function"?Object.is:t,n=i.useState,r=i.useEffect,o=i.useLayoutEffect,h=i.useDebugValue;function C(s,u){var d=u(),f=n({inst:{value:d,getSnapshot:u}}),a=f[0].inst,m=f[1];return o(function(){a.value=d,a.getSnapshot=u,y(a)&&m({inst:a})},[s,d,u]),r(function(){return y(a)&&m({inst:a}),s(function(){y(a)&&m({inst:a})})},[s]),h(d),d}function y(s){var u=s.getSnapshot;s=s.value;try{var d=u();return!e(s,d)}catch{return!0}}function p(s,u){return u()}var v=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?p:C;return w.useSyncExternalStore=i.useSyncExternalStore!==void 0?i.useSyncExternalStore:v,w}V.exports=P();var D=V.exports;const A=(...i)=>t=>{i.forEach(e=>{typeof e=="function"?e(t):e&&(e.current=t)})},F=({contentComponent:i})=>{const t=D.useSyncExternalStore(i.subscribe,i.getSnapshot,i.getServerSnapshot);return c.createElement(c.Fragment,null,Object.values(t))};function W(){const i=new Set;let t={};return{subscribe(e){return i.add(e),()=>{i.delete(e)}},getSnapshot(){return t},getServerSnapshot(){return t},setRenderer(e,n){t={...t,[e]:z.createPortal(n.reactElement,n.element,e)},i.forEach(r=>r())},removeRenderer(e){const n={...t};delete n[e],t=n,i.forEach(r=>r())}}}class L extends c.Component{constructor(t){var e;super(t),this.editorContentRef=c.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(!((e=t.editor)===null||e===void 0)&&e.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){const t=this.props.editor;if(t&&!t.isDestroyed&&t.options.element){if(t.contentComponent)return;const e=this.editorContentRef.current;e.append(...t.options.element.childNodes),t.setOptions({element:e}),t.contentComponent=W(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=t.contentComponent.subscribe(()=>{this.setState(n=>n.hasContentComponentInitialized?n:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),t.createNodeViews(),this.initialized=!0}}componentWillUnmount(){const t=this.props.editor;if(!t||(this.initialized=!1,t.isDestroyed||t.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),t.contentComponent=null,!t.options.element.firstChild))return;const e=document.createElement("div");e.append(...t.options.element.childNodes),t.setOptions({element:e})}render(){const{editor:t,innerRef:e,...n}=this.props;return c.createElement(c.Fragment,null,c.createElement("div",{ref:A(e,this.editorContentRef),...n}),(t==null?void 0:t.contentComponent)&&c.createElement(F,{contentComponent:t.contentComponent}))}}const $=l.forwardRef((i,t)=>{const e=c.useMemo(()=>Math.floor(Math.random()*4294967295).toString(),[i.editor]);return c.createElement(L,{key:e,innerRef:t,...i})}),it=c.memo($);var K=function i(t,e){if(t===e)return!0;if(t&&e&&typeof t=="object"&&typeof e=="object"){if(t.constructor!==e.constructor)return!1;var n,r,o;if(Array.isArray(t)){if(n=t.length,n!=e.length)return!1;for(r=n;r--!==0;)if(!i(t[r],e[r]))return!1;return!0}if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(r of t.entries())if(!e.has(r[0]))return!1;for(r of t.entries())if(!i(r[1],e.get(r[0])))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(r of t.entries())if(!e.has(r[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(e)){if(n=t.length,n!=e.length)return!1;for(r=n;r--!==0;)if(t[r]!==e[r])return!1;return!0}if(t.constructor===RegExp)return t.source===e.source&&t.flags===e.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===e.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===e.toString();if(o=Object.keys(t),n=o.length,n!==Object.keys(e).length)return!1;for(r=n;r--!==0;)if(!Object.prototype.hasOwnProperty.call(e,o[r]))return!1;for(r=n;r--!==0;){var h=o[r];if(!(h==="_owner"&&t.$$typeof)&&!i(t[h],e[h]))return!1}return!0}return t!==t&&e!==e},G=U(K),q={exports:{}},_={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var N;function H(){if(N)return _;N=1;var i=c,t=D;function e(p,v){return p===v&&(p!==0||1/p===1/v)||p!==p&&v!==v}var n=typeof Object.is=="function"?Object.is:e,r=t.useSyncExternalStore,o=i.useRef,h=i.useEffect,C=i.useMemo,y=i.useDebugValue;return _.useSyncExternalStoreWithSelector=function(p,v,s,u,d){var f=o(null);if(f.current===null){var a={hasValue:!1,value:null};f.current=a}else a=f.current;f=C(function(){function O(S){if(!I){if(I=!0,g=S,S=u(S),d!==void 0&&a.hasValue){var E=a.value;if(d(E,S))return b=E}return b=S}if(E=b,n(g,S))return E;var M=u(S);return d!==void 0&&d(E,M)?E:(g=S,b=M)}var I=!1,g,b,T=s===void 0?null:s;return[function(){return O(v())},T===null?void 0:function(){return O(T())}]},[v,s,u,d]);var m=r(p,f[0],f[1]);return h(function(){a.hasValue=!0,a.value=m},[m]),y(m),m},_}q.exports=H();var J=q.exports;const Q=typeof window<"u"?l.useLayoutEffect:l.useEffect;class X{constructor(t){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=t,this.lastSnapshot={editor:t,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber?this.lastSnapshot:(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber},this.lastSnapshot)}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(t){return this.subscribers.add(t),()=>{this.subscribers.delete(t)}}watch(t){if(this.editor=t,this.editor){const e=()=>{this.transactionNumber+=1,this.subscribers.forEach(r=>r())},n=this.editor;return n.on("transaction",e),()=>{n.off("transaction",e)}}}}function Y(i){var t;const[e]=l.useState(()=>new X(i.editor)),n=J.useSyncExternalStoreWithSelector(e.subscribe,e.getSnapshot,e.getServerSnapshot,i.selector,(t=i.equalityFn)!==null&&t!==void 0?t:G);return Q(()=>e.watch(i.editor),[i.editor,e]),l.useDebugValue(n),n}const Z=!1,x=typeof window>"u",k=x||!!(typeof window<"u"&&window.next);class R{constructor(t){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=t,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(t){this.editor=t,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){if(this.options.current.immediatelyRender===void 0)return x||k?null:this.createEditor();if(this.options.current.immediatelyRender&&x&&Z)throw new Error("Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.");return this.options.current.immediatelyRender?this.createEditor():null}createEditor(){const t={...this.options.current,onBeforeCreate:(...n)=>{var r,o;return(o=(r=this.options.current).onBeforeCreate)===null||o===void 0?void 0:o.call(r,...n)},onBlur:(...n)=>{var r,o;return(o=(r=this.options.current).onBlur)===null||o===void 0?void 0:o.call(r,...n)},onCreate:(...n)=>{var r,o;return(o=(r=this.options.current).onCreate)===null||o===void 0?void 0:o.call(r,...n)},onDestroy:(...n)=>{var r,o;return(o=(r=this.options.current).onDestroy)===null||o===void 0?void 0:o.call(r,...n)},onFocus:(...n)=>{var r,o;return(o=(r=this.options.current).onFocus)===null||o===void 0?void 0:o.call(r,...n)},onSelectionUpdate:(...n)=>{var r,o;return(o=(r=this.options.current).onSelectionUpdate)===null||o===void 0?void 0:o.call(r,...n)},onTransaction:(...n)=>{var r,o;return(o=(r=this.options.current).onTransaction)===null||o===void 0?void 0:o.call(r,...n)},onUpdate:(...n)=>{var r,o;return(o=(r=this.options.current).onUpdate)===null||o===void 0?void 0:o.call(r,...n)},onContentError:(...n)=>{var r,o;return(o=(r=this.options.current).onContentError)===null||o===void 0?void 0:o.call(r,...n)},onDrop:(...n)=>{var r,o;return(o=(r=this.options.current).onDrop)===null||o===void 0?void 0:o.call(r,...n)},onPaste:(...n)=>{var r,o;return(o=(r=this.options.current).onPaste)===null||o===void 0?void 0:o.call(r,...n)}};return new B(t)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(t){return this.subscriptions.add(t),()=>{this.subscriptions.delete(t)}}static compareOptions(t,e){return Object.keys(t).every(n=>["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)?!0:n==="extensions"&&t.extensions&&e.extensions?t.extensions.length!==e.extensions.length?!1:t.extensions.every((r,o)=>{var h;return r===((h=e.extensions)===null||h===void 0?void 0:h[o])}):t[n]===e[n])}onRender(t){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&t.length===0?R.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(t),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(t){if(this.editor&&!this.editor.isDestroyed){if(this.previousDeps===null){this.previousDeps=t;return}if(this.previousDeps.length===t.length&&this.previousDeps.every((n,r)=>n===t[r]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=t}scheduleDestroy(){const t=this.instanceId,e=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===t){e&&e.setOptions(this.options.current);return}e&&!e.isDestroyed&&(e.destroy(),this.instanceId===t&&this.setEditor(null))},1)}}function st(i={},t=[]){const e=l.useRef(i);e.current=i;const[n]=l.useState(()=>new R(e)),r=D.useSyncExternalStore(n.subscribe,n.getEditor,n.getServerSnapshot);return l.useDebugValue(r),l.useEffect(n.onRender(t)),Y({editor:r,selector:({transactionNumber:o})=>i.shouldRerenderOnTransaction===!1?null:i.immediatelyRender&&o===0?0:o+1}),r}const tt=l.createContext({editor:null});tt.Consumer;const et=l.createContext({onDragStart:void 0}),rt=()=>l.useContext(et);c.forwardRef((i,t)=>{const{onDragStart:e}=rt(),n=i.as||"div";return c.createElement(n,{...i,ref:t,"data-node-view-wrapper":"",onDragStart:e,style:{whiteSpace:"normal",...i.style}})});export{it as E,st as u};
