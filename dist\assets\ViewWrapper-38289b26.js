import{j as r}from"./@react-google-maps/api-5b2d83cc.js";import{R as u}from"./vendor-489b60f1.js";import{L as n,f as d}from"./index-95f0e460.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const E=({children:i,view:o,views:t,setView:l,viewsMap:a,className:p,tabClassName:s,tabContainerClassName:f})=>{const e=u.Children.toArray(i);return r.jsxs("div",{className:`grid h-full max-h-full min-h-full w-full min-w-full max-w-full grid-rows-[auto_1fr] ${p}`,children:[r.jsx("div",{className:`mb-5 w-full min-w-full max-w-full overflow-auto ${f}`,children:r.jsx(n,{children:r.jsx(d,{tabs:t,view:o,setView:l,viewsMap:a,tabClassName:s})})}),e.map(m=>m.props.view===o?m:null)]})};export{E as default};
