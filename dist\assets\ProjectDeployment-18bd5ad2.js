import{j as s}from"./@react-google-maps/api-5b2d83cc.js";import{r as u,k as p}from"./vendor-489b60f1.js";import{a as x,L as m,V as f,F as h,B as v}from"./index-95f0e460.js";import{u as y}from"./@tanstack/react-query-dc4b6186.js";import{q as w,M as D}from"./models-4d813338.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const j=(e,t,o)=>{var n,i;const{tdk:r}=x(),a=async(c,d)=>{const l=await r.getOne(c,d,o);return l.data??(l==null?void 0:l.model)};return y({queryKey:[(i=(n=w)==null?void 0:n[e])==null?void 0:i.byId,e,t],enabled:!!t&&!!e,queryFn:()=>a(e,t)})},N=(e,t)=>{const{data:o,isLoading:r}=j(e,t);return{data:o,isLoading:r}},S=()=>{const[e]=u.useState({FRONTEND_DEPLOY:{value:"frontend",hasCount:!1,count:0},BACKEND_DEPLOY:{value:"backend",hasCount:!1,count:0}}),[t,o]=u.useState(e.FRONTEND_DEPLOY.value),{id:r}=p(),{data:a}=N(D.PROJECT,r);return s.jsx("div",{className:"h-full max-h-full min-h-full overflow-y-auto bg-[#f9f9f9]",children:s.jsxs("div",{className:"",children:[s.jsx("div",{className:"relative bg-white px-8 pb-6 pt-3",children:s.jsxs("p",{className:"mb-1 text-xl font-semibold",children:["Deployment ",a==null?void 0:a.slug]})}),s.jsx("div",{children:s.jsx(m,{children:s.jsxs(f,{view:t,views:[...Object.keys(e)],viewsMap:e,setView:n=>{var i;o((i=e[n])==null?void 0:i.value)},tabContainerClassName:" px-8 ",className:"!grid-rows-[auto_auto] !min-h-fit !max-h-fit !h-fit",children:[s.jsx(m,{view:e.FRONTEND_DEPLOY.value,children:s.jsx(h,{view:t})}),s.jsx(m,{view:e.BACKEND_DEPLOY.value,children:s.jsx(v,{view:t,project:a})})]})})})]})})};export{S as default};
