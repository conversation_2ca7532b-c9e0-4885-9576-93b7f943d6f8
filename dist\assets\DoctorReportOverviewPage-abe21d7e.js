import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as d,d as k}from"./vendor-489b60f1.js";import{u as P,E as T}from"./@tiptap/react-8d1cdd44.js";import{S as B}from"./@tiptap/starter-kit-8dc7719e.js";import{T as R}from"./@tiptap/extension-text-align-4d325d88.js";import{U as $}from"./@tiptap/extension-underline-5fc56973.js";import{T as z}from"./@tiptap/extension-table-a2bc9598.js";import{T as O}from"./@tiptap/extension-table-row-159ab625.js";import{T as I}from"./@tiptap/extension-table-cell-b57d510e.js";import{T as F}from"./@tiptap/extension-table-header-412f6f9c.js";import{b as A}from"./index-95f0e460.js";import{D as H}from"./deanna_logo-c6d631d7.js";import"./@tiptap/extension-highlight-1b3a19b9.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const M=({editor:t})=>t?e.jsx("div",{className:"w-full",children:e.jsxs("div",{className:"flex items-center gap-2 border-b px-2 py-2 bg-white rounded-t-xl",children:[e.jsx("button",{type:"button","aria-label":"Bold","aria-pressed":t.isActive("bold"),className:`px-2 py-1 rounded ${t.isActive("bold")?"font-bold text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBold().run(),disabled:!0,children:e.jsx("b",{children:"B"})}),e.jsx("button",{type:"button","aria-label":"Italic","aria-pressed":t.isActive("italic"),className:`px-2 py-1 rounded ${t.isActive("italic")?"italic text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleItalic().run(),disabled:!0,children:e.jsx("i",{children:"I"})}),e.jsx("button",{type:"button","aria-label":"Underline","aria-pressed":t.isActive("underline"),className:`px-2 py-1 rounded ${t.isActive("underline")?"underline text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>{var n,l;return(l=(n=t.chain().focus()).toggleUnderline)==null?void 0:l.call(n).run()},disabled:!0,children:e.jsx("u",{children:"U"})}),e.jsx("button",{type:"button","aria-label":"Bulleted List","aria-pressed":t.isActive("bulletList"),className:`px-2 py-1 rounded ${t.isActive("bulletList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBulletList().run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("circle",{cx:"4",cy:"5",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"9",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"13",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"12",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Numbered List","aria-pressed":t.isActive("orderedList"),className:`px-2 py-1 rounded ${t.isActive("orderedList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleOrderedList().run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("text",{x:"2",y:"7",fontSize:"6",fill:"currentColor",children:"1."}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("text",{x:"2",y:"13",fontSize:"6",fill:"currentColor",children:"2."}),e.jsx("rect",{x:"7",y:"10",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("span",{className:"mx-2 border-l h-6"}),e.jsx("button",{type:"button","aria-label":"Align Left","aria-pressed":t.isActive({textAlign:"left"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"left"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("left").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Center","aria-pressed":t.isActive({textAlign:"center"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"center"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("center").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"5",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Right","aria-pressed":t.isActive({textAlign:"right"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"right"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("right").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Justify","aria-pressed":t.isActive({textAlign:"justify"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"justify"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("justify").run(),disabled:!0,children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})})]})}):null;function V(t){if(typeof window>"u"||!t)return{};const l=new window.DOMParser().parseFromString(t,"text/html"),r=o=>{var g;const x=Array.from(l.querySelectorAll("tr")).find(u=>{var y;const a=u.querySelectorAll("td");return a.length>=2?(y=a[0].textContent)==null?void 0:y.replace(/\s/g,"").toLowerCase().includes(o.replace(/\s/g,"").toLowerCase()):!1});if(!x)return"";const p=x.querySelectorAll("td");return p.length>=2&&((g=p[1].textContent)==null?void 0:g.trim())||""};return{name:r("Name"),dob:r("D.O.B"),parents:r("Parents"),address:r("Address"),phone:r("Phone"),school:r("School"),schoolBoard:r("School Board"),gradeProgram:r("Grade/Program"),assessmentBy:r("Assessment by"),assessmentDates:r("Assessment Dates"),ageAtTesting:r("Age at testing")}}async function U(t){try{const l=await(await fetch(t)).blob();return new Promise((r,o)=>{const s=new FileReader;s.onloadend=()=>r(s.result),s.onerror=o,s.readAsDataURL(l)})}catch(n){return console.error("Error converting image to base64:",n),""}}function D(t){return t.replace(/^\d+\.\s*/,"").trim()}async function W({sections:t}){const n=t[0],l=t[1],r=t.slice(2);return`
  <div style="font-family: 'Times New Roman', Times, serif; font-size: 14px;">
          <style>
 
        h2 { font-size: 16px !important; font-weight: bold; margin: 10px 0 4px 0; text-decoration: underline; font-style: italic; }
        p { font-size: 14px !important; margin: 4px 0; }
        ul, ol { font-size: 14px !important; }
        li { font-size: 14px !important; }
        table { font-size: 14px !important; }
        td, th { font-size: 14px !important; }
        .psychologists-list { font-size: 10px !important; }
        .psychologists-list li { font-size: 10px !important; }
      </style>
    <div style="display: flex; border-bottom: 1px solid #ccc; padding: 20px 0px;">
      <div style="width: 120px;">
        <div style="width:100px; height:60px; display:flex; align-items:center; justify-content:center;">
          <img src="${await U(H)}" alt="Logo" style="max-width: 100%; max-height: 100%; object-fit: contain;" />
        </div>
      </div>
      <div style="flex:1; text-align:right; font-size: 14px;">
        <h1 style="color:#1ca3a3; margin:0; font-size:2em;">Gilmour Psychological Services®</h1>
        <div>421 Gilmour Street, Ottawa, ON  K2P 0R5</div>
        <div>Tel: ************ &nbsp; Fax: ************</div>
        <div><a href="http://www.ottawa-psychologists.com">www.ottawa-psychologists.com</a></div>
      </div>
    </div>
    <div style="display: flex;">
      <div style="width:180px; border-right:1px solid #ccc; padding:10px;">
         <p style="margin:0; padding:0;"><b>Psychologists</b></p>
          <ul class="psychologists-list" style="margin:0; padding:0; list-style:none;">
            <li style="margin:10px 0;">Dr. Iris Jackson &nbsp; Ext. 124</li>
            <li style="margin:10px 0;">Dr. Karen Davies &nbsp; Ext. 126</li>
            <li style="margin:10px 0;">Dr. Alex Weinberger &nbsp; Ext. 136</li>
            <li style="margin:10px 0;">Dr. Qadeer Ahmad &nbsp; Ext. 129</li>
            <li style="margin:10px 0;">Dr. Peter Judge &nbsp; Ext. 132</li>
            <li style="margin:10px 0;">Dr. Deanna Drahovzal &nbsp; Ext. 146</li>
            <li style="margin:10px 0;">Dr. Sarah Pantin &nbsp; Ext. 150</li>
            <li style="margin:10px 0;">Dr. Marc Zahradnik &nbsp; Ext. 142</li>
            <li style="margin:10px 0;">Dr. Caroline Ostiguy &nbsp; Ext. 140</li>
            <li style="margin:10px 0;">Dr. Jessica Henry &nbsp; Ext. 155</li>
            <li style="margin:10px 0;">Dr. Delyana Miller &nbsp; Ext. 143</li>
            <li style="margin:10px 0;">Dr. Angelina Chupetlovska &nbsp; Ext. 152</li>
            <li style="margin:10px 0;">Dr. Douglas Scoular &nbsp; Ext. 148</li>
            <li style="margin:10px 0;">Dr. Elisabeth Melsom &nbsp; Ext. 157</li>
            <li style="margin:10px 0;">Dr. Emma Dargie &nbsp; Ext. 175</li>
            <li style="margin:10px 0;">Dr. Karima Lacène &nbsp; Ext. 188</li>
            <li style="margin:10px 0;">Dr. Amanda Timmers &nbsp; Ext. 153</li>
            <li style="margin:10px 0;">Dr. Rana Pishva &nbsp; Ext. 160</li>
            <li style="margin:10px 0;">Dr. Emily Segal &nbsp; Ext. 164</li>
            <li style="margin:10px 0;">Dr. Emma Murray &nbsp; Ext. 144</li>
            <li style="margin:10px 0;">Dr. Stephenie Davies &nbsp; Ext. 192</li>
          </ul>
      </div>
      <div style="flex:1; padding:10px;">
        <div style="text-align:center; font-weight:bold; margin-top:0;">PSYCHOLOGICAL REPORT</div>
        <div style="text-align:center; font-weight:bold;">(Private and Confidential)</div>
         <div style="margin-bottom:10px;"> ${n.body}</div>
         <div style="margin-bottom:10px;">${l.body}</div>
      </div>
    </div>
        ${r.length>0?r.map(s=>`<div style="page-break-before: always; padding:0px 30px;">
                  <div style="margin-bottom:0px;"><h1 style="font-size: 18px !important; font-weight: bold; margin: 10px 0 14px 0px !important; text-decoration: underline;"> <br/> ${D(s.title)}</h1> ${s.body}</div>
                </div>`).join(""):""}
    <div style="padding: 30px;">
      <div style="margin-bottom:20px;">
        <div style="border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; width: fit-content;">
          Deanna Drahovzal, Ph.D., C.Psych.
        </div>
        <div style="margin-bottom: 10px;">
            <strong>Printed:</strong> ${new Date().toLocaleDateString()}
        </div>
        <div>
          <strong>Copies to:</strong><br/>
          Clinical chart<br/>
          Dr. BW Wilson and Ms. AW Wilson via OWL secure messenger
        </div>
      </div>
    </div>
  </div>
  `}const be=()=>{const[t,n]=d.useState([]),[l,r]=d.useState(!0),[o,s]=d.useState(null),[x,p]=d.useState(!1),[g,u]=d.useState(!1);k();const a=P({extensions:[B.configure({heading:{levels:[1,2,3]},bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}}),$,R.configure({types:["heading","paragraph"]}),z.configure({resizable:!0}),O,I,F],content:"",editable:!1,editorProps:{attributes:{class:"prose prose-sm max-w-none min-h-[600px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap bg-white rounded-b-xl",spellCheck:"true","aria-label":"Doctor Report Overview Document"}},parseOptions:{preserveWhitespace:"full"}});d.useEffect(()=>{const i=localStorage.getItem("reportSections");if(i)try{const c=JSON.parse(i);n(c);const h=c.map(f=>`<h1 style="font-size: 18px !important; font-weight: bold; margin: 10px 0 14px 0px !important; text-decoration: underline;"> <br/> ${D(f.title)}</h1> ${f.body}`).join("");a==null||a.commands.setContent(h)}catch(c){s("Failed to load sections from storage"),console.error("Error loading sections:",c)}else s("No sections found in storage");r(!1)},[a]);const y=async()=>{try{p(!0);const i=new A;if(!localStorage.getItem("reportId"))throw new Error("No report ID found");for(const h of t)await i.updateReportSection({id:h.id.toString(),content:h.body})}catch(i){s(i instanceof Error?i.message:"Error saving changes.")}finally{p(!1)}},j=async()=>{if(!(!t||t.length===0)){u(!0);try{const i=V(t[0].body),c={name:i.name||"",dob:i.dob||"",address:i.address||"",phone:i.phone||"",assessmentBy:i.assessmentBy||"",assessmentDates:i.assessmentDates||"",ageAtTesting:i.ageAtTesting||""},f=await W({clientDetails:c,sections:t}),m=await new A().renderPdf({html:f,include_index:!1});if(m&&m.pdf){const E=m==null?void 0:m.pdf,v=atob(E),C=new Array(v.length);for(let b=0;b<v.length;b++)C[b]=v.charCodeAt(b);const S=new Uint8Array(C),L=new Blob([S],{type:"application/pdf"}),N=URL.createObjectURL(L),w=document.createElement("a");w.href=N,w.download=`${(localStorage.getItem("patientName")||"unknown")+"_report.pdf"}`,w.click()}else alert("Failed to generate PDF")}catch(i){console.error("Error exporting PDF:",i),alert("Error exporting PDF")}finally{u(!1)}}};return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"flex max-w-[95%] mx-auto flex-col flex-shrink-0 justify-center items-start gap-6 border-0 border-gray-200 bg-black/0",children:[e.jsxs("div",{className:"flex flex-shrink-0 items-center p-4 w-full border h-16 rounded-lg border-0 border-gray-200 bg-white justify-between",children:[e.jsx("h1",{className:"text-2xl text-gray-800 font-['Inter']",children:"Complete Report Overview"}),e.jsxs("div",{className:"flex flex-shrink-0 justify-center items-center gap-2",children:[e.jsx("button",{className:"flex items-center gap-2 px-4 py-2 rounded-md bg-gray-800 text-white disabled:opacity-50",onClick:y,disabled:x,children:x?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:"14",height:"16",viewBox:"0 0 14 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M1.8125 3V13C1.8125 13.275 2.0375 13.5 2.3125 13.5H12.3125C12.5875 13.5 12.8125 13.275 12.8125 13V5.32812C12.8125 5.19688 12.7594 5.06875 12.6656 4.975L13.725 3.91563C14.1 4.29063 14.3094 4.8 14.3094 5.33125V13C14.3094 14.1031 13.4125 15 12.3094 15H2.3125C1.20938 15 0.3125 14.1031 0.3125 13V3C0.3125 1.89688 1.20938 1 2.3125 1H9.98438C10.5156 1 11.025 1.20938 11.4 1.58438L13.7281 3.9125L12.6687 4.97188L10.3375 2.64687C10.3281 2.6375 10.3219 2.63125 10.3125 2.62188V5.75C10.3125 6.16563 9.97812 6.5 9.5625 6.5H3.5625C3.14687 6.5 2.8125 6.16563 2.8125 5.75V2.5H2.3125C2.0375 2.5 1.8125 2.725 1.8125 3Z",fill:"white"})}),"Save Changes"]})}),e.jsx("button",{className:"flex items-center gap-2 px-4 py-2 rounded-md bg-violet-600 text-white disabled:opacity-50",onClick:j,disabled:g,children:g?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Generating PDF..."]}):e.jsxs(e.Fragment,{children:[e.jsx("svg",{width:"18",height:"16",viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M0.96875 2C0.96875 0.896875 1.86563 0 2.96875 0H7.96875V4C7.96875 4.55312 8.41563 5 8.96875 5H12.9688V9H7.71875C7.30312 9 6.96875 9.33438 6.96875 9.75C6.96875 10.1656 7.30312 10.5 7.71875 10.5H12.9688V14C12.9688 15.1031 12.0719 16 10.9688 16H2.96875C1.86563 16 0.96875 15.1031 0.96875 14V2Z",fill:"white"})}),"Export",e.jsx("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.38201 11.1181C6.7238 11.4599 7.27888 11.4599 7.62068 11.1181L12.8707 5.86807C13.2125 5.52627 13.2125 4.97119 12.8707 4.62939C12.5289 4.2876 11.9738 4.2876 11.632 4.62939L6.99998 9.26143L2.36794 4.63213C2.02615 4.29033 1.47107 4.29033 1.12927 4.63213C0.787476 4.97393 0.787476 5.529 1.12927 5.8708L6.37927 11.1208L6.38201 11.1181Z",fill:"white"})})]})})]})]}),e.jsx("div",{className:"w-full h-full p-4",children:e.jsx("div",{className:" mx-auto bg-white rounded-xl shadow",children:l?e.jsx("div",{className:"flex items-center justify-center min-h-[600px]",children:"Loading..."}):o?e.jsx("div",{className:"flex items-center justify-center min-h-[600px] text-red-600",children:o}):e.jsxs(e.Fragment,{children:[e.jsx(M,{editor:a}),e.jsx(T,{editor:a})]})})})]})})};export{be as default};
