import { useState, useEffect } from "react";
import ReactQuill from "react-quill";
import { EditorToolbars } from ".";
import { formats, modules } from "./EditorToolbars";
import { marked } from "marked";

interface EditorProps {
  setValue: (name: string, content: string) => void;
  errors: any;
  name: string;
  placeholder?: string;
  initialContent?: string;
  isMarkdown?: boolean;
}

const Editor = ({
  setValue,
  errors,
  name,
  placeholder = "Write something awesome...",
  initialContent = "",
  isMarkdown = false,
}: EditorProps) => {
  const [content, setContent] = useState(initialContent);
  const editorStyle = {
    // maxheight: '500px',
    // minheight: '500px',
    height: "500px",
    // overFlow: 'auto',

    // set the height to 500 pixels
  };

  // Convert markdown to HTML when initialContent changes
  useEffect(() => {
    if (isMarkdown && initialContent) {
      const htmlContent = marked(initialContent);
      setContent(htmlContent as string);
      setValue(name, htmlContent as string);
    }
  }, [initialContent, isMarkdown, name, setValue]);

  const onSetContent = (content: string) => {
    setContent(content);
    setValue(name, content);
  };

  return (
    <>
      <EditorToolbars />
      <ReactQuill
        theme="snow"
        value={content}
        onChange={(content) => onSetContent(content)}
        placeholder={placeholder}
        modules={modules}
        formats={formats}
        style={editorStyle}
      />
      {errors && errors?.content && (
        <p className="text-field-error italic text-red-500">
          {errors?.content?.message}
        </p>
      )}
    </>
  );
};

export default Editor;
