import React, { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";

// Types
interface UploadedFile {
  id: string;
  file: File;
  status: "pending" | "success" | "error";
  error?: string;
}

interface UploadFormData {
  patientId: string;
  patientName: string;
  dob: string;
}

interface FormErrors {
  patientId?: string;
  patientName?: string;
  dob?: string;
}

interface DocumentUploadResponse {
  error: boolean;
  documents?: Array<{
    id: string;
    url: string;
  }>;
  message?: string;
}

const SUPPORTED_FILE_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "text/plain",
];

// Validation functions
const validatePatientId = (value: string): string | undefined => {
  if (!value.trim()) {
    return "Patient ID is required";
  }
  if (value.trim().length < 3) {
    return "Patient ID must be at least 3 characters long";
  }
  if (!/^[a-zA-Z0-9-_]+$/.test(value.trim())) {
    return "Patient ID can only contain letters, numbers, hyphens, and underscores";
  }
  return undefined;
};

const validatePatientName = (value: string): string | undefined => {
  if (!value.trim()) {
    return "Patient name is required";
  }
  if (value.trim().length < 2) {
    return "Patient name must be at least 2 characters long";
  }
  if (!/^[a-zA-Z\s'-]+$/.test(value.trim())) {
    return "Patient name can only contain letters, spaces, hyphens, and apostrophes";
  }
  return undefined;
};

const validateDateOfBirth = (value: string): string | undefined => {
  if (!value) {
    return "Date of birth is required";
  }

  const selectedDate = new Date(value);
  const today = new Date();
  const minDate = new Date("1900-01-01");
  const maxDate = new Date(
    today.getFullYear() - 1,
    today.getMonth(),
    today.getDate()
  ); // At least 1 year old

  if (selectedDate < minDate) {
    return "Date of birth cannot be before 1900";
  }
  if (selectedDate > maxDate) {
    return "Patient must be at least 1 year old";
  }
  if (selectedDate > today) {
    return "Date of birth cannot be in the future";
  }

  return undefined;
};

const DocumentUpload: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { sdk } = useSDK();
  const projectId = searchParams.get("projectId");

  // State
  const [formData, setFormData] = useState<UploadFormData>({
    patientId: "",
    patientName: "",
    dob: "",
  });
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Validation helper
  const validateField = (
    name: keyof UploadFormData,
    value: string
  ): string | undefined => {
    switch (name) {
      case "patientId":
        return validatePatientId(value);
      case "patientName":
        return validatePatientName(value);
      case "dob":
        return validateDateOfBirth(value);
      default:
        return undefined;
    }
  };

  // Handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (formErrors[name as keyof UploadFormData]) {
      setFormErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTouchedFields((prev) => new Set(prev).add(name));

    // Validate field on blur
    const error = validateField(name as keyof UploadFormData, value);
    setFormErrors((prev) => ({ ...prev, [name]: error }));
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // Validate all fields
    Object.keys(formData).forEach((key) => {
      const fieldName = key as keyof UploadFormData;
      const error = validateField(fieldName, formData[fieldName]);
      if (error) {
        errors[fieldName] = error;
      }
    });

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    await handleFiles(files);
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      await handleFiles(files);
    }
  };

  const handleFiles = async (files: File[]) => {
    try {
      setError(null);

      const newFiles: UploadedFile[] = files.map((file) => {
        if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
          throw new Error(`Unsupported file type: ${file.type}`);
        }

        return {
          id: Math.random().toString(36).substr(2, 9),
          file,
          status: "pending",
        };
      });

      setUploadedFiles((prev) => [...prev, ...newFiles]);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to process files");
    }
  };

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
  };

  const handleAnalyze = async () => {
    // Validate form before proceeding
    if (!validateForm()) {
      setError("Please fix the validation errors before proceeding");
      return;
    }

    if (uploadedFiles.length === 0) {
      setError("Please upload at least one file");
      return;
    }

    try {
      setUploading(true);
      setError(null);
      setUploadProgress(0);

      // Track upload results during the loop
      let uploadResults: Array<{ success: boolean; error?: string }> = [];

      // Upload files one by one to show individual progress
      for (let i = 0; i < uploadedFiles.length; i++) {
        const currentFile = uploadedFiles[i];
        
        // Update current file status to uploading
        setUploadedFiles(prev => 
          prev.map((file, index) => 
            index === i 
              ? { ...file, status: "pending" as const }
              : file
          )
        );

        try {
          // Upload single file while maintaining the same backend format
          const result = await sdk?.uploadDocument(
            [currentFile.file], // Send single file in array format
            formData.patientId,
            formData.patientName,
            formData.dob
          );

          // Update file status to success
          setUploadedFiles(prev => 
            prev.map((file, index) => 
              index === i 
                ? { ...file, status: "success" as const }
                : file
            )
          );

          // Track successful upload
          uploadResults.push({ success: true });

          // Update overall progress
          const progress = ((i + 1) / uploadedFiles.length) * 100;
          setUploadProgress(progress);

        } catch (fileError) {
          // Update file status to error
          setUploadedFiles(prev => 
            prev.map((file, index) => 
              index === i 
                ? { ...file, status: "error" as const, error: fileError instanceof Error ? fileError.message : "Upload failed" }
                : file
            )
          );
          
          // Track failed upload
          uploadResults.push({ 
            success: false, 
            error: fileError instanceof Error ? fileError.message : "Upload failed" 
          });
          
          // Continue with other files even if one fails
          console.error(`Failed to upload ${currentFile.file.name}:`, fileError);
          
          // Update progress even for failed files
          const progress = ((i + 1) / uploadedFiles.length) * 100;
          setUploadProgress(progress);
        }
      }

      // Store patient info in localStorage after all uploads
      localStorage.setItem("patientId", formData.patientId);
      localStorage.setItem("patientName", formData.patientName);

      // Create project after all files are processed
      const projectRes = await sdk.createProject({
        name: "case report " + formData.patientName || "",
        diagnosis: "",
        user_id: localStorage.getItem("user") || "",
        status: 0,
        patient_id: formData.patientId,
        patient_name: formData.patientName || "",
      });

      if (!projectRes || projectRes.error) {
        throw new Error(projectRes?.message || "Failed to create project");
      }

      localStorage.setItem("projectId", projectRes.project.id);

      // Check if all files were uploaded successfully using the tracked results
      const allFilesSuccessful = uploadResults.every(result => result.success);
      
      console.log("Upload results:", uploadResults);
      console.log("All files successful:", allFilesSuccessful);

      if (allFilesSuccessful) {
        // Navigate to analysis page after successful upload
        navigate("/member/analysis");
      } else {
        const failedCount = uploadResults.filter(result => !result.success).length;
        setError(`${failedCount} file(s) failed to upload. Please check the file status and try again.`);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to upload files");
    } finally {
      setUploading(false);
    }
  };

  // Helper function to get input styling based on validation state
  const getInputClassName = (fieldName: keyof UploadFormData): string => {
    const baseClasses =
      "w-full rounded-md border bg-white text-gray-900 px-3 py-2 focus:outline-none focus:ring-1";
    const hasError = formErrors[fieldName] && touchedFields.has(fieldName);

    if (hasError) {
      return `${baseClasses} border-red-300 focus:ring-red-500 focus:border-red-500`;
    }

    return `${baseClasses} border-gray-300 focus:ring-gray-500 focus:border-gray-500`;
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-gray-50 py-8 px-2">
      <div className="w-full max-w-3xl bg-white rounded-xl shadow p-8 flex flex-col gap-y-6">
        {/* Upload Form */}
        <form
          className="flex flex-col gap-y-4"
          autoComplete="off"
          aria-label="Upload Patient Documents"
          onSubmit={(e) => e.preventDefault()}
        >
          <h1 className="text-2xl font-semibold text-gray-800 mb-2">
            Upload Patient Documents
          </h1>
          <div className="flex flex-col gap-y-4">
            {/* Patient ID */}
            <div>
              <label
                htmlFor="patientId"
                className="block text-sm text-gray-700 mb-1"
              >
                Patient ID <span className="text-red-500">*</span>
              </label>
              <input
                id="patientId"
                name="patientId"
                type="text"
                required
                value={formData.patientId}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                placeholder="Enter patient ID (e.g., PAT-001)"
                className={getInputClassName("patientId")}
                aria-label="Patient ID"
                aria-invalid={
                  !!(formErrors.patientId && touchedFields.has("patientId"))
                }
                aria-describedby={
                  formErrors.patientId && touchedFields.has("patientId")
                    ? "patientId-error"
                    : undefined
                }
              />
              {formErrors.patientId && touchedFields.has("patientId") && (
                <p id="patientId-error" className="mt-1 text-sm text-red-600">
                  {formErrors.patientId}
                </p>
              )}
            </div>
            {/* Patient Name */}
            <div>
              <label
                htmlFor="patientName"
                className="block text-sm text-gray-700 mb-1"
              >
                Patient Name <span className="text-red-500">*</span>
              </label>
              <input
                id="patientName"
                name="patientName"
                type="text"
                required
                value={formData.patientName}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                placeholder="Enter patient name"
                className={getInputClassName("patientName")}
                aria-label="Patient Name"
                aria-invalid={
                  !!(formErrors.patientName && touchedFields.has("patientName"))
                }
                aria-describedby={
                  formErrors.patientName && touchedFields.has("patientName")
                    ? "patientName-error"
                    : undefined
                }
              />
              {formErrors.patientName && touchedFields.has("patientName") && (
                <p id="patientName-error" className="mt-1 text-sm text-red-600">
                  {formErrors.patientName}
                </p>
              )}
            </div>
            {/* Date of Birth */}
            <div>
              <label htmlFor="dob" className="block text-sm text-gray-700 mb-1">
                Date of Birth <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  id="dob"
                  name="dob"
                  type="date"
                  required
                  value={formData.dob}
                  onChange={handleInputChange}
                  onBlur={handleInputBlur}
                  max={new Date().toISOString().split("T")[0]}
                  className={getInputClassName("dob")}
                  aria-label="Date of Birth"
                  aria-invalid={!!(formErrors.dob && touchedFields.has("dob"))}
                  aria-describedby={
                    formErrors.dob && touchedFields.has("dob")
                      ? "dob-error"
                      : undefined
                  }
                />
              </div>
              {formErrors.dob && touchedFields.has("dob") && (
                <p id="dob-error" className="mt-1 text-sm text-red-600">
                  {formErrors.dob}
                </p>
              )}
            </div>
          </div>
        </form>

        {/* File Upload Section */}
        <section
          className={`flex flex-col items-center justify-center gap-2 w-full rounded-lg border-2 border-dashed ${
            dragActive ? "border-gray-400 bg-gray-50" : "border-gray-300"
          } bg-white py-8 transition-colors`}
          aria-label="File upload area"
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <svg
            width={46}
            height={36}
            viewBox="0 0 46 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
          >
            <g clipPath="url(#clip0_1_146)">
              <path
                d="M10.625 33.75C5.03516 33.75 0.5 29.2148 0.5 23.625C0.5 19.2094 3.32656 15.4547 7.26406 14.0695C7.25703 13.8797 7.25 13.6898 7.25 13.5C7.25 7.28437 12.2844 2.25 18.5 2.25C22.6695 2.25 26.3047 4.51406 28.2523 7.88906C29.3211 7.17188 30.6148 6.75 32 6.75C35.7266 6.75 38.75 9.77344 38.75 13.5C38.75 14.3578 38.5883 15.1734 38.3 15.9328C42.4062 16.7625 45.5 20.3977 45.5 24.75C45.5 29.7211 41.4711 33.75 36.5 33.75H10.625ZM16.1797 18.4922C15.5188 19.1531 15.5188 20.2219 16.1797 20.8758C16.8406 21.5297 17.9094 21.5367 18.5633 20.8758L21.3055 18.1336V27.5625C21.3055 28.4977 22.0578 29.25 22.993 29.25C23.9281 29.25 24.6805 28.4977 24.6805 27.5625V18.1336L27.4227 20.8758C28.0836 21.5367 29.1523 21.5367 29.8062 20.8758C30.4602 20.2148 30.4672 19.1461 29.8062 18.4922L24.1812 12.8672C23.5203 12.2063 22.4516 12.2063 21.7977 12.8672L16.1727 18.4922H16.1797Z"
                fill="#9CA3AF"
              />
            </g>
            <defs>
              <clipPath id="clip0_1_146">
                <path d="M0.5 0H45.5V36H0.5V0Z" fill="white" />
              </clipPath>
            </defs>
          </svg>
          <div className="text-lg text-gray-700 font-medium">
            {uploading ? "Uploading..." : "Drag and drop files here"}
          </div>
          <div className="text-xs text-gray-500 mb-2">
            Supported formats: PDF, DOC, DOCX, TXT
          </div>
          <label className="px-4 py-2 bg-gray-800 text-white rounded-md cursor-pointer hover:bg-gray-700 transition-colors">
            Browse Files
            <input
              type="file"
              multiple
              accept={SUPPORTED_FILE_TYPES.join(",")}
              onChange={handleFileSelect}
              className="hidden"
            />
          </label>
        </section>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            {error}
          </div>
        )}

        {/* Upload Progress */}
        {uploading && (
          <div className="w-full">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">
                Uploading files... {Math.round(uploadProgress)}%
              </span>
              <span className="text-sm text-gray-500">
                {uploadedFiles.filter(f => f.status === "success").length} of {uploadedFiles.length} completed
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {/* Uploaded Files Table */}
        <section
          className="w-full max-h-[500px] overflow-y-auto overflow-x-hidden"
          aria-label="Uploaded files"
        >
          <table className="w-full text-left border-collapse">
            <thead>
              <tr className="bg-gray-50">
                <th className="py-3 pl-6 text-gray-500 text-xs font-bold">
                  Filename
                </th>
                <th className="py-3 pl-6 text-gray-500 text-xs font-bold">
                  Type
                </th>
                <th className="py-3 pl-6 text-gray-500 text-xs font-bold">
                  Status
                </th>
                <th className="py-3 pl-6 text-gray-500 text-xs font-bold">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {uploadedFiles.map((file, idx) => {
                return (
                  <tr
                    key={file.id}
                    className={
                      idx === uploadedFiles.length - 1
                        ? ""
                        : "border-b border-gray-200"
                    }
                  >
                    <td className="py-3 pl-6 text-gray-900 text-sm">
                      {file.file.name}
                    </td>
                    <td className="py-3 pl-6 text-gray-500 text-sm">
                      {file.file.type.split("/")[1].toUpperCase()}
                    </td>
                    <td className="py-3 pl-6">
                      <span
                        className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs ${
                          file.status === "success"
                            ? "bg-green-100 text-green-800"
                            : file.status === "error"
                              ? "bg-red-100 text-red-800"
                              : uploading && file.status === "pending"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {file.status === "success" ? (
                          <>
                            <svg
                              width={12}
                              height={12}
                              viewBox="0 0 12 12"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              aria-hidden="true"
                            >
                              <path
                                d="M10.9516 2.47034C11.2446 2.76331 11.2446 3.23909 10.9516 3.53206L4.95161 9.53206C4.65864 9.82502 4.18286 9.82502 3.88989 9.53206L0.889893 6.53206C0.596924 6.23909 0.596924 5.76331 0.889893 5.47034C1.18286 5.17737 1.65864 5.17737 1.95161 5.47034L4.42192 7.93831L9.89224 2.47034C10.1852 2.17737 10.661 2.17737 10.954 2.47034H10.9516Z"
                                fill="currentColor"
                              />
                            </svg>
                            Success
                          </>
                        ) : file.status === "error" ? (
                          <>
                            <svg
                              width={10}
                              height={12}
                              viewBox="0 0 10 12"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              aria-hidden="true"
                            >
                              <path
                                d="M8.70161 3.52974C8.99458 3.23677 8.99458 2.76099 8.70161 2.46802C8.40864 2.17505 7.93286 2.17505 7.63989 2.46802L5.17192 4.93833L2.70161 2.47036C2.40864 2.17739 1.93286 2.17739 1.63989 2.47036C1.34692 2.76333 1.34692 3.23911 1.63989 3.53208L4.1102 6.00005L1.64224 8.47036C1.34927 8.76333 1.34927 9.23911 1.64224 9.53208C1.93521 9.82505 2.41099 9.82505 2.70396 9.53208L5.17192 7.06177L7.64224 9.52974C7.93521 9.8227 8.41099 9.8227 8.70396 9.52974C8.99692 9.23677 8.99692 8.76099 8.70396 8.46802L6.23364 6.00005L8.70161 3.52974Z"
                                fill="currentColor"
                              />
                            </svg>
                            Error
                          </>
                        ) : uploading && file.status === "pending" ? (
                          <>
                            <svg
                              width={12}
                              height={12}
                              viewBox="0 0 12 12"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              aria-hidden="true"
                              className="animate-spin"
                            >
                              <path
                                d="M6 1.5C3.51472 1.5 1.5 3.51472 1.5 6C1.5 8.48528 3.51472 10.5 6 10.5C8.48528 10.5 10.5 8.48528 10.5 6C10.5 3.51472 8.48528 1.5 6 1.5ZM0 6C0 2.68629 2.68629 0 6 0C9.31371 0 12 2.68629 12 6C12 9.31371 9.31371 12 6 12C2.68629 12 0 9.31371 0 6ZM6 2.5C6.82843 2.5 7.5 3.17157 7.5 4C7.5 4.82843 6.82843 5.5 6 5.5C5.17157 5.5 4.5 4.82843 4.5 4C4.5 3.17157 5.17157 2.5 6 2.5Z"
                                fill="currentColor"
                              />
                            </svg>
                            Uploading...
                          </>
                        ) : (
                          <>
                            <svg
                              width={10}
                              height={12}
                              viewBox="0 0 10 12"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              aria-hidden="true"
                            >
                              <path
                                d="M8.70161 3.52974C8.99458 3.23677 8.99458 2.76099 8.70161 2.46802C8.40864 2.17505 7.93286 2.17505 7.63989 2.46802L5.17192 4.93833L2.70161 2.47036C2.40864 2.17739 1.93286 2.17739 1.63989 2.47036C1.34692 2.76333 1.34692 3.23911 1.63989 3.53208L4.1102 6.00005L1.64224 8.47036C1.34927 8.76333 1.34927 9.23911 1.64224 9.53208C1.93521 9.82505 2.41099 9.82505 2.70396 9.53208L5.17192 7.06177L7.64224 9.52974C7.93521 9.8227 8.41099 9.8227 8.70396 9.52974C8.99692 9.23677 8.99692 8.76099 8.70396 8.46802L6.23364 6.00005L8.70161 3.52974Z"
                                fill="currentColor"
                              />
                            </svg>
                            Pending
                          </>
                        )}
                      </span>
                      {file.error && (
                        <div className="mt-1 text-xs text-red-600">
                          {file.error}
                        </div>
                      )}
                    </td>
                    <td className="py-3 pl-6">
                      <div className="flex gap-2">
                        {/* <button
                          type="button"
                          className="p-1 hover:bg-gray-100 rounded"
                          aria-label={`Analyze ${file.file.name}`}
                        >
                          <svg
                            width={16}
                            height={16}
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            aria-hidden="true"
                          >
                            <path
                              d="M8 1.33334C4.32 1.33334 1.33333 4.32001 1.33333 8.00001C1.33333 11.68 4.32 14.6667 8 14.6667C11.68 14.6667 14.6667 11.68 14.6667 8.00001C14.6667 4.32001 11.68 1.33334 8 1.33334ZM8 13.3333C5.06 13.3333 2.66667 10.94 2.66667 8.00001C2.66667 5.06001 5.06 2.66668 8 2.66668C10.94 2.66668 13.3333 5.06001 13.3333 8.00001C13.3333 10.94 10.94 13.3333 8 13.3333ZM7.33333 4.66668H8.66667V9.33334H7.33333V4.66668ZM8 10.6667C8.36667 10.6667 8.66667 10.3667 8.66667 10C8.66667 9.63334 8.36667 9.33334 8 9.33334C7.63333 9.33334 7.33333 9.63334 7.33333 10C7.33333 10.3667 7.63333 10.6667 8 10.6667Z"
                              fill="#4B5563" />
                          </svg>
                        </button> */}
                        <button
                          type="button"
                          className="p-1 hover:bg-gray-100 rounded"
                          onClick={() => handleDelete(file.id)}
                          aria-label={`Delete ${file.file.name}`}
                        >
                          <svg
                            width={13}
                            height={14}
                            viewBox="0 0 13 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            aria-hidden="true"
                          >
                            <path
                              d="M4.00937 0.483984L3.8125 0.875H1.1875C0.703516 0.875 0.3125 1.26602 0.3125 1.75C0.3125 2.23398 0.703516 2.625 1.1875 2.625H11.6875C12.1715 2.625 12.5625 2.23398 12.5625 1.75C12.5625 1.26602 12.1715 0.875 11.6875 0.875H9.0625L8.86562 0.483984C8.71797 0.185938 8.41445 0 8.08359 0H4.79141C4.46055 0 4.15703 0.185938 4.00937 0.483984ZM11.6875 3.5H1.1875L1.76719 12.7695C1.81094 13.4613 2.38516 14 3.07695 14H9.79805C10.4898 14 11.0641 13.4613 11.1078 12.7695L11.6875 3.5Z"
                              fill="#4B5563"
                            />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          {uploadedFiles.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No files uploaded yet
            </div>
          )}
        </section>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 mt-4">
          <button
            type="button"
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
            onClick={() => navigate("/projects")}
          >
            Cancel
          </button>
          <button
            type="button"
            className="flex items-center gap-2 px-6 py-2 rounded-md bg-gray-800 text-white hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleAnalyze}
            disabled={uploading || uploadedFiles.length === 0}
          >
            {uploading ? (
              <>
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="animate-spin"
                >
                  <path
                    d="M7 1.5C3.96243 1.5 1.5 3.96243 1.5 7C1.5 10.0376 3.96243 12.5 7 12.5C10.0376 12.5 12.5 10.0376 12.5 7C12.5 3.96243 10.0376 1.5 7 1.5ZM0 7C0 3.13401 3.13401 0 7 0C10.866 0 14 3.13401 14 7C14 10.866 10.866 14 7 14C3.13401 14 0 10.866 0 7ZM7 3.5C7.82843 3.5 8.5 4.17157 8.5 5C8.5 5.82843 7.82843 6.5 7 6.5C6.17157 6.5 5.5 5.82843 5.5 5C5.5 4.17157 6.17157 3.5 7 3.5Z"
                    fill="white"
                  />
                </svg>
                Uploading... ({Math.round(uploadProgress)}%)
              </>
            ) : (
              <>
                Upload & Proceed
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="16"
                  viewBox="0 0 14 16"
                  fill="none"
                >
                  <path
                    d="M13.7063 8.70627C14.0969 8.31565 14.0969 7.68127 13.7063 7.29065L8.70625 2.29065C8.31563 1.90002 7.68125 1.90002 7.29063 2.29065C6.9 2.68127 6.9 3.31565 7.29063 3.70627L10.5875 7.00002H1C0.446875 7.00002 0 7.4469 0 8.00002C0 8.55315 0.446875 9.00002 1 9.00002H10.5844L7.29375 12.2938C6.90312 12.6844 6.90312 13.3188 7.29375 13.7094C7.68437 14.1 8.31875 14.1 8.70938 13.7094L13.7094 8.7094L13.7063 8.70627Z"
                    fill="white"
                  />
                </svg>
              </>
            )}
          </button>
        </div>
      </div>
    </main>
  );
};

export default DocumentUpload;
