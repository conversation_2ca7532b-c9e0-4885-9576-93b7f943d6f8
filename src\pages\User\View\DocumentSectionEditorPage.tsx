import React, { useState, useEffect, useCallback, useRef } from "react";
import { Editor<PERSON>onte<PERSON>, useEditor, Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useNavigate } from "react-router-dom";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Highlight from "@tiptap/extension-highlight";
import MkdSDK from "../../../utils/MkdSDK";
import { format } from "date-fns";
import { useSDK } from "@/hooks/useSDK";
import { createEditorConfig } from "@/components/Editor/TipTapConfig";

interface Section {
  id: number;
  title: string;
  body: string;
  order?: number;
}

const Toolbar: React.FC<{ editor: Editor | null }> = ({ editor }) => {
  const [showNewChanges, setShowNewChanges] = useState(false);
  const [showOriginalAI, setShowOriginalAI] = useState(false);
  if (!editor) return null;
  return (
    <div className="w-full">
      {/* Main Toolbar */}
      <div className="flex items-center gap-2 border-b px-2 py-2 bg-white">
        {/* Bold */}
        <button
          type="button"
          aria-label="Bold"
          aria-pressed={editor.isActive("bold")}
          className={`px-2 py-1 rounded ${editor.isActive("bold") ? "font-bold text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <b>B</b>
        </button>
        {/* Italic */}
        <button
          type="button"
          aria-label="Italic"
          aria-pressed={editor.isActive("italic")}
          className={`px-2 py-1 rounded ${editor.isActive("italic") ? "italic text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <i>I</i>
        </button>
        {/* Underline */}
        <button
          type="button"
          aria-label="Underline"
          aria-pressed={editor.isActive("underline")}
          className={`px-2 py-1 rounded ${editor.isActive("underline") ? "underline text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleUnderline?.().run()}
          disabled={!editor.can().toggleUnderline?.()}
        >
          <u>U</u>
        </button>
        {/* List Buttons */}
        <button
          type="button"
          aria-label="Bulleted List"
          aria-pressed={editor.isActive("bulletList")}
          className={`px-2 py-1 rounded ${editor.isActive("bulletList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <circle cx="4" cy="5" r="1.5" fill="currentColor" />
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="9" r="1.5" fill="currentColor" />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="13" r="1.5" fill="currentColor" />
            <rect
              x="7"
              y="12"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Numbered List"
          aria-pressed={editor.isActive("orderedList")}
          className={`px-2 py-1 rounded ${editor.isActive("orderedList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <text x="2" y="7" fontSize="6" fill="currentColor">
              1.
            </text>
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <text x="2" y="13" fontSize="6" fill="currentColor">
              2.
            </text>
            <rect
              x="7"
              y="10"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        {/* Divider */}
        <span className="mx-2 border-l h-6" />
        {/* Alignment Buttons */}
        <button
          type="button"
          aria-label="Align Left"
          aria-pressed={editor.isActive({ textAlign: "left" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "left" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="3" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Center"
          aria-pressed={editor.isActive({ textAlign: "center" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "center" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="5" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Right"
          aria-pressed={editor.isActive({ textAlign: "right" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "right" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Justify"
          aria-pressed={editor.isActive({ textAlign: "justify" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "justify" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("justify").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="8"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      {/* Undo/Redo and Change Indicators */}
      <div className="flex items-center justify-between px-2 py-2">
        <div className="flex gap-2">
          <button
            type="button"
            aria-label="Undo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().undo().run()}
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M7 4L3 8L7 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Undo
          </button>
          <button
            type="button"
            aria-label="Redo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().redo().run()}
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M11 4L15 8L11 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Redo
          </button>
        </div>
        <div className="flex gap-6 items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-blue-100 border border-blue-300" />
              <span className="text-gray-500 text-sm">Original AI Text</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-gray-200 border border-gray-400" />
              <span className="text-gray-500 text-sm">New Changes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface Comment {
  id: string;
  text: string;
  created_at: string;
  user: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

interface CommentsPopupProps {
  onClose: () => void;
  reportSectionId: string;
}

const CommentsPopup: React.FC<CommentsPopupProps> = ({
  onClose,
  reportSectionId,
}) => {
  const { sdk } = useSDK();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState("");

  useEffect(() => {
    const fetchComments = async () => {
      try {
        setLoading(true);
        const response = await sdk.getSectionComments({
          report_section_id: reportSectionId,
        });
        if (response.error) {
          throw new Error(response.message || "Failed to fetch comments");
        }
        setComments(response.data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load comments"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchComments();
  }, [sdk, reportSectionId]);

  const handleSubmit = async () => {
    if (!newComment.trim()) return;

    try {
      setSubmitting(true);
      setError(null);
      const userId = localStorage.getItem("user");
      if (!userId) {
        throw new Error("User ID not found");
      }

      const response = await sdk.addSectionComment({
        report_section_id: reportSectionId,
        user_id: userId,
        text: newComment.trim(),
      });

      if (response.error) {
        throw new Error(response.message || "Failed to add comment");
      }

      // Refresh comments after adding new one
      const updatedResponse = await sdk.getSectionComments({
        report_section_id: reportSectionId,
      });
      if (updatedResponse.error) {
        throw new Error(
          updatedResponse.message || "Failed to fetch updated comments"
        );
      }
      setComments(updatedResponse.data);
      setNewComment("");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to add comment");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="absolute bottom-[50px] left-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="flex flex-col flex-shrink-0 justify-center items-start gap-4 p-4 w-96 h-[512px] rounded-lg border-0 border-gray-200 bg-white shadow-xl relative">
        {/* Header */}
        <div className="flex justify-between items-center w-full">
          <h2 className="text-gray-800 font-['Inter'] font-medium leading-6">
            Comments
          </h2>
          <button
            onClick={onClose}
            className="flex justify-center items-center w-3 h-4"
            aria-label="Close comments"
          >
            <svg
              width={12}
              height={16}
              viewBox="0 0 12 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z"
                fill="#9CA3AF"
              />
            </svg>
          </button>
        </div>
        {/* Comments List */}
        <div className="flex flex-col gap-4 w-[22rem] h-[17.5rem] overflow-y-auto">
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <div className="text-red-600 text-center p-4">{error}</div>
          ) : comments.length === 0 ? (
            <div className="text-gray-500 text-center p-4">No comments yet</div>
          ) : (
            comments.map((comment) => (
              <div
                key={comment.id}
                className="flex flex-col gap-2 p-4 rounded-lg bg-gray-50"
              >
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-gray-300">
                    {comment.user.avatar && (
                      <img
                        src={comment.user.avatar}
                        alt={comment.user.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <div className="text-gray-800 font-['Inter'] font-medium">
                      {comment.user.name}
                    </div>
                    <div className="text-gray-500 font-['Inter'] text-sm">
                      Generated on:{" "}
                      {format(new Date(comment.created_at), "MMM d, yyyy")}
                    </div>
                  </div>
                </div>
                <p className="text-gray-600 font-['Inter'] leading-normal">
                  {comment.text}
                </p>
              </div>
            ))
          )}
        </div>
        {/* Add Comment */}
        <div className="flex flex-col gap-2 w-full">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Add your comment..."
            className="p-3 h-24 rounded-lg border border-gray-200 bg-white text-gray-600 font-['Inter'] leading-6 resize-none focus:outline-none focus:border-gray-300"
          />
          <button
            onClick={handleSubmit}
            disabled={!newComment.trim() || submitting}
            className="flex justify-center items-center gap-2 px-4 py-2 rounded-lg bg-gray-800 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <>
                <svg
                  width={16}
                  height={16}
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_1_1221)">
                    <path
                      d="M15.5657 0.175119C15.8813 0.393869 16.047 0.771994 15.9876 1.15012L13.9876 14.1501C13.9407 14.4532 13.7563 14.7189 13.4876 14.8689C13.2188 15.0189 12.897 15.0376 12.6126 14.9189L8.87508 13.3657L6.73446 15.6814C6.45633 15.9845 6.01883 16.0845 5.63446 15.9345C5.25008 15.7845 5.00008 15.4126 5.00008 15.0001V12.3876C5.00008 12.2626 5.04696 12.1439 5.13133 12.0532L10.3688 6.33762C10.5501 6.14074 10.5438 5.83762 10.3563 5.65012C10.1688 5.46262 9.86571 5.45012 9.66883 5.62824L3.31258 11.2751L0.553206 9.89387C0.221956 9.72824 0.00945635 9.39699 8.13452e-05 9.02824C-0.00929365 8.65949 0.184456 8.31574 0.503206 8.13137L14.5032 0.131369C14.8376 -0.0592555 15.2501 -0.0405055 15.5657 0.175119Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_1_1221">
                      <path d="M0 0H16V16H0V0Z" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                Send Comment
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

const DocumentSectionEditorPage: React.FC = () => {
  const [sections, setSections] = useState<Section[]>([]);
  const [activeSection, setActiveSection] = useState(0);
  const [showComments, setShowComments] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [originalContent, setOriginalContent] = useState<string>("");
  const navigate = useNavigate();
  const [hasReadSection, setHasReadSection] = useState(false);
  const editorContentRef = useRef<HTMLDivElement>(null);
  const [showWarning, setShowWarning] = useState(false);

  // Fetch sections from backend
  useEffect(() => {
    const fetchSections = async () => {
      try {
        const reportId = localStorage.getItem("reportId");

        if (!reportId) {
          throw new Error("No report ID found");
        }

        const sdk = new MkdSDK();
        const response = await sdk.getReportSections(reportId);

        if (response.error) {
          throw new Error(response.message || "Failed to fetch report data");
        }

        if (!response.sections || !Array.isArray(response.sections)) {
          throw new Error("Invalid response format");
        }

        // Sort sections by order if available
        const sortedSections = response.sections.sort(
          (a, b) => (a.order || 0) - (b.order || 0)
        );

        setSections(sortedSections);
        setOriginalContent(sortedSections[activeSection]?.body || "");

        setError(null);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while fetching the report"
        );
        setSections([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSections();
  }, []);

  // Initialize TipTap editor with change tracking
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
      }),
      Underline,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      Highlight.configure({
        multicolor: true,
        HTMLAttributes: {
          class: "bg-gray-200",
        },
      }),
    ],
    content: sections[activeSection]?.body || "",
    onUpdate: ({ editor, transaction }) => {
      const currentContent = editor.getHTML();

      if (originalContent) {
        // Only process if there are actual changes
        if (transaction.docChanged) {
          // Get the current selection
          const { from, to } = editor.state.selection;

          // Apply highlight to the selected/changed text
          editor
            .chain()
            .focus()
            .setTextSelection({ from, to })
            .setHighlight({ color: "#D1D5DB" })
            .run();
        }
      }
    },
    onBlur: ({ editor }) => {
      const html = editor.getHTML();
      setSections((prev) =>
        prev.map((section, idx) =>
          idx === activeSection ? { ...section, body: html } : section
        )
      );
    },
    editorProps: {
      attributes: {
        class:
          "prose prose-sm max-w-none min-h-[400px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",
        spellCheck: "true",
        "aria-label": `Edit ${sections[activeSection]?.title || ""}`,
      },
    },
    parseOptions: { preserveWhitespace: "full" },
  });

  // When activeSection changes, update editor content and original content
  useEffect(() => {
    if (editor && sections[activeSection]) {
      editor.commands.setContent(sections[activeSection].body || "", false);
      // Update original content from localStorage
      const storedSections = localStorage.getItem("reportSections");
      if (storedSections) {
        const parsedSections = JSON.parse(storedSections);
        setOriginalContent(parsedSections[activeSection]?.body || "");
      }
    }
  }, [activeSection, editor, sections]);

  // Navigation handlers
  const goToSection = useCallback(
    (idx: number) => {
      if (idx >= 0 && idx < sections.length) setActiveSection(idx);
    },
    [sections.length]
  );

  // Scroll detection for reading the whole section
  useEffect(() => {
    setHasReadSection(false); // Reset on section change
    const el = editorContentRef.current;
    if (!el) return;
    el.scrollTop = 0; // Always scroll to top on section change
    // If not scrollable, mark as read
    if (el.scrollHeight <= el.clientHeight + 1) {
      setHasReadSection(true);
      return;
    }
    const handleScroll = () => {
      if (el.scrollTop + el.clientHeight >= el.scrollHeight - 10) {
        setHasReadSection(true);
      }
    };
    el.addEventListener("scroll", handleScroll);
    return () => el.removeEventListener("scroll", handleScroll);
  }, [activeSection, sections.length]);

  // Handler for Next/Previous navigation
  const guardedGoToSection = (idx: number) => {
    if (!hasReadSection) {
      setShowWarning(true);
      setTimeout(() => setShowWarning(false), 3000);
      return;
    }
    goToSection(idx);
  };

  // Handler for Continue/Proceed button
  const handleContinueOrProceed = () => {
    if (!hasReadSection) {
      setShowWarning(true);
      setTimeout(() => setShowWarning(false), 3000);
      return;
    }
    if (activeSection === sections.length - 1) {
      localStorage.setItem("reportSections", JSON.stringify(sections));
      navigate("/member/review-changes");
    } else {
      goToSection(activeSection + 1);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-red-600">Error loading report: {error}</div>
      </div>
    );
  }

  if (!sections.length) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-gray-600">Report not found</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-shrink-0 items-center pb-[396px] w-full h-[1440px] border-0 border-gray-200 bg-gray-50">
      {/* Warning at the top center */}
      {showWarning && (
        <div className="fixed top-3 left-1/2 -translate-x-1/2 bg-yellow-100 text-yellow-800 px-6 py-3 rounded shadow-lg z-50 border border-yellow-300">
          You must read and verify the entire section first before proceeding to the next one!
        </div>
      )}
      <div className="flex flex-shrink-0 justify-center items-center p-6 w-[1184px] h-[1036px] border-0 border-gray-200 bg-black/0">
        <div className="flex flex-col flex-shrink-0 justify-center items-start gap-6 w-[1136px] h-[988px] border-0 border-gray-200 bg-black/0">
          {/* Header */}
          <header className="flex flex-shrink-0 justify-between items-center p-4 w-full h-[4.5rem] rounded-lg border-0 border-gray-200 bg-white">
            <h1 className="flex justify-center items-center  h-8 text-gray-800 font-['Inter'] text-2xl leading-[normal]">
              Document Section Editor
            </h1>
            <div className="flex items-center gap-4">
              <button
                type="button"
                className="flex items-center gap-2 pb-[0.4375rem] pr-[1.125rem] pt-2 pl-4 h-10 rounded-md border-0 border-gray-200 bg-gray-800 text-white"
                onClick={handleContinueOrProceed}
              >
                <svg
                  width={13}
                  height={16}
                  viewBox="0 0 13 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  aria-hidden="true"
                >
                  <g clipPath="url(#clip0_1_619)">
                    <path
                      d="M6.875 0C5.56875 0 4.45625 0.834375 4.04688 2H2.875C1.77188 2 0.875 2.89687 0.875 4V14C0.875 15.1031 1.77188 16 2.875 16H10.875C11.9781 16 12.875 15.1031 12.875 14V4C12.875 2.89687 11.9781 2 10.875 2H9.70312C9.29375 0.834375 8.18125 0 6.875 0Z"
                      fill="white"
                    />
                  </g>
                </svg>
                Proceed to Next Step
              </button>
            </div>
          </header>

          {/* Header with Progress */}
          <div className="w-full rounded-lg bg-white">
            {/* Header Bar */}
            <div className="flex items-center justify-between px-6 py-4 border-b">
              {/* Back Button */}
              <button
                className="flex items-center gap-2 text-gray-600 disabled:opacity-40"
                onClick={() => navigate(-1)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="12"
                  viewBox="0 0 14 12"
                  fill="none"
                >
                  <path
                    d="M0.293701 5.29414C-0.0969238 5.68477 -0.0969238 6.31914 0.293701 6.70977L5.2937 11.7098C5.68433 12.1004 6.3187 12.1004 6.70933 11.7098C7.09995 11.3191 7.09995 10.6848 6.70933 10.2941L3.41245 7.00039H13C13.5531 7.00039 14 6.55352 14 6.00039C14 5.44727 13.5531 5.00039 13 5.00039H3.41558L6.7062 1.70664C7.09683 1.31602 7.09683 0.681641 6.7062 0.291016C6.31558 -0.0996094 5.6812 -0.0996094 5.29058 0.291016L0.290576 5.29102L0.293701 5.29414Z"
                    fill="#4B5563"
                  />
                </svg>
                <span className="hidden sm:inline">Back</span>
              </button>
              <div className="flex items-center justify-between">
                {/* Previous Button */}
                <button
                  className="flex border rounded-full p-3 items-center gap-2 text-gray-600 disabled:opacity-40 justify-end"
                  onClick={() => guardedGoToSection(activeSection - 1)}
                  disabled={activeSection === 0}
                  aria-label="Previous section"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="12"
                    viewBox="0 0 14 12"
                    fill="none"
                  >
                    <path
                      d="M0.293701 5.29414C-0.0969238 5.68477 -0.0969238 6.31914 0.293701 6.70977L5.2937 11.7098C5.68433 12.1004 6.3187 12.1004 6.70933 11.7098C7.09995 11.3191 7.09995 10.6848 6.70933 10.2941L3.41245 7.00039H13C13.5531 7.00039 14 6.55352 14 6.00039C14 5.44727 13.5531 5.00039 13 5.00039H3.41558L6.7062 1.70664C7.09683 1.31602 7.09683 0.681641 6.7062 0.291016C6.31558 -0.0996094 5.6812 -0.0996094 5.29058 0.291016L0.290576 5.29102L0.293701 5.29414Z"
                      fill="#4B5563"
                    />
                  </svg>
                </button>
                {/* Section Title Centered */}
                <span className="text-xl min-w-[300px] mx-4 font-medium text-gray-900 text-center flex-1">
                  {sections[activeSection].title}
                </span>
                {/* Next Button */}
                <button
                  className="flex border rounded-full p-3 items-center gap-2 text-gray-600 disabled:opacity-40 justify-end"
                  onClick={() => guardedGoToSection(activeSection + 1)}
                  disabled={activeSection === sections.length - 1}
                  aria-label="Next section"
                >
                  <svg
                    style={{ transform: "rotate(180deg)" }}
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="12"
                    viewBox="0 0 14 12"
                    fill="none"
                  >
                    <path
                      d="M0.293701 5.29414C-0.0969238 5.68477 -0.0969238 6.31914 0.293701 6.70977L5.2937 11.7098C5.68433 12.1004 6.3187 12.1004 6.70933 11.7098C7.09995 11.3191 7.09995 10.6848 6.70933 10.2941L3.41245 7.00039H13C13.5531 7.00039 14 6.55352 14 6.00039C14 5.44727 13.5531 5.00039 13 5.00039H3.41558L6.7062 1.70664C7.09683 1.31602 7.09683 0.681641 6.7062 0.291016C6.31558 -0.0996094 5.6812 -0.0996094 5.29058 0.291016L0.290576 5.29102L0.293701 5.29414Z"
                      fill="#4B5563"
                    />
                  </svg>
                </button>
              </div>
              <div></div>
            </div>
            {/* Progress Bar Row */}
            <div className="flex-1 mx-4">
              <div className="w-full h-2 bg-gray-200 rounded-full">
                <div
                  className="bg-gray-800 h-2 rounded-full"
                  style={{
                    width: `${((activeSection + 1) / sections.length) * 100}%`,
                  }}
                />
              </div>
            </div>
            <div className="flex items-center justify-between px-6 py-2">
              <span className="text-xs text-gray-500">
                Section {activeSection + 1} of {sections.length}
              </span>

              <span className="text-xs text-gray-500">
                {Math.round(((activeSection + 1) / sections.length) * 100)}%
                Reviewed
              </span>
            </div>
          </div>

          {/* Editor Area */}
          <div className="flex justify-center items-center p-8 w-full rounded-lg bg-white">
            <div className="w-[90%] h-full">
              <Toolbar editor={editor} />
              <div
                className="w-full h-[811px] overflow-y-auto"
                ref={editorContentRef}
              >
                <EditorContent editor={editor} />
              </div>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="flex relative justify-between items-center w-full">
            <div className="flex gap-4">
              <button
                className="flex items-center gap-2 px-4 py-2 text-gray-600"
                onClick={() => setShowComments(true)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <g clipPath="url(#clip0_1_1187)">
                    <path
                      d="M16 7.5C16 11.0906 12.4187 14 7.99995 14C6.84058 14 5.74057 13.8 4.74682 13.4406C4.37495 13.7125 3.7687 14.0844 3.04995 14.3969C2.29995 14.7219 1.39683 15 0.49995 15C0.296825 15 0.115575 14.8781 0.0374502 14.6906C-0.0406748 14.5031 0.00307515 14.2906 0.1437 14.1469L0.153075 14.1375C0.16245 14.1281 0.17495 14.1156 0.1937 14.0938C0.228075 14.0562 0.2812 13.9969 0.346825 13.9156C0.47495 13.7594 0.646825 13.5281 0.821825 13.2406C1.13433 12.7219 1.4312 12.0406 1.49058 11.275C0.553075 10.2125 -4.98406e-05 8.90938 -4.98406e-05 7.5C-4.98406e-05 3.90937 3.5812 1 7.99995 1C12.4187 1 16 3.90937 16 7.5Z"
                      fill="#4B5563"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_1_1187">
                      <path d="M0 0H16V16H0V0Z" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                Add Comment
              </button>
            </div>
            <button
              className="flex items-center gap-2 px-6 py-2 bg-gray-800 text-white rounded-md disabled:opacity-50"
              onClick={handleContinueOrProceed}
              disabled={!hasReadSection}
            >
              {activeSection === sections.length - 1
                ? "Proceed to Psychometrist Review"
                : "Continue to Next Section"}
            </button>
            {showComments && (
              <CommentsPopup
                onClose={() => setShowComments(false)}
                reportSectionId={sections[activeSection].id.toString()}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentSectionEditorPage;
