import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{r as b,L as h}from"./vendor-489b60f1.js";import{u as j}from"./react-hook-form-7e42b371.js";import{o as y}from"./yup-fe85ba88.js";import{c as w,a as v}from"./yup-5d8330af.js";import{I as k}from"./index-ec6e151a.js";import{a as L,u as S,L as N}from"./index-95f0e460.js";import{M as E}from"./index-e9605eb4.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const X=()=>{const{sdk:n}=L(),{showToast:l,tokenExpireError:d}=S(),[r,s]=b.useState(!1),p=w({email:v().email().required()}).required(),{register:c,handleSubmit:u,setError:o,formState:{errors:x}}=j({resolver:y(p)}),g=async f=>{try{s(!0);const e=await n.forgot(f.email);if(!e.error)l("Reset Code Sent");else if(e.validation){const i=Object.keys(e.validation);for(let a=0;a<i.length;a++){const m=i[a];o(m,{type:"manual",message:e.validation[m]})}}s(!1)}catch(e){s(!1),o("email",{type:"manual",message:e.response.data.message?e.response.data.message:e.message}),d(e.response.data.message?e.response.data.message:e.message)}};return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[t.jsxs("form",{onSubmit:u(g),className:"mb-4 mt-8 rounded bg-white px-8 pb-8 pt-6 shadow-md ",children:[t.jsx("div",{className:"mb-4",children:t.jsx(N,{children:t.jsx(E,{name:"email",type:"text",errors:x,register:c,label:"Email"})})}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(k,{className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:r,disabled:r,children:"Forgot Password"}),t.jsx(h,{className:"inline-block align-baseline text-sm font-bold text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),t.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{X as default};
