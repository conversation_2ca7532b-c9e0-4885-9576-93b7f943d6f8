import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { BackButton } from "@/components/BackButton";
import { StringCaser } from "@/utils/utils";
import { useContexts } from "@/hooks/useContexts";

const TopHeader = () => {
  const stringCaser = new StringCaser();
  const {
    globalState: { showBackButton, isOpen },
    globalDispatch,
  } = useContexts();

  const [currentPath, setCurrentPath] = useState("");
  const location = useLocation();

  useEffect(() => {
    const pathArr = location.pathname.split("/");
    if (pathArr[1] !== "user" && pathArr[1] !== "admin") {
      setCurrentPath(pathArr[1]);
    } else {
      setCurrentPath(pathArr[2]);
    }
  }, [location]);

  const handleOpenSidebar = () => {
    globalDispatch({ type: "OPEN_SIDEBAR", payload: { isOpen: true } });
  };

  return (
    <div className="sticky right-0 top-0 z-20 flex h-14 max-h-14 w-full items-center justify-between bg-white px-6 py-4 shadow-sm">
      <div className="flex items-center gap-3">
        {/* Hamburger menu only when sidebar is closed */}
        {!isOpen && (
          <button
            onClick={handleOpenSidebar}
            className="flex items-center justify-center w-10 h-10 rounded-md hover:bg-gray-100 transition-colors"
            aria-label="Open sidebar"
          >
            <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
              <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor"/>
              <rect x="4" y="11" width="16" height="2" rx="1" fill="currentColor"/>
              <rect x="4" y="16" width="16" height="2" rx="1" fill="currentColor"/>
            </svg>
          </button>
        )}
        {/* {showBackButton && <BackButton />}
        <h1 className="text-base capitalize">
          {currentPath === "generate-ui"
            ? "Generate UI"
            : stringCaser.Capitalize(currentPath, {
                separator: " "
              })}
        </h1> */}
      </div>
    </div>
  );
};

export default TopHeader;
