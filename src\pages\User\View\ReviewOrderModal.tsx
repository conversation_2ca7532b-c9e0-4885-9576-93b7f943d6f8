import React, { useState } from "react";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "react-beautiful-dnd";

export interface ReviewSection {
  id: string;
  title: string;
  order: number;
}

interface ReviewOrderModalProps {
  open: boolean;
  onClose: () => void;
  onBeginReview: (sections: ReviewSection[]) => void;
  sections: ReviewSection[];
}

const ReviewOrderModal: React.FC<ReviewOrderModalProps> = ({
  open,
  onClose,
  onBeginReview,
  sections: initialSections,
}) => {
  const [sections, setSections] = useState<ReviewSection[]>(initialSections);

  // Update sections when initialSections prop changes
  React.useEffect(() => {
    setSections(initialSections);
  }, [initialSections]);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setSections(items.map((item, idx) => ({ ...item, order: idx + 1 })));
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 flex flex-col gap-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl text-gray-800 font-semibold">Review Order</h2>
          <button className="p-1" aria-label="Close" onClick={onClose}>
            <svg
              width={12}
              height={16}
              viewBox="0 0 12 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z"
                fill="#4B5563"
              />
            </svg>
          </button>
        </div>
        <div className="text-gray-600">
          Arrange sections in order of review priority. Drag to reorder.
        </div>
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="sections">
            {(provided: any) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="flex flex-col gap-2 max-h-[300px] overflow-y-auto"
              >
                {sections.map((section, index) => (
                  <Draggable
                    key={section.id}
                    draggableId={section.id}
                    index={index}
                  >
                    {(provided: any) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className="flex items-center gap-3 p-3 w-full h-12 rounded-md bg-gray-50"
                      >
                        <div {...provided.dragHandleProps}>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="10"
                            height="14"
                            viewBox="0 0 10 14"
                            fill="none"
                          >
                            <path
                              d="M1.25 10H2.75C3.44062 10 4 10.5594 4 11.25V12.75C4 13.4406 3.44062 14 2.75 14H1.25C0.559375 14 0 13.4406 0 12.75V11.25C0 10.5594 0.559375 10 1.25 10ZM7.25 10H8.75C9.44063 10 10 10.5594 10 11.25V12.75C10 13.4406 9.44063 14 8.75 14H7.25C6.55937 14 6 13.4406 6 12.75V11.25C6 10.5594 6.55937 10 7.25 10ZM1.25 9C0.559375 9 0 8.44063 0 7.75V6.25C0 5.55937 0.559375 5 1.25 5H2.75C3.44062 5 4 5.55937 4 6.25V7.75C4 8.44063 3.44062 9 2.75 9H1.25ZM7.25 5H8.75C9.44063 5 10 5.55937 10 6.25V7.75C10 8.44063 9.44063 9 8.75 9H7.25C6.55937 9 6 8.44063 6 7.75V6.25C6 5.55937 6.55937 5 7.25 5ZM1.25 4C0.559375 4 0 3.44062 0 2.75V1.25C0 0.559375 0.559375 0 1.25 0H2.75C3.44062 0 4 0.559375 4 1.25V2.75C4 3.44062 3.44062 4 2.75 4H1.25ZM7.25 0H8.75C9.44063 0 10 0.559375 10 1.25V2.75C10 3.44062 9.44063 4 8.75 4H7.25C6.55937 4 6 3.44062 6 2.75V1.25C6 0.559375 6.55937 0 7.25 0Z"
                              fill="#9CA3AF"
                            />
                          </svg>
                        </div>
                        <span className="text-gray-800">
                           {index + 1}. {section.title}
                        </span>
                        <svg
                          className="ml-auto"
                          width={10}
                          height={16}
                          viewBox="0 0 10 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M5.70615 0.293945C5.31553 -0.0966797 4.68115 -0.0966797 4.29053 0.293945L1.29053 3.29395C0.899902 3.68457 0.899902 4.31895 1.29053 4.70957C1.68115 5.1002 2.31553 5.1002 2.70615 4.70957L3.9999 3.41582V12.5846L2.70615 11.2939C2.31553 10.9033 1.68115 10.9033 1.29053 11.2939C0.899902 11.6846 0.899902 12.3189 1.29053 12.7096L4.29053 15.7096C4.68115 16.1002 5.31553 16.1002 5.70615 15.7096L8.70615 12.7096C9.09678 12.3189 9.09678 11.6846 8.70615 11.2939C8.31553 10.9033 7.68115 10.9033 7.29053 11.2939L5.9999 12.5846V3.41582L7.29365 4.70957C7.68428 5.1002 8.31865 5.1002 8.70928 4.70957C9.0999 4.31895 9.0999 3.68457 8.70928 3.29395L5.70928 0.293945H5.70615Z"
                            fill="#9CA3AF"
                          />
                        </svg>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
        <div className="flex justify-end gap-3 mt-4">
          <button
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="flex items-center gap-2 px-4 py-2 bg-gray-800 text-white rounded-md"
            onClick={() => onBeginReview(sections)}
          >
            <svg
              width={13}
              height={16}
              viewBox="0 0 13 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6.39062 0C5.08437 0 3.97187 0.834375 3.5625 2H2.39062C1.2875 2 0.390625 2.89687 0.390625 4V14C0.390625 15.1031 1.2875 16 2.39062 16H10.3906C11.4937 16 12.3906 15.1031 12.3906 14V4C12.3906 2.89687 11.4937 2 10.3906 2H9.21875C8.80937 0.834375 7.69688 0 6.39062 0Z"
                fill="white"
              />
            </svg>
            Begin Review
          </button>
        </div>
        <p className="text-center text-sm text-gray-500">
          This is for editing purposes only and won't be the final report.
        </p>
      </div>
    </div>
  );
};

export default ReviewOrderModal;
