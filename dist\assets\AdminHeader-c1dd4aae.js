import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{R as r,L as c,f as m}from"./vendor-489b60f1.js";import{G as d}from"./react-icons-fe0a0adf.js";import{b as p,G as u,A,t as v}from"./index-95f0e460.js";import{M as s}from"./index.esm-1e38c052.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";function f(a){return d({tag:"svg",attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M64.12,147.8a4,4,0,0,1-4,4.2H16a8,8,0,0,1-7.8-6.17,8.35,8.35,0,0,1,1.62-6.93A67.79,67.79,0,0,1,37,117.51a40,40,0,1,1,66.46-35.8,3.94,3.94,0,0,1-2.27,4.18A64.08,64.08,0,0,0,64,144C64,145.28,64,146.54,64.12,147.8Zm182-8.91A67.76,67.76,0,0,0,219,117.51a40,40,0,1,0-66.46-35.8,3.94,3.94,0,0,0,2.27,4.18A64.08,64.08,0,0,1,192,144c0,1.28,0,2.54-.12,3.8a4,4,0,0,0,4,4.2H240a8,8,0,0,0,7.8-6.17A8.33,8.33,0,0,0,246.17,138.89Zm-89,43.18a48,48,0,1,0-58.37,0A72.13,72.13,0,0,0,65.07,212,8,8,0,0,0,72,224H184a8,8,0,0,0,6.93-12A72.15,72.15,0,0,0,157.19,182.07Z"}}]})(a)}let h=new p;const j=[{to:"/admin/dashboard",text:"Dashboard",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"admin",visible:!0},{to:"/admin/job",text:"  Jobs",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"job",visible:!0},{to:"/admin/cms",text:"  Cmss",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"cms",visible:!0},{to:"/admin/uploads",text:"  Uploadss",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"uploads",visible:!0},{to:"/admin/tokens",text:"  Tokenss",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"tokens",visible:!0},{to:"/admin/preference",text:"  Preferences",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"preference",visible:!0},{to:"/admin/user",text:"  Users",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"user",visible:!0},{to:"/admin/document",text:"  Documents",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"document",visible:!0},{to:"/admin/project",text:"  Projects",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"project",visible:!0},{to:"/admin/report_section",text:"  Report_sections",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"report_section",visible:!0},{to:"/admin/report",text:"  Reports",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"report",visible:!0},{to:"/admin/report_order",text:"  Report_orders",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"report_order",visible:!0},{to:"/admin/comment",text:"  Comments",icon:e.jsx(s,{className:"text-xl text-[#A8A8A8]"}),value:"comment",visible:!0},{to:"/admin/profile",text:"Profile",icon:e.jsx(f,{className:"text-xl text-[#A8A8A8]"}),value:"profile",visible:!0}],I=()=>{const{state:{isOpen:a,path:l},dispatch:o}=r.useContext(u),{state:b,dispatch:i}=r.useContext(A);r.useState(!1),r.useState(!1);let n=t=>{o({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};async function x(){try{const t=await h.getProfile();i({type:"UPDATE_PROFILE",payload:t})}catch(t){console.log("Error",t),v(i,t)}}return r.useEffect(()=>{x()},[]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:`z-50 flex max-h-screen flex-1 flex-col border border-[#E0E0E0] bg-white py-4 text-[#A8A8A8] transition-all ${a?"fixed h-screen w-[15rem] min-w-[15rem] max-w-[15rem] md:relative":"relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-black text-white"}`,children:[e.jsxs("div",{className:`text-[#393939] ${a?"flex w-full":"flex items-center justify-center"}`,children:[e.jsx("div",{}),a&&e.jsx("div",{className:"text-2xl font-bold",children:e.jsx(c,{to:"/",children:e.jsxs("h4",{className:"flex cursor-pointer items-center px-4 pb-4 font-sans font-bold",children:["Baas Brand"," "]})})})]}),e.jsx("div",{className:"h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:j.filter(t=>t.visible).map(t=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(m,{to:t.to,className:`${l===t.value?"active-nav":""}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,a&&e.jsx("span",{children:t.text})]})})},t.value))})})}),e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>n(!a),children:e.jsx("svg",{className:`transition-transform ${a?"":"rotate-180"}`,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})})]})})};export{I as AdminHeader,I as default};
