import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{d as K,r as l}from"./vendor-489b60f1.js";import{u as q,E as G}from"./@tiptap/react-8d1cdd44.js";import{S as Q}from"./@tiptap/starter-kit-8dc7719e.js";import{T as X}from"./@tiptap/extension-text-align-4d325d88.js";import{U as Y}from"./@tiptap/extension-underline-5fc56973.js";import{T as ee}from"./@tiptap/extension-table-a2bc9598.js";import{T as te}from"./@tiptap/extension-table-row-159ab625.js";import{T as se}from"./@tiptap/extension-table-cell-b57d510e.js";import{T as re}from"./@tiptap/extension-table-header-412f6f9c.js";import{H as ie}from"./@tiptap/extension-highlight-1b3a19b9.js";import{a as le}from"./index-95f0e460.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const ne=({editor:r})=>{var n,x;return l.useState(!1),l.useState(!1),r?e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex items-center gap-2 border-b px-2 py-2 bg-white",children:[e.jsx("button",{type:"button","aria-label":"Bold","aria-pressed":r.isActive("bold"),className:`px-2 py-1 rounded ${r.isActive("bold")?"font-bold text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().toggleBold().run(),children:e.jsx("b",{children:"B"})}),e.jsx("button",{type:"button","aria-label":"Italic","aria-pressed":r.isActive("italic"),className:`px-2 py-1 rounded ${r.isActive("italic")?"italic text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().toggleItalic().run(),children:e.jsx("i",{children:"I"})}),e.jsx("button",{type:"button","aria-label":"Underline","aria-pressed":r.isActive("underline"),className:`px-2 py-1 rounded ${r.isActive("underline")?"underline text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>{var a,p;return(p=(a=r.chain().focus()).toggleUnderline)==null?void 0:p.call(a).run()},disabled:!((x=(n=r.can()).toggleUnderline)!=null&&x.call(n)),children:e.jsx("u",{children:"U"})}),e.jsx("button",{type:"button","aria-label":"Bulleted List","aria-pressed":r.isActive("bulletList"),className:`px-2 py-1 rounded ${r.isActive("bulletList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().toggleBulletList().run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("circle",{cx:"4",cy:"5",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"9",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"13",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"12",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Numbered List","aria-pressed":r.isActive("orderedList"),className:`px-2 py-1 rounded ${r.isActive("orderedList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().toggleOrderedList().run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("text",{x:"2",y:"7",fontSize:"6",fill:"currentColor",children:"1."}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("text",{x:"2",y:"13",fontSize:"6",fill:"currentColor",children:"2."}),e.jsx("rect",{x:"7",y:"10",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("span",{className:"mx-2 border-l h-6"}),e.jsx("button",{type:"button","aria-label":"Align Left","aria-pressed":r.isActive({textAlign:"left"}),className:`px-2 py-1 rounded ${r.isActive({textAlign:"left"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().setTextAlign("left").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Center","aria-pressed":r.isActive({textAlign:"center"}),className:`px-2 py-1 rounded ${r.isActive({textAlign:"center"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().setTextAlign("center").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"5",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Right","aria-pressed":r.isActive({textAlign:"right"}),className:`px-2 py-1 rounded ${r.isActive({textAlign:"right"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().setTextAlign("right").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Justify","aria-pressed":r.isActive({textAlign:"justify"}),className:`px-2 py-1 rounded ${r.isActive({textAlign:"justify"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>r.chain().focus().setTextAlign("justify").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})})]}),e.jsxs("div",{className:"flex items-center justify-between px-2 py-2",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{type:"button","aria-label":"Undo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>r.chain().focus().undo().run(),children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M7 4L3 8L7 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Undo"]}),e.jsxs("button",{type:"button","aria-label":"Redo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>r.chain().focus().redo().run(),children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M11 4L15 8L11 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Redo"]})]}),e.jsx("div",{className:"flex gap-6 items-center",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-blue-100 border border-blue-300"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"Original AI Text"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-gray-200 border border-gray-400"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"New Changes"})]})]})})]})]}):null},_e=()=>{var Z,P,R;const r=K(),[n,x]=l.useState([]),[a,p]=l.useState(0),[o,f]=l.useState({type:"comments",isVisible:!1}),[T,D]=l.useState(!0),[C,y]=l.useState(!1),[j,b]=l.useState(null),[$,v]=l.useState([]),{sdk:d}=le(),[w,N]=l.useState([]),[U,S]=l.useState(!1),[L,H]=l.useState(null),[k,A]=l.useState([]),[O,V]=l.useState(!1),[M,E]=l.useState(null),[F,W]=l.useState(""),u=t=>{f(s=>({type:t,isVisible:s.type===t?!s.isVisible:!0}))};l.useEffect(()=>{(async()=>{try{const s=localStorage.getItem("reportId");if(!s)throw new Error("No report ID found");const i=await d.getReportSections(s);if(i.error)throw new Error(i.message||"Failed to fetch report data");if(!i.sections||!Array.isArray(i.sections))throw new Error("Invalid response format");const c=i.sections.sort((h,m)=>(h.order||0)-(m.order||0));x(c),b(null)}catch(s){b(s instanceof Error?s.message:"An error occurred while fetching the report"),x([])}finally{D(!1)}})()},[d]),l.useEffect(()=>{(async()=>{if(n[a])try{const s=await d.getSectionComments({report_section_id:n[a].id.toString()});if(s.error)throw new Error(s.message||"Failed to fetch comments");v(s.data)}catch(s){console.error("Error fetching comments:",s),v([])}})()},[d,a,n]);const g=q({extensions:[Q.configure({heading:{levels:[1,2,3]},bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}}),Y,X.configure({types:["heading","paragraph"]}),ee.configure({resizable:!0}),te,se,re,ie.configure({multicolor:!0,HTMLAttributes:{class:"bg-gray-200"}})],content:((Z=n[a])==null?void 0:Z.body)||"",onUpdate:({editor:t,transaction:s})=>{if(t.getHTML(),F&&s.docChanged){const{from:i,to:c}=t.state.selection;t.chain().focus().setTextSelection({from:i,to:c}).setHighlight({color:"#D1D5DB"}).run()}},onBlur:({editor:t})=>{const s=t.getHTML();x(i=>i.map((c,h)=>h===a?{...c,body:s}:c))},editorProps:{attributes:{class:"prose prose-sm max-w-none min-h-[600px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",spellCheck:"true","aria-label":`Edit ${((P=n[a])==null?void 0:P.title)||""}`}},parseOptions:{preserveWhitespace:"full"}});l.useEffect(()=>{var t;if(g&&n[a]){g.commands.setContent(n[a].body||"",!1);const s=localStorage.getItem("reportSections");if(s){const i=JSON.parse(s);W(((t=i[a])==null?void 0:t.body)||"")}}},[a,g,n]);const _=l.useCallback(t=>{t>=0&&t<n.length&&p(t)},[n.length]),z=async()=>{try{if(y(!0),!localStorage.getItem("reportId"))throw new Error("No report ID found");for(const s of n)await d.updateReportSection({id:s.id.toString(),title:s.title,content:s.body});console.log("Changes saved successfully")}catch(t){console.error("Error saving changes:",t)}finally{y(!1)}},J=()=>{localStorage.setItem("reportSections",JSON.stringify(n)),r("/doctor/report-overview")},B=async()=>{var t,s;try{S(!0),H(null);const i=await d.getDoctorCheatsheet();if(i.error)throw new Error(i.message||"Failed to fetch cheatsheet");if(!i.data||!Array.isArray(i.data.sections))throw new Error("Invalid response format from server");const c=i.data.sections.map((h,m)=>({...h,isExpanded:m===0,subsections:Array.isArray(h.subsections)?h.subsections:[]}));N(c)}catch(i){const c=((s=(t=i==null?void 0:i.response)==null?void 0:t.data)==null?void 0:s.message)||i.message||"An error occurred";H(c),N([])}finally{S(!1)}};l.useEffect(()=>{o.type==="cheatSheet"&&o.isVisible&&B()},[o.type,o.isVisible]);const I=async()=>{try{V(!0),E(null);const t=localStorage.getItem("reportId");if(!t)throw new Error("No report ID found");const s=await d.getPreviousReport({project_id:t});if(s.error)throw new Error(s.message||"Failed to fetch previous report");A(s.data||[])}catch(t){const s=t instanceof Error?t.message:"An error occurred";E(s),A([])}finally{V(!1)}};return l.useEffect(()=>{o.type==="oldReport"&&o.isVisible&&I()},[o.type,o.isVisible]),T?e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsx("div",{className:"w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):j?e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsxs("div",{className:"text-red-600",children:["Error loading report: ",j]})}):n.length?e.jsxs("div",{className:"flex min-h-screen bg-gray-50",children:[e.jsxs("div",{className:"flex-1 p-6",children:[e.jsxs("div",{className:"justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex mb-2 justify-end items-center gap-4",children:[e.jsxs("button",{onClick:()=>u("comments"),className:"flex border-2 items-center gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_1_2300)",children:e.jsx("path",{d:"M2.61483 8.45195C2.8828 7.95156 2.80077 7.33633 2.40976 6.92617C1.82733 6.31367 1.51562 5.57812 1.51562 4.8125C1.51562 3.07617 3.26015 1.3125 5.89062 1.3125C8.52108 1.3125 10.2656 3.07617 10.2656 4.8125C10.2656 6.54883 8.52108 8.3125 5.89062 8.3125C5.53241 8.3125 5.18515 8.27695 4.85702 8.21406C4.57265 8.15938 4.27733 8.19766 4.01757 8.32891C3.90546 8.38633 3.79062 8.44102 3.67304 8.49297C3.23554 8.68984 2.77343 8.86211 2.30858 8.98516C2.38515 8.85938 2.45624 8.73633 2.5246 8.61328C2.55468 8.56133 2.58476 8.50664 2.6121 8.45195H2.61483ZM0.203116 4.8125C0.203116 5.95547 0.673428 7.00273 1.45819 7.82852C1.43358 7.875 1.40624 7.92422 1.38163 7.96797C1.09999 8.47109 0.771866 8.96602 0.38085 9.39258C0.200382 9.58398 0.153897 9.86289 0.255069 10.1008C0.36171 10.3441 0.596866 10.5 0.859366 10.5C2.03515 10.5 3.2246 10.1363 4.21444 9.68789C4.34569 9.62773 4.47694 9.56484 4.60273 9.50195C5.01562 9.58398 5.44765 9.625 5.89062 9.625C9.03241 9.625 11.5781 7.47031 11.5781 4.8125C11.5781 2.15469 9.03241 0 5.89062 0C2.74882 0 0.203116 2.15469 0.203116 4.8125ZM12.0156 13.125C12.4586 13.125 12.8879 13.0813 13.3035 13.002C13.4293 13.0648 13.5605 13.1277 13.6918 13.1879C14.6816 13.6363 15.8711 14 17.0469 14C17.3094 14 17.5445 13.8441 17.6484 13.6035C17.7523 13.3629 17.7031 13.084 17.5226 12.8953C17.1344 12.4688 16.8062 11.9738 16.5219 11.4707C16.4973 11.4242 16.4699 11.3777 16.4453 11.3313C17.2328 10.5027 17.7031 9.45547 17.7031 8.3125C17.7031 5.73125 15.2996 3.62305 12.2836 3.50547C12.3957 3.92109 12.4531 4.35859 12.4531 4.8125V4.82891C14.8375 5.01211 16.3906 6.67461 16.3906 8.3125C16.3906 9.07812 16.0789 9.81367 15.4965 10.4234C15.1055 10.8336 15.0234 11.4516 15.2914 11.9492C15.3215 12.0039 15.3516 12.0586 15.3789 12.1105C15.4473 12.2336 15.5211 12.3566 15.5949 12.4824C15.1301 12.3594 14.668 12.1898 14.2305 11.9902C14.1129 11.9383 13.998 11.8836 13.8859 11.8262C13.6262 11.6949 13.3309 11.6566 13.0465 11.7113C12.7156 11.777 12.3711 11.8098 12.0129 11.8098C10.3258 11.8098 9.00507 11.0852 8.27226 10.1062C7.83476 10.2539 7.37538 10.3633 6.90507 10.4289C7.83202 12.0258 9.77343 13.125 12.0156 13.125Z",fill:"#374151"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2300",children:e.jsx("path",{d:"M0.203125 0H17.7031V14H0.203125V0Z",fill:"white"})})})]}),o.type==="comments"&&o.isVisible?"Hide":"View"," ","Comments"]}),e.jsxs("button",{onClick:()=>u("cheatSheet"),className:"flex items-center border-2 gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"15",height:"14",viewBox:"0 0 15 14",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_1_2260)",children:e.jsx("path",{d:"M4.84651 1.04438C5.11721 1.28774 5.13909 1.70063 4.89573 1.97133L2.92698 4.15883C2.80667 4.29282 2.63713 4.37211 2.45667 4.37485C2.2762 4.37758 2.10393 4.30922 1.97541 4.18344L0.878931 3.08969C0.624634 2.83266 0.624634 2.41703 0.878931 2.16C1.13323 1.90297 1.55159 1.90297 1.80588 2.16L2.41018 2.7643L3.91682 1.09086C4.16018 0.820159 4.57307 0.798284 4.84377 1.04164L4.84651 1.04438ZM4.84651 5.41938C5.11721 5.66274 5.13909 6.07563 4.89573 6.34633L2.92698 8.53383C2.80667 8.66782 2.63713 8.74711 2.45667 8.74985C2.2762 8.75258 2.10393 8.68422 1.97541 8.55844L0.878931 7.46469C0.621899 7.20766 0.621899 6.79203 0.878931 6.53774C1.13596 6.28344 1.55159 6.28071 1.80588 6.53774L2.41018 7.14203L3.91682 5.4686C4.16018 5.19789 4.57307 5.17602 4.84377 5.41938H4.84651ZM6.81252 2.62485C6.81252 2.14086 7.20354 1.74985 7.68752 1.74985H13.8125C14.2965 1.74985 14.6875 2.14086 14.6875 2.62485C14.6875 3.10883 14.2965 3.49985 13.8125 3.49985H7.68752C7.20354 3.49985 6.81252 3.10883 6.81252 2.62485ZM6.81252 6.99985C6.81252 6.51586 7.20354 6.12485 7.68752 6.12485H13.8125C14.2965 6.12485 14.6875 6.51586 14.6875 6.99985C14.6875 7.48383 14.2965 7.87485 13.8125 7.87485H7.68752C7.20354 7.87485 6.81252 7.48383 6.81252 6.99985ZM5.06252 11.3748C5.06252 10.8909 5.45354 10.4998 5.93752 10.4998H13.8125C14.2965 10.4998 14.6875 10.8909 14.6875 11.3748C14.6875 11.8588 14.2965 12.2498 13.8125 12.2498H5.93752C5.45354 12.2498 5.06252 11.8588 5.06252 11.3748ZM2.00002 10.0623C2.34812 10.0623 2.68196 10.2006 2.9281 10.4468C3.17424 10.6929 3.31252 11.0267 3.31252 11.3748C3.31252 11.7229 3.17424 12.0568 2.9281 12.3029C2.68196 12.5491 2.34812 12.6873 2.00002 12.6873C1.65193 12.6873 1.31809 12.5491 1.07195 12.3029C0.825805 12.0568 0.687524 11.7229 0.687524 11.3748C0.687524 11.0267 0.825805 10.6929 1.07195 10.4468C1.31809 10.2006 1.65193 10.0623 2.00002 10.0623Z",fill:"#374151"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2260",children:e.jsx("path",{d:"M0.6875 0H14.6875V14H0.6875V0Z",fill:"white"})})})]}),"Show Cheat Sheet"]}),e.jsxs("button",{onClick:()=>u("oldReport"),className:"flex items-center border-2 gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"14",viewBox:"0 0 12 14",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_1_2265)",children:e.jsx("path",{d:"M2.65625 12.6875C2.41562 12.6875 2.21875 12.4906 2.21875 12.25V1.75C2.21875 1.50937 2.41562 1.3125 2.65625 1.3125H7.03125V3.5C7.03125 3.98398 7.42227 4.375 7.90625 4.375H10.0938V12.25C10.0938 12.4906 9.89688 12.6875 9.65625 12.6875H2.65625ZM2.65625 0C1.69102 0 0.90625 0.784766 0.90625 1.75V12.25C0.90625 13.2152 1.69102 14 2.65625 14H9.65625C10.6215 14 11.4062 13.2152 11.4062 12.25V4.22461C11.4062 3.75977 11.223 3.31406 10.8949 2.98594L8.41758 0.511328C8.08945 0.183203 7.64648 0 7.18164 0H2.65625ZM4.1875 7C3.82383 7 3.53125 7.29258 3.53125 7.65625C3.53125 8.01992 3.82383 8.3125 4.1875 8.3125H8.125C8.48867 8.3125 8.78125 8.01992 8.78125 7.65625C8.78125 7.29258 8.48867 7 8.125 7H4.1875ZM4.1875 9.625C3.82383 9.625 3.53125 9.91758 3.53125 10.2812C3.53125 10.6449 3.82383 10.9375 4.1875 10.9375H8.125C8.48867 10.9375 8.78125 10.6449 8.78125 10.2812C8.78125 9.91758 8.48867 9.625 8.125 9.625H4.1875Z",fill:"#374151"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2265",children:e.jsx("path",{d:"M0.90625 0H11.4062V14H0.90625V0Z",fill:"white"})})})]}),"Show Old Report"]}),e.jsx("button",{onClick:z,disabled:C,className:"px-4 flex items-center gap-2 py-2 bg-gray-800 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed",children:C?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"13",height:"14",viewBox:"0 0 13 14",fill:"none",children:[e.jsx("g",{clipPath:"url(#clip0_1_2303)",children:e.jsx("path",{d:"M2.03125 2.625V11.375C2.03125 11.6156 2.22812 11.8125 2.46875 11.8125H11.2188C11.4594 11.8125 11.6562 11.6156 11.6562 11.375V4.66211C11.6562 4.54727 11.6098 4.43516 11.5277 4.35312L12.4547 3.42617C12.7828 3.7543 12.966 4.2 12.966 4.66484V11.375C12.966 12.3402 12.1812 13.125 11.216 13.125H2.46875C1.50352 13.125 0.71875 12.3402 0.71875 11.375V2.625C0.71875 1.65977 1.50352 0.875 2.46875 0.875H9.18164C9.64648 0.875 10.0922 1.0582 10.4203 1.38633L12.4574 3.42344L11.5305 4.35039L9.49062 2.31602C9.48242 2.30781 9.47695 2.30234 9.46875 2.29414V5.03125C9.46875 5.39492 9.17617 5.6875 8.8125 5.6875H3.5625C3.19883 5.6875 2.90625 5.39492 2.90625 5.03125V2.1875H2.46875C2.22812 2.1875 2.03125 2.38437 2.03125 2.625ZM4.21875 2.1875V4.375H8.15625V2.1875H4.21875ZM5.09375 8.75C5.09375 8.28587 5.27812 7.84075 5.60631 7.51256C5.9345 7.18437 6.37962 7 6.84375 7C7.30788 7 7.753 7.18437 8.08119 7.51256C8.40938 7.84075 8.59375 8.28587 8.59375 8.75C8.59375 9.21413 8.40938 9.65925 8.08119 9.98744C7.753 10.3156 7.30788 10.5 6.84375 10.5C6.37962 10.5 5.9345 10.3156 5.60631 9.98744C5.27812 9.65925 5.09375 9.21413 5.09375 8.75Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2303",children:e.jsx("path",{d:"M0.71875 0H12.9688V14H0.71875V0Z",fill:"white"})})})]}),"Save Changes"]})}),e.jsxs("button",{onClick:J,className:"px-4 flex items-center gap-2 py-2 bg-violet-600 text-white rounded-md",children:["Proceed",e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"13",height:"14",viewBox:"0 0 13 14",fill:"none",children:[e.jsx("g",{clipPath:"url(#clip0_1_2306)",children:e.jsx("path",{d:"M12.743 7.61807C13.0848 7.27627 13.0848 6.72119 12.743 6.3794L8.36797 2.00439C8.02617 1.6626 7.47109 1.6626 7.1293 2.00439C6.7875 2.34619 6.7875 2.90127 7.1293 3.24307L10.0141 6.1251H1.625C1.14102 6.1251 0.75 6.51611 0.75 7.0001C0.75 7.48408 1.14102 7.8751 1.625 7.8751H10.0113L7.13203 10.7571C6.79023 11.0989 6.79023 11.654 7.13203 11.9958C7.47383 12.3376 8.02891 12.3376 8.3707 11.9958L12.7457 7.6208L12.743 7.61807Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2306",children:e.jsx("path",{d:"M0.75 0H13V14H0.75V0Z",fill:"white"})})})]})]})]}),e.jsxs("div",{className:"flex mt-4 justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>_(a-1),disabled:a===0,className:"p-2 border rounded-full","aria-label":"Previous section",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"15",height:"16",viewBox:"0 0 15 16",fill:"none",style:{rotate:"180deg"},children:[e.jsx("g",{"clip-path":"url(#clip0_1_2279)",children:e.jsx("path",{d:"M13.7219 8.70615C14.1125 8.31553 14.1125 7.68115 13.7219 7.29053L8.72188 2.29053C8.33125 1.8999 7.69688 1.8999 7.30625 2.29053C6.91563 2.68115 6.91563 3.31553 7.30625 3.70615L10.6031 6.9999H1.01562C0.4625 6.9999 0.015625 7.44678 0.015625 7.9999C0.015625 8.55303 0.4625 8.9999 1.01562 8.9999H10.6L7.30937 12.2937C6.91875 12.6843 6.91875 13.3187 7.30937 13.7093C7.7 14.0999 8.33438 14.0999 8.725 13.7093L13.725 8.70928L13.7219 8.70615Z",fill:"#4B5563"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2279",children:e.jsx("path",{d:"M0.015625 0H14.0156V16H0.015625V0Z",fill:"white"})})})]})}),e.jsx("div",{className:"min-w-[300px] flex items-center justify-center",children:e.jsx("h1",{className:"text-xl mx-3 font-semibold",children:(R=n[a])==null?void 0:R.title})}),e.jsx("button",{onClick:()=>_(a+1),disabled:a===n.length-1,className:"p-2 border rounded-full","aria-label":"Next section",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"15",height:"16",viewBox:"0 0 15 16",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_1_2279)",children:e.jsx("path",{d:"M13.7219 8.70615C14.1125 8.31553 14.1125 7.68115 13.7219 7.29053L8.72188 2.29053C8.33125 1.8999 7.69688 1.8999 7.30625 2.29053C6.91563 2.68115 6.91563 3.31553 7.30625 3.70615L10.6031 6.9999H1.01562C0.4625 6.9999 0.015625 7.44678 0.015625 7.9999C0.015625 8.55303 0.4625 8.9999 1.01562 8.9999H10.6L7.30937 12.2937C6.91875 12.6843 6.91875 13.3187 7.30937 13.7093C7.7 14.0999 8.33438 14.0999 8.725 13.7093L13.725 8.70928L13.7219 8.70615Z",fill:"#4B5563"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_2279",children:e.jsx("path",{d:"M0.015625 0H14.0156V16H0.015625V0Z",fill:"white"})})})]})})]})]}),e.jsx(ne,{editor:g}),e.jsx("div",{className:"bg-white p-6 rounded-lg min-h-[600px]",children:e.jsx(G,{editor:g})})]}),o.isVisible&&e.jsxs("div",{className:"w-96 bg-white border-l border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h2",{className:"text-lg font-medium text-gray-900",children:[o.type==="comments"&&"Section Comments",o.type==="cheatSheet"&&"Cheat Sheet",o.type==="oldReport"&&"Previous Report"]}),e.jsx("button",{onClick:()=>f(t=>({...t,isVisible:!1})),className:"text-gray-500 hover:text-gray-700","aria-label":"Close side panel",children:"×"})]}),o.type==="comments"&&e.jsx("div",{className:"space-y-4",children:$.map(t=>e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-violet-100 rounded-full flex items-center justify-center",children:t.user.name[0]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:t.user.name}),e.jsx("div",{className:"text-sm text-gray-500",children:new Date(t.created_at).toLocaleDateString()})]})]}),e.jsx("p",{className:"text-gray-700",children:t.text})]},t.id))}),o.type==="cheatSheet"&&e.jsx("div",{className:"space-y-4",children:U?e.jsx("div",{className:"flex items-center justify-center p-4",children:e.jsx("div",{className:"w-6 h-6 border-2 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):L?e.jsxs("div",{className:"text-red-600 p-4 text-center",children:[L,e.jsx("button",{onClick:B,className:"block mx-auto mt-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:"Retry"})]}):w.length===0?e.jsx("div",{className:"text-gray-500 p-4 text-center",children:"No cheatsheet data available"}):w.map((t,s)=>e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:t.title}),t.subsections.map((i,c)=>e.jsxs("div",{className:"mb-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:i.subtitle}),e.jsx("p",{className:"text-sm text-gray-600",children:i.content})]},c))]},s))}),o.type==="oldReport"&&e.jsx("div",{className:"space-y-4",children:O?e.jsx("div",{className:"flex items-center justify-center p-4",children:e.jsx("div",{className:"w-6 h-6 border-2 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):M?e.jsxs("div",{className:"text-red-600 p-4 text-center",children:[M,e.jsx("button",{onClick:I,className:"block mx-auto mt-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200",children:"Retry"})]}):k.length===0?e.jsx("div",{className:"text-gray-500 p-4 text-center",children:"No previous report available"}):k.map((t,s)=>e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:t.title}),e.jsx("p",{className:"text-sm text-gray-600",children:t.content})]},s))})]})]}):e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsx("div",{className:"text-gray-600",children:"No sections available"})})};export{_e as default};
