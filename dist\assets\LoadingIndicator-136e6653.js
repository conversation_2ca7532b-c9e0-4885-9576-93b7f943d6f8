import{j as t}from"./@react-google-maps/api-5b2d83cc.js";import{m as s}from"./framer-motion-3274d37b.js";import"./vendor-489b60f1.js";const d={start:{transition:{staggerChildren:.2}},end:{transition:{staggerChildren:.2}}},a={start:{y:"0%"},end:{y:"100%"}},i={duration:.4,yoyo:1/0,ease:"easeIn"},p=({dotsClasses:e,size:r,style:o})=>{const n=`block w-[${r??9}px] h-[${r??9}px] bg-slate-900 rounded-md shrink-0 ${e??""}`;return t.jsxs(s.div,{variants:d,className:"flex justify-between items-center w-[40px] pb-[10px]",initial:"start",animate:"end",style:{...o},children:[t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i})]})};export{p as default};
