import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{c as h,a as i,d as j}from"./yup-5d8330af.js";import{M as r}from"./AdminLoginPage-95257026.js";import{u as b}from"./react-hook-form-7e42b371.js";import{o as g}from"./yup-fe85ba88.js";import{L as s}from"./index-95f0e460.js";import{M as f}from"./index-235b3e94.js";import"./vendor-489b60f1.js";import"./index-ec6e151a.js";import"./html2pdf.js-82514bbc.js";import"./index-c6183aa1.js";import"./@hookform/resolvers-6b9dee20.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const D=()=>{const m={support:"Support",feedback:"Feedback",partnership:"Partnership",other:"Other"},n=h().shape({name:i().required("Name is required"),email:i().email("Please enter a valid email").required("Email is required"),message:i().min(10,"Message must be at least 10 characters").required("Message is required"),subscribe:j().required().default(!1),category:i().required("Please select a category")}),{register:t,handleSubmit:c,formState:{errors:a},watch:d,setValue:u}=b({resolver:g(n),defaultValues:{name:"",email:"",message:"",subscribe:!1,category:""}}),o=d(),p=l=>{u("subscribe",l.target.checked)},x=l=>{console.log("Form submitted:",l)};return e.jsxs("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"React Hook Form + Yup Example"}),e.jsxs("form",{onSubmit:c(x),className:"space-y-6",children:[e.jsx(s,{children:e.jsx(r,{name:"name",type:"text",register:t,errors:a,required:!0,children:e.jsxs(r.Container,{children:[e.jsx(r.Label,{children:"Your Name"}),e.jsx(r.Field,{placeholder:"Enter your name"}),e.jsx(r.Error,{})]})})}),e.jsx(s,{children:e.jsx(r,{name:"email",type:"email",register:t,errors:a,required:!0,children:e.jsxs(r.Container,{children:[e.jsx(r.Label,{className:"text-blue-600",children:"Email Address"}),e.jsx(r.Field,{placeholder:"Enter your email",className:"border-blue-200 focus:border-blue-500"}),e.jsx(r.Error,{})]})})}),e.jsx(s,{children:e.jsx(r,{name:"message",type:"textarea",register:t,errors:a,required:!0,children:e.jsxs(r.Container,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(r.Label,{children:"Your Message"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Min 10 characters"})]}),e.jsx(r.Field,{rows:"4",placeholder:"Type your message here..."}),e.jsx(r.Error,{})]})})}),e.jsx(s,{children:e.jsx(r,{name:"subscribe",type:"toggle",value:o.subscribe,onChange:p,errors:a,children:e.jsxs(r.Container,{className:"flex gap-3 items-center",children:[e.jsx(r.Field,{}),e.jsx(r.Label,{children:"Subscribe to newsletter"})]})})}),e.jsx(s,{children:e.jsx(r,{name:"category",type:"mapping",register:t,errors:a,mapping:m,required:!0,children:e.jsxs(r.Container,{children:[e.jsx(r.Label,{children:"Category"}),e.jsx(r.Field,{placeholder:"Select a category"}),e.jsx(r.Error,{})]})})}),e.jsxs("div",{className:"mt-4 p-4 bg-gray-100 rounded-md",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Current Form Values:"}),e.jsx("pre",{className:"text-sm overflow-auto",children:JSON.stringify(o,null,2)})]}),e.jsx("div",{className:"pt-4",children:e.jsx(s,{children:e.jsx(f,{type:"submit",className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Submit"})})})]})]})};export{D as MkdInputV2HookFormExample,D as default};
