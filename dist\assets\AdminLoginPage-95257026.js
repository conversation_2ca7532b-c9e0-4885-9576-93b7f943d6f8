import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{c as D,a as E}from"./yup-5d8330af.js";import{r as t,u as T,d as U,L as _}from"./vendor-489b60f1.js";import{u as z}from"./react-hook-form-7e42b371.js";import{o as B}from"./yup-fe85ba88.js";import{I as G}from"./index-ec6e151a.js";import{a as Y,u as K,L,R as v,T as R}from"./index-95f0e460.js";import{M as Q}from"./index-c6183aa1.js";import{a}from"./html2pdf.js-82514bbc.js";const q="/assets/login-new-bg-b92ed446.png",H=t.lazy(()=>a(()=>import("./MkdInputV2-b8b948d2.js"),["assets/MkdInputV2-b8b948d2.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/MkdInputV2Context-f6333041.js"])),X=t.lazy(()=>a(()=>import("./MkdInputV2Label-b71a7596.js"),["assets/MkdInputV2Label-b71a7596.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/MkdInputV2Context-f6333041.js"])),J=t.lazy(()=>a(()=>import("./MkdInputV2Field-faf97389.js"),["assets/MkdInputV2Field-faf97389.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-toggle-88721710.js","assets/@uppy/dashboard-6cf0145e.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/index-95f0e460.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-d4c6ce51.css","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css","assets/index-52d51cfb.js","assets/MkdInputV2Context-f6333041.js"])),W=t.lazy(()=>a(()=>import("./MkdInputV2Error-29593761.js"),["assets/MkdInputV2Error-29593761.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/MkdInputV2Context-f6333041.js","assets/index-95f0e460.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-d4c6ce51.css","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),Z=t.lazy(()=>a(()=>import("./MkdInputV2Container-ca40774e.js"),["assets/MkdInputV2Container-ca40774e.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/MkdInputV2Context-f6333041.js"]));t.lazy(()=>a(()=>import("./MkdInputV2.example-9321e303.js"),["assets/MkdInputV2.example-9321e303.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index-95f0e460.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-d4c6ce51.css","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css","assets/index-235b3e94.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/index-ec6e151a.js","assets/index-c6183aa1.js"]));t.lazy(()=>a(()=>import("./MkdInputV2.hookform.example-c735e703.js"),["assets/MkdInputV2.hookform.example-c735e703.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/index-95f0e460.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-d4c6ce51.css","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css","assets/index-235b3e94.js","assets/index-ec6e151a.js","assets/index-c6183aa1.js"]));const o=Object.assign(H,{Container:Z,Label:X,Field:J,Error:W}),$=({role:C=v.SUPER_ADMIN})=>{const{sdk:c}=Y(),{authDispatch:S,showToast:m}=K(),[d,l]=t.useState(!1),I=T(),P=new URLSearchParams(I.search).get("redirect_uri"),M=U(),O=D({email:E().email().required(),password:E().required()}).required(),{register:u,handleSubmit:V,setError:x,formState:{errors:p}}=z({resolver:B(O)}),k=async i=>{var n,A,h,g,f,j,b,w;try{l(!0);const s=await c.login(i.email,i.password,C);if(console.log("result",s),!s.error)S({type:"LOGIN",payload:s}),m("Succesfully Logged In",4e3,R.SUCCESS),M(P??"/admin/build");else if(l(!1),s.validation){const y=Object.keys(s.validation);for(let r=0;r<y.length;r++){const N=y[r];x(N,{type:"manual",message:s.validation[N]})}}}catch(s){l(!1),m((A=(n=s==null?void 0:s.response)==null?void 0:n.data)!=null&&A.message?(g=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:g.message:s==null?void 0:s.message,4e3,R.ERROR),x("email",{type:"manual",message:(j=(f=s==null?void 0:s.response)==null?void 0:f.data)!=null&&j.message?(w=(b=s==null?void 0:s.response)==null?void 0:b.data)==null?void 0:w.message:s==null?void 0:s.message})}},F=async i=>{const n=await c.oauthLoginApi(i,v.SUPER_ADMIN);window.open(n,"_self")};return e.jsx("main",{className:"flex justify-center min-h-svh max-h-svh h-svh flex-col bg-cover bg-no-repeat items-center",style:{backgroundImage:`url(${q})`},children:e.jsxs("div",{className:"flex justify-center w-[90%] flex-col items-center rounded-lg border  border-[#a8a8a8] p-6 shadow-md md:w-[22.8125rem]",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",children:[e.jsx("path",{d:"M12.5 2C10.0147 2 8 4.01472 8 6.5C8 8.98528 10.0147 11 12.5 11C14.9853 11 17 8.98528 17 6.5C17 4.01472 14.9853 2 12.5 2Z",fill:"#4F46E5"}),e.jsx("path",{d:"M12.5004 12.5C8.3271 12.5 5.27345 15.2936 4.4402 19.0013C4.19057 20.112 5.10014 21 6.09882 21H18.902C19.9007 21 20.8102 20.112 20.5606 19.0013C19.7274 15.2936 16.6737 12.5 12.5004 12.5Z",fill:"#4F46E5"})]}),e.jsx("div",{className:"text-xl font-semibold text-[#262626]",children:"Welcome Back"}),e.jsxs("div",{className:"flex mb-5 items-center text-sm",children:[e.jsx("span",{className:"mr-1 text-[#525252]",children:"Don’t have account? "})," ",e.jsx(_,{to:"/admin/sign-up",className:"text-[#4F46E5]",children:"Sign up here"})]}),e.jsxs("form",{className:"w-full space-y-5 max-w-full",onSubmit:V(k),children:[e.jsx("div",{className:"flex flex-col text-sm",children:e.jsx(L,{children:e.jsx(o,{name:"email",type:"email",register:u,errors:p,required:!0,placeholder:"<EMAIL>",children:e.jsxs(o.Container,{children:[e.jsx(o.Label,{className:"",children:"Email Address"}),e.jsx(o.Field,{className:"border-blue-200 focus:border-blue-500"}),e.jsx(o.Error,{})]})})})}),e.jsx("div",{className:"flex flex-col text-sm",children:e.jsx(L,{children:e.jsx(Q,{required:!0,name:"password",label:"Password",errors:p,register:u})})}),e.jsxs("div",{className:"my-2 flex justify-between text-sm",children:[e.jsx("div",{className:"flex items-center text-[#525252]",children:e.jsxs("label",{className:"flex h-[1.5rem] items-center justify-center gap-3 py-1 text-black",children:[e.jsx("input",{type:"checkbox",className:"h-[1.5rem] w-[1.5rem] cursor-pointer rounded-[0.5rem]  text-[#4F46E5] accent-[#4F46E5] outline-0 focus:outline-none focus:ring-0"}),e.jsx("div",{className:"h-full cursor-pointer",children:"Remember me"})]})}),e.jsx(_,{to:"/admin/forgot",className:"text-[#4F46E5]",children:"Forgot password"})]}),e.jsx(G,{type:"submit",className:"flex w-full items-center justify-center rounded-md bg-[#4F46E5] py-2 tracking-wide text-white  outline-none focus:outline-none",loading:d,disabled:d,children:"Sign in"})]}),e.jsx("div",{className:"w-full text-[1rem] text-black text-center",children:"OR"}),e.jsx("div",{className:"oauth flex w-full flex-col gap-[2rem] text-[#344054] ",children:e.jsxs("button",{onClick:()=>F("google"),className:"my-2 flex h-[2.75rem] min-w-[70%] cursor-pointer items-center justify-center gap-3 rounded-sm border-2  px-4",children:[e.jsx("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALpSURBVHgBtVbNTxNBFH8zuy3QoN0YJMEQs8QQP05LAsbEg4uRxMSD4AeaeLB6xEPhpIkm4MF4IsG/oODF4Edajgahy8UDxbAcjDEc2IORCIlUhVK6u/OcKbVpaZdWxN+lkzd9v9+b9968WQK7YEnXlYPSxm0GqCMQjZtUYScASUSw+NJwGU40GXOGFwfxIg7IqX6KGEYABSqCWBmKPc2TCbOiwEpXhwaMRAFQhb+Ei/i4aXpuyFNAkBMG8eqiLoVIG2N2Z5NhWiUCyxfPqLLtznuTYxKQWIRk869wT60SuYD8ZyHZrGzk3NGkCP3r6Cy0GGYyH5CuqRL1DXKhkBd5/gRrfa0h+7MSKQ0aRhqnEwOwC1YvtOuO41jlyPMCzpRvKT3boKbeNRdsYOzw1FwP/COoPSnriKjWdKxCsO8j0GAmm0/HdQZgHyADhXM8FdtqnPzArUVIv280gsOWVc5BH9xUoWrUJkWRi7pBiAQufRmF4fIukt+N8Hh0qAYsNUoBSztHRtmCfQASVCn8Z1BCiLXT6DJbg32CzPhFKpwXv9AHkY3jOoA5Uc6B53+Mn90o2SBi0mKo2MS5RZvyVVwYFp0g3P95GpbdQNJJuy3mnVgSqsT5JxuRnQKMQYj6uhyDr5Pjm8fg3o+zsMwCQlqR66RIteT6082S6LNw7BlJ/EpX22ufp1r1DEiF2yeOXDupfH396W0lcopMZKCoG/llNYzB4LN8+tvHr8zz3JYUl48MPkHJ0OyNN2NFxJFuZb1W7pfSp8J1K3cV6jQU+aHk1+IP/At5Ae3FTVWm9ny5e5FT4uMasi8WL7RKcs+nALUboO5bGKStozl2GJl+VD+w7VaAjpfXNRTHxb09OP61Hqj53m3GH9a35cUL/5DofWU6zNfGI7RgD9g6FI1hxu4stJV99LVotyJnaJjXZAiqAPI6Aa/Thx118hTIC/G6UMjolJLL2Y+AXBMgr4coPmc2CMVYojc648XxG0ZrPRAMMnAhAAAAAElFTkSuQmCC",className:"h-[18px] w-[18px]"}),e.jsx("span",{className:"text-[1rem] text-[#525252] font-[600]",children:"Sign in With Google"})]})})]})})},me=Object.freeze(Object.defineProperty({__proto__:null,default:$},Symbol.toStringTag,{value:"Module"}));export{me as A,o as M};
