import fs from "fs";
import path from "path";
import { program } from "commander";

program
  .option("-n, --name <name>", "Page name")
  .option("-t, --type <type>", "Page type (add/edit/list/view)")
  .option("-p, --portal <portal>", "Portal (admin/user)")
  .parse(process.argv);

const options = program.opts();

if (!options.name || !options.type || !options.portal) {
  console.error("Please provide all required options: name, type, and portal");
  process.exit(1);
}

const { name, type, portal } = options;

// Validate inputs
const validTypes = ["add", "edit", "list", "view"];
const validPortals = ["admin", "user"];

if (!validTypes.includes(type.toLowerCase())) {
  console.error(`Invalid type. Must be one of: ${validTypes.join(", ")}`);
  process.exit(1);
}

if (!validPortals.includes(portal.toLowerCase())) {
  console.error(`Invalid portal. Must be one of: ${validPortals.join(", ")}`);
  process.exit(1);
}

// Create page directory
const pageDir = path.join(
  process.cwd(),
  "src",
  "pages",
  portal.charAt(0).toUpperCase() + portal.slice(1),
  type.charAt(0).toUpperCase() + type.slice(1),
  `${name}Page`
);

if (!fs.existsSync(pageDir)) {
  fs.mkdirSync(pageDir, { recursive: true });
}

// Create page component
const componentTemplate = `import React from 'react';
import { useContexts } from '@/hooks/useContexts';
import { I${name}PageProps } from './types';

const ${name}Page: React.FC<I${name}PageProps> = () => {
  const { authState } = useContexts();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">${name}</h1>
      {/* Add your page content here */}
    </div>
  );
};

export default ${name}Page;
`;

// Create types file
const typesTemplate = `export interface I${name}PageProps {
  // Add your props here
}

export interface I${name}Data {
  // Add your data interface here
}
`;

// Create utils file
const utilsTemplate = `import { I${name}Data } from './types';

export const format${name}Data = (data: I${name}Data) => {
  // Add your utility functions here
  return data;
};
`;

// Create constants file
const constantsTemplate = `export const ${name.toUpperCase()}_CONSTANTS = {
  // Add your constants here
};
`;

// Write files
fs.writeFileSync(path.join(pageDir, "index.tsx"), componentTemplate);
fs.writeFileSync(path.join(pageDir, "types.ts"), typesTemplate);
fs.writeFileSync(path.join(pageDir, "utils.ts"), utilsTemplate);
fs.writeFileSync(path.join(pageDir, "constants.ts"), constantsTemplate);

console.log(`✅ Successfully created ${name}Page in ${pageDir}`);
console.log("Remember to:");
console.log("1. Add the route to your routing configuration");
console.log("2. Add the page to LazyLoad.ts");
console.log("3. Update navigation if needed");
