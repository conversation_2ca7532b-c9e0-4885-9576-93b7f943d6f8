import{j as r}from"./@react-google-maps/api-5b2d83cc.js";import{M as a}from"./index-235b3e94.js";import{L as e}from"./index-95f0e460.js";import{C as s}from"./react-modern-calendar-datepicker-d679796a.js";import"./vendor-489b60f1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const P=({selectedDay:o,setSelectedDay:t,showDate:i=!0,onSave:m})=>r.jsxs(r.Fragment,{children:[r.jsx("style",{children:`.custom-selected-date {
          background-color: #000 !important;
          color: #fff !important;
          border-radius: 50% !important;
        }`}),r.jsxs("div",{children:[i&&r.jsx(s,{value:o,onChange:t}),r.jsx(e,{children:r.jsx(a,{className:"!h-full !w-full ",showPlus:!1,onClick:m,children:"Save"})})]})]});export{P as default};
