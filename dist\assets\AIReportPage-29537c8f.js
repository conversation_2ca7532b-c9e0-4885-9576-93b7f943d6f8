import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as f,R as k,d as A}from"./vendor-489b60f1.js";import{D as V,C as R,P as E}from"./react-beautiful-dnd-047ecf8c.js";import{b as y}from"./index-95f0e460.js";import{D as _}from"./deanna_logo-c6d631d7.js";import"./@mantine/core-d9c5c65c.js";import"./redux-3b07d581.js";import"./react-select-8c03feb0.js";import"./html2pdf.js-82514bbc.js";import"./@craftjs/core-ae02137e.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const L=({open:r,onClose:o,onBeginReview:p,sections:t})=>{const[m,l]=f.useState(t);k.useEffect(()=>{l(t)},[t]);const i=s=>{if(!s.destination)return;const d=Array.from(m),[a]=d.splice(s.source.index,1);d.splice(s.destination.index,0,a),l(d.map((x,w)=>({...x,order:w+1})))};return r?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md p-6 flex flex-col gap-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl text-gray-800 font-semibold",children:"Review Order"}),e.jsx("button",{className:"p-1","aria-label":"Close",onClick:o,children:e.jsx("svg",{width:12,height:16,viewBox:"0 0 12 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z",fill:"#4B5563"})})})]}),e.jsx("div",{className:"text-gray-600",children:"Arrange sections in order of review priority. Drag to reorder."}),e.jsx(V,{onDragEnd:i,children:e.jsx(R,{droppableId:"sections",children:s=>e.jsxs("div",{...s.droppableProps,ref:s.innerRef,className:"flex flex-col gap-2 max-h-[300px] overflow-y-auto",children:[m.map((d,a)=>e.jsx(E,{draggableId:d.id,index:a,children:x=>e.jsxs("div",{ref:x.innerRef,...x.draggableProps,className:"flex items-center gap-3 p-3 w-full h-12 rounded-md bg-gray-50",children:[e.jsx("div",{...x.dragHandleProps,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"10",height:"14",viewBox:"0 0 10 14",fill:"none",children:e.jsx("path",{d:"M1.25 10H2.75C3.44062 10 4 10.5594 4 11.25V12.75C4 13.4406 3.44062 14 2.75 14H1.25C0.559375 14 0 13.4406 0 12.75V11.25C0 10.5594 0.559375 10 1.25 10ZM7.25 10H8.75C9.44063 10 10 10.5594 10 11.25V12.75C10 13.4406 9.44063 14 8.75 14H7.25C6.55937 14 6 13.4406 6 12.75V11.25C6 10.5594 6.55937 10 7.25 10ZM1.25 9C0.559375 9 0 8.44063 0 7.75V6.25C0 5.55937 0.559375 5 1.25 5H2.75C3.44062 5 4 5.55937 4 6.25V7.75C4 8.44063 3.44062 9 2.75 9H1.25ZM7.25 5H8.75C9.44063 5 10 5.55937 10 6.25V7.75C10 8.44063 9.44063 9 8.75 9H7.25C6.55937 9 6 8.44063 6 7.75V6.25C6 5.55937 6.55937 5 7.25 5ZM1.25 4C0.559375 4 0 3.44062 0 2.75V1.25C0 0.559375 0.559375 0 1.25 0H2.75C3.44062 0 4 0.559375 4 1.25V2.75C4 3.44062 3.44062 4 2.75 4H1.25ZM7.25 0H8.75C9.44063 0 10 0.559375 10 1.25V2.75C10 3.44062 9.44063 4 8.75 4H7.25C6.55937 4 6 3.44062 6 2.75V1.25C6 0.559375 6.55937 0 7.25 0Z",fill:"#9CA3AF"})})}),e.jsxs("span",{className:"text-gray-800",children:[a+1,". ",d.title]}),e.jsx("svg",{className:"ml-auto",width:10,height:16,viewBox:"0 0 10 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.70615 0.293945C5.31553 -0.0966797 4.68115 -0.0966797 4.29053 0.293945L1.29053 3.29395C0.899902 3.68457 0.899902 4.31895 1.29053 4.70957C1.68115 5.1002 2.31553 5.1002 2.70615 4.70957L3.9999 3.41582V12.5846L2.70615 11.2939C2.31553 10.9033 1.68115 10.9033 1.29053 11.2939C0.899902 11.6846 0.899902 12.3189 1.29053 12.7096L4.29053 15.7096C4.68115 16.1002 5.31553 16.1002 5.70615 15.7096L8.70615 12.7096C9.09678 12.3189 9.09678 11.6846 8.70615 11.2939C8.31553 10.9033 7.68115 10.9033 7.29053 11.2939L5.9999 12.5846V3.41582L7.29365 4.70957C7.68428 5.1002 8.31865 5.1002 8.70928 4.70957C9.0999 4.31895 9.0999 3.68457 8.70928 3.29395L5.70928 0.293945H5.70615Z",fill:"#9CA3AF"})})]})},d.id)),s.placeholder]})})}),e.jsxs("div",{className:"flex justify-end gap-3 mt-4",children:[e.jsx("button",{className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-md",onClick:o,children:"Cancel"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 bg-gray-800 text-white rounded-md",onClick:()=>p(m),children:[e.jsx("svg",{width:13,height:16,viewBox:"0 0 13 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.39062 0C5.08437 0 3.97187 0.834375 3.5625 2H2.39062C1.2875 2 0.390625 2.89687 0.390625 4V14C0.390625 15.1031 1.2875 16 2.39062 16H10.3906C11.4937 16 12.3906 15.1031 12.3906 14V4C12.3906 2.89687 11.4937 2 10.3906 2H9.21875C8.80937 0.834375 7.69688 0 6.39062 0Z",fill:"white"})}),"Begin Review"]})]}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"This is for editing purposes only and won't be the final report."})]})}):null},S=({jobStatus:r})=>{const o=[{status:"complete",label:"Document preprocessing complete"},{status:"complete",label:"Test identification complete"},{status:(r==null?void 0:r.status)===1?"in-progress":(r==null?void 0:r.status)===3?"complete":"pending",label:"Generating comprehensive report"}],p=l=>l==="complete"?e.jsxs("svg",{width:17,height:16,viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[e.jsx("g",{clipPath:"url(#clip0_1_538)",children:e.jsx("path",{d:"M8.76562 16C10.8874 16 12.9222 15.1571 14.4225 13.6569C15.9228 12.1566 16.7656 10.1217 16.7656 8C16.7656 5.87827 15.9228 3.84344 14.4225 2.34315C12.9222 0.842855 10.8874 0 8.76562 0C6.64389 0 4.60906 0.842855 3.10877 2.34315C1.60848 3.84344 0.765625 5.87827 0.765625 8C0.765625 10.1217 1.60848 12.1566 3.10877 13.6569C4.60906 15.1571 6.64389 16 8.76562 16ZM12.2969 6.53125L8.29688 10.5312C8.00313 10.825 7.52813 10.825 7.2375 10.5312L5.2375 8.53125C4.94375 8.2375 4.94375 7.7625 5.2375 7.47188C5.53125 7.18125 6.00625 7.17813 6.29688 7.47188L7.76562 8.94063L11.2344 5.46875C11.5281 5.175 12.0031 5.175 12.2937 5.46875C12.5844 5.7625 12.5875 6.2375 12.2937 6.52812L12.2969 6.53125Z",fill:"#059669"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_538",children:e.jsx("path",{d:"M0.765625 0H16.7656V16H0.765625V0Z",fill:"white"})})})]}):l==="in-progress"?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"21",viewBox:"0 0 23 24",fill:"none",children:e.jsx("path",{d:"M13.8406 3.00773C13.8406 2.45737 13.622 1.92956 13.2328 1.5404C12.8437 1.15124 12.3159 0.932617 11.7655 0.932617C11.2151 0.932617 10.6873 1.15124 10.2982 1.5404C9.90902 1.92956 9.69039 2.45737 9.69039 3.00773C9.69039 3.55808 9.90902 4.0859 10.2982 4.47505C10.6873 4.86421 11.2151 5.08284 11.7655 5.08284C12.3159 5.08284 12.8437 4.86421 13.2328 4.47505C13.622 4.0859 13.8406 3.55808 13.8406 3.00773ZM13.8406 20.992C13.8406 20.4417 13.622 19.9139 13.2328 19.5247C12.8437 19.1355 12.3159 18.9169 11.7655 18.9169C11.2151 18.9169 10.6873 19.1355 10.2982 19.5247C9.90902 19.9139 9.69039 20.4417 9.69039 20.992C9.69039 21.5424 9.90902 22.0702 10.2982 22.4594C10.6873 22.8485 11.2151 23.0671 11.7655 23.0671C12.3159 23.0671 12.8437 22.8485 13.2328 22.4594C13.622 22.0702 13.8406 21.5424 13.8406 20.992ZM2.77335 14.075C3.32371 14.075 3.85152 13.8564 4.24068 13.4672C4.62984 13.078 4.84846 12.5502 4.84846 11.9999C4.84846 11.4495 4.62984 10.9217 4.24068 10.5326C3.85152 10.1434 3.32371 9.92477 2.77335 9.92477C2.223 9.92477 1.69519 10.1434 1.30603 10.5326C0.916869 10.9217 0.698242 11.4495 0.698242 11.9999C0.698242 12.5502 0.916869 13.078 1.30603 13.4672C1.69519 13.8564 2.223 14.075 2.77335 14.075ZM22.8328 11.9999C22.8328 11.4495 22.6141 10.9217 22.225 10.5326C21.8358 10.1434 21.308 9.92477 20.7577 9.92477C20.2073 9.92477 19.6795 10.1434 19.2903 10.5326C18.9012 10.9217 18.6825 11.4495 18.6825 11.9999C18.6825 12.5502 18.9012 13.078 19.2903 13.4672C19.6795 13.8564 20.2073 14.075 20.7577 14.075C21.308 14.075 21.8358 13.8564 22.225 13.4672C22.6141 13.078 22.8328 12.5502 22.8328 11.9999ZM6.87602 19.8248C7.06876 19.632 7.22166 19.4032 7.32597 19.1514C7.43028 18.8996 7.48397 18.6296 7.48397 18.3571C7.48397 18.0845 7.43028 17.8146 7.32597 17.5627C7.22166 17.3109 7.06876 17.0821 6.87602 16.8894C6.68328 16.6966 6.45446 16.5437 6.20263 16.4394C5.9508 16.3351 5.68089 16.2814 5.40831 16.2814C4.85781 16.2814 4.32986 16.5001 3.9406 16.8894C3.55134 17.2786 3.33266 17.8066 3.33266 18.3571C3.33266 18.9076 3.55134 19.4355 3.9406 19.8248C4.32986 20.214 4.85781 20.4327 5.40831 20.4327C5.95881 20.4327 6.48676 20.214 6.87602 19.8248ZM6.87602 7.10607C7.07834 6.91558 7.24034 6.68638 7.3524 6.43209C7.46446 6.17779 7.5243 5.90358 7.52838 5.62573C7.53246 5.34787 7.48068 5.07202 7.37613 4.81455C7.27158 4.55708 7.11638 4.32323 6.91974 4.12687C6.72309 3.93052 6.48902 3.77567 6.23139 3.67149C5.97377 3.56732 5.69784 3.51595 5.41999 3.52044C5.14214 3.52493 4.86801 3.58517 4.61389 3.69761C4.35976 3.81005 4.1308 3.97238 3.9406 4.17498C3.55134 4.56424 3.33266 5.09219 3.33266 5.64269C3.33266 6.19319 3.55134 6.72114 3.9406 7.1104C4.32986 7.49966 4.85781 7.71834 5.40831 7.71834C5.95881 7.71834 6.48676 7.49966 6.87602 7.1104V7.10607ZM16.655 19.8248C16.8477 20.0175 17.0765 20.1704 17.3284 20.2747C17.5802 20.379 17.8501 20.4327 18.1227 20.4327C18.3953 20.4327 18.6652 20.379 18.917 20.2747C19.1688 20.1704 19.3977 20.0175 19.5904 19.8248C19.7831 19.632 19.936 19.4032 20.0403 19.1514C20.1447 18.8996 20.1983 18.6296 20.1983 18.3571C20.1983 18.0845 20.1447 17.8146 20.0403 17.5627C19.936 17.3109 19.7831 17.0821 19.5904 16.8894C19.3977 16.6966 19.1688 16.5437 18.917 16.4394C18.6652 16.3351 18.3953 16.2814 18.1227 16.2814C17.8501 16.2814 17.5802 16.3351 17.3284 16.4394C17.0765 16.5437 16.8477 16.6966 16.655 16.8894C16.4622 17.0821 16.3094 17.3109 16.205 17.5627C16.1007 17.8146 16.047 18.0845 16.047 18.3571C16.047 18.6296 16.1007 18.8996 16.205 19.1514C16.3094 19.4032 16.4622 19.632 16.655 19.8248Z",fill:"#1F2937"})}):null,m=(l=>{if(!l)return 25;switch(l){case 0:return 10;case 1:return 60;case 2:return 100;case 3:return 100;default:return 25}})(r==null?void 0:r.status);return e.jsx("div",{className:"flex flex-col flex-shrink-0 items-center pb-[404px] w-full h-full border-0 border-gray-200 bg-gray-50",children:e.jsx("div",{className:"flex flex-shrink-0 justify-center items-center p-6 w-full  border-0 border-gray-200 bg-black/0",children:e.jsx("div",{className:"flex flex-col flex-shrink-0 justify-center items-start gap-6  border-0 border-gray-200 bg-black/0",children:e.jsx("div",{className:"flex flex-shrink-0 justify-center items-center p-6 min-w-[1136px] h-[900px] rounded-lg border-0 border-gray-200 bg-white",children:e.jsxs("div",{className:"flex flex-col items-center gap-8 w-[1088px] h-[852px] border-0 border-gray-200 bg-black/0",children:[e.jsx("div",{className:"w-[10.0625rem] h-[10.0625rem] rounded-full border-b-4 border-b-[#1f2937] animate-spin"}),e.jsxs("div",{className:"flex flex-col items-center gap-4 w-[448px] h-[7.5rem] border-0 border-gray-200 bg-black/0",children:[e.jsx("div",{className:"flex-shrink-0 w-[16.8125rem] h-8 text-gray-800 text-center font-['Inter'] text-2xl font-semibold leading-8",children:"Processing Documents"}),e.jsx("div",{className:"flex-shrink-0 w-[430px] h-[4.5rem] text-gray-600 text-center font-['Inter'] leading-6",children:"Our AI is analyzing the uploaded documents and generating a comprehensive report. This may take a few minutes."})]}),e.jsx("div",{className:"w-full max-w-[600px] h-2 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gray-800 rounded-full transition-all duration-500 ease-out",style:{width:`${m}%`}})}),e.jsxs("div",{className:"flex flex-col items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2 opacity-50",children:[e.jsxs("svg",{width:17,height:16,viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[e.jsx("g",{clipPath:"url(#clip0_1_526)",children:e.jsx("path",{d:"M5.95312 0.75C5.95312 0.334375 5.61875 0 5.20312 0C4.7875 0 4.45312 0.334375 4.45312 0.75V2C3.35 2 2.45312 2.89687 2.45312 4H1.20312C0.7875 4 0.453125 4.33437 0.453125 4.75C0.453125 5.16563 0.7875 5.5 1.20312 5.5H2.45312V7.25H1.20312C0.7875 7.25 0.453125 7.58437 0.453125 8C0.453125 8.41562 0.7875 8.75 1.20312 8.75H2.45312V10.5H1.20312C0.7875 10.5 0.453125 10.8344 0.453125 11.25C0.453125 11.6656 0.7875 12 1.20312 12H2.45312C2.45312 13.1031 3.35 14 4.45312 14V15.25C4.45312 15.6656 4.7875 16 5.20312 16C5.61875 16 5.95312 15.6656 5.95312 15.25V14H7.70312V15.25C7.70312 15.6656 8.0375 16 8.45312 16C8.86875 16 9.20312 15.6656 9.20312 15.25V14H10.9531V15.25C10.9531 15.6656 11.2875 16 11.7031 16C12.1187 16 12.4531 15.6656 12.4531 15.25V14C13.5562 14 14.4531 13.1031 14.4531 12H15.7031C16.1187 12 16.4531 11.6656 16.4531 11.25C16.4531 10.8344 16.1187 10.5 15.7031 10.5H14.4531V8.75H15.7031C16.1187 8.75 16.4531 8.41562 16.4531 8C16.4531 7.58437 16.1187 7.25 15.7031 7.25H14.4531V5.5H15.7031C16.1187 5.5 16.4531 5.16563 16.4531 4.75C16.4531 4.33437 16.1187 4 15.7031 4H14.4531C14.4531 2.89687 13.5562 2 12.4531 2V0.75C12.4531 0.334375 12.1187 0 11.7031 0C11.2875 0 10.9531 0.334375 10.9531 0.75V2H9.20312V0.75C9.20312 0.334375 8.86875 0 8.45312 0C8.0375 0 7.70312 0.334375 7.70312 0.75V2H5.95312V0.75Z",fill:"#4B5563"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_526",children:e.jsx("path",{d:"M0.453125 0H16.4531V16H0.453125V0Z",fill:"white"})})})]}),e.jsx("span",{className:"text-gray-600 font-['Inter'] leading-6",children:"AI Analysis in Progress"})]}),e.jsxs("div",{className:"text-gray-500 font-['Inter'] text-sm",children:["Estimated time remaining:"," ",(r==null?void 0:r.status)===0?"3-5 minutes":(r==null?void 0:r.status)===1?"1-2 minutes":"Almost done"]})]}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"text-center text-gray-500 font-['Inter'] text-sm",children:"Processing steps:"}),e.jsx("div",{className:"flex flex-col gap-2",children:o.map((l,i)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex justify-center items-center h-4",children:p(l.status)}),e.jsx("div",{className:`font-['Inter'] leading-6 ${l.status==="complete"?"text-emerald-600":"text-gray-800"}`,children:l.label})]},i))})]})]})})})})})};function M(r){if(typeof window>"u"||!r)return{};const p=new window.DOMParser().parseFromString(r,"text/html"),t=m=>{var d;const i=Array.from(p.querySelectorAll("tr")).find(a=>{var w;const x=a.querySelectorAll("td");return x.length>=2?(w=x[0].textContent)==null?void 0:w.replace(/\s/g,"").toLowerCase().includes(m.replace(/\s/g,"").toLowerCase()):!1});if(!i)return"";const s=i.querySelectorAll("td");return s.length>=2&&((d=s[1].textContent)==null?void 0:d.trim())||""};return{name:t("Name"),dob:t("D.O.B"),parents:t("Parents"),address:t("Address"),phone:t("Phone"),school:t("School"),schoolBoard:t("School Board"),gradeProgram:t("Grade/Program"),assessmentBy:t("Assessment by"),assessmentDates:t("Assessment Dates"),ageAtTesting:t("Age at testing")}}const O=({onProceedReview:r,sections:o,isLoading:p,error:t})=>{const l=o?(a=>a.find(x=>x.title.toUpperCase().includes("PSYCHOLOGICAL REPORT"))||null)(o):null,i=l?M(l.body):{},s=o?o.filter(a=>!a.title.toUpperCase().includes("PSYCHOLOGICAL REPORT")):[],d=({title:a,content:x})=>e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"section-title font-bold !text-lg mb-4 text-gray-800",children:a}),e.jsx("div",{className:"custom mb-0 [&_table]:w-full [&_table]:border-collapse [&_table]:mb-4 [&_th]:border [&_th]:border-gray-200 [&_th]:p-2 [&_th]:text-left [&_th]:bg-gray-50 [&_th]:font-semibold [&_td]:border [&_td]:border-gray-200 [&_td]:p-2 [&_td]:text-left",dangerouslySetInnerHTML:{__html:x}})]});return p?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"})}):t?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-red-600",children:["Error loading report: ",t]})}):!o||o.length===0?e.jsx("div",{className:"flex min-h-screen items-center justify-center h-full",children:e.jsx("div",{className:"text-gray-600",children:"No report data available"})}):e.jsx("div",{className:"flex flex-col flex-shrink-0 items-center pb-[396px] border-0 border-gray-200",children:e.jsx("div",{className:"flex flex-shrink-0 justify-center items-center p-6 border-0 border-gray-200 bg-black/0",children:e.jsxs("div",{className:"flex flex-col flex-shrink-0 justify-center items-start gap-6 border-0 border-gray-200 bg-black/0",children:[e.jsxs("header",{className:"flex flex-shrink-0 justify-between items-center p-4 w-full h-[4.5rem] rounded-lg border-0 border-gray-200 bg-white",children:[e.jsx("h1",{className:"flex justify-center items-center pr-[0.109px] w-[14.5625rem] h-8 text-gray-800 font-['Inter'] text-2xl leading-[normal]",children:"AI Generated Report"}),e.jsxs("button",{type:"button",className:"flex items-center gap-2 pb-[0.4375rem] pr-[1.125rem] pt-2 pl-4 h-10 rounded-md border-0 border-gray-200 bg-gray-800 text-white",onClick:r,children:[e.jsx("svg",{width:13,height:16,viewBox:"0 0 13 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:e.jsx("g",{clipPath:"url(#clip0_1_619)",children:e.jsx("path",{d:"M6.875 0C5.56875 0 4.45625 0.834375 4.04688 2H2.875C1.77188 2 0.875 2.89687 0.875 4V14C0.875 15.1031 1.77188 16 2.875 16H10.875C11.9781 16 12.875 15.1031 12.875 14V4C12.875 2.89687 11.9781 2 10.875 2H9.70312C9.29375 0.834375 8.18125 0 6.875 0Z",fill:"white"})})}),"Proceed to Psychometrist Review"]})]}),e.jsx("main",{className:"flex flex-shrink-0 justify-center items-center p-6 rounded-lg border-0 border-gray-200 bg-white overflow-y-auto w-full",children:e.jsx("div",{className:"w-full max-w-[1200px] border-0 border-gray-200 bg-black/0",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex border-b border-gray-300 p-5",children:[e.jsx("div",{className:"w-32",children:e.jsx("div",{className:"w-24 h-16 bg-gray-300 flex items-center justify-center text-xs text-gray-600 rounded",children:e.jsx("img",{src:_,alt:"logo"})})}),e.jsxs("div",{className:"flex-1 text-right",children:[e.jsx("h1",{className:"text-[#1ca3a3] m-0 text-3xl font-bold",children:"Gilmour Psychological Services®"}),e.jsx("div",{className:"text-gray-700",children:"421 Gilmour Street, Ottawa, ON K2P 0R5"}),e.jsx("div",{className:"text-gray-700",children:"Tel: ************   Fax: ************"}),e.jsx("div",{className:"text-gray-700",children:e.jsx("a",{href:"http://www.ottawa-psychologists.com",className:"text-blue-600 hover:underline",children:"www.ottawa-psychologists.com"})})]})]}),e.jsxs("div",{className:"flex",children:[e.jsxs("div",{className:"w-56 border-r border-gray-300 p-5 bg-gray-50",children:[e.jsx("div",{className:"font-bold text-gray-800 mb-3",children:"Psychologists"}),e.jsxs("ul",{className:"m-0 p-0 list-none",children:[e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Iris Jackson   Ext. 124"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Karen Davies   Ext. 126"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Alex Weinberger   Ext. 136"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Qadeer Ahmad   Ext. 129"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Peter Judge   Ext. 132"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Deanna Drahovzal   Ext. 146"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Sarah Pantin   Ext. 150"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Marc Zahradnik   Ext. 142"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Caroline Ostiguy   Ext. 140"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Jessica Henry   Ext. 155"}),e.jsx("li",{className:"mb-2 text-sm",children:"Dr. Delyana Miller   Ext. 143"})]})]}),e.jsxs("div",{className:"flex-1 p-8",children:[e.jsx("div",{className:"text-center text-xl font-bold mt-0 mb-2",children:"PSYCHOLOGICAL REPORT"}),e.jsx("div",{className:"text-center font-bold mb-6",children:"(Private and Confidential)"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"Name:"}),e.jsx("span",{className:"text-gray-700",children:i.name||"ClientName ClientLastName"})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"D.O.B.:"}),e.jsx("span",{className:"text-gray-700",children:i.dob||"January 25, 1968"})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"Address:"}),e.jsx("span",{className:"text-gray-700",children:i.address||"x Ottawa, ON Kx"})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"Telephone:"}),e.jsx("span",{className:"text-gray-700",children:i.phone||"613-x"})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"Assessment by:"}),e.jsx("span",{className:"text-gray-700",children:i.assessmentBy||"Dr. Deanna Drahovzal (Psychologist), Ms. Ex (Psychometrist)"})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"Assessment dates:"}),e.jsx("div",{className:"text-gray-700 ml-40",children:i.assessmentDates||e.jsxs(e.Fragment,{children:["April 5, 2022 (intake interview)",e.jsx("br",{}),"July 12 and 19, 2022 (testing)",e.jsx("br",{}),"October 27, 2022 (clinical interview)"]})})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"font-bold inline-block w-40",children:"Age at testing:"}),e.jsx("span",{className:"text-gray-700",children:i.ageAtTesting||"59 years and x months"})]})]}),s.length>0&&e.jsx(d,{title:s[0].title,content:s[0].body},s[0].id)]})]}),s.length>1&&s.slice(1).map(a=>e.jsx("div",{className:"",style:{pageBreakBefore:"always"},children:e.jsx(d,{title:a.title,content:a.body})},a.id))]})})})]})})})},ce=()=>{const[r,o]=f.useState(!1),[p,t]=f.useState(!1),[m,l]=f.useState(null),[i,s]=f.useState(!0),[d,a]=f.useState(null),[x,w]=f.useState(null),C=f.useRef(null),N=A(),v=async()=>{try{const n=localStorage.getItem("patientId");if(!n)return;const g=await new y().getDSJobStatus(n);if(!g||g.error)return;w(g.data),g.data.status===3&&(C.current&&(clearInterval(C.current),C.current=null),await I())}catch(n){console.error("Error polling job status:",n)}},I=async()=>{try{const n=localStorage.getItem("projectId"),c=localStorage.getItem("user"),g=localStorage.getItem("patientId");if(!n||!c||!g)throw new Error("Missing required parameters");const b=new y,u=await b.fetchDSReport({project_id:n,user_id:c,patient_id:g});if(localStorage.setItem("reportId",u.data.report_id),u.error)throw new Error(u.message||"Failed to fetch report data");const h=await b.getReportSections(u.data.report_id);if(h.error)throw new Error(h.message||"Failed to fetch report sections");if(!h.sections||!Array.isArray(h.sections))throw new Error("Invalid sections response format");l(h.sections),o(!0),a(null),s(!1)}catch(n){a(n instanceof Error?n.message:"An error occurred while fetching the report"),l(null),s(!1)}},D=()=>t(!0),H=()=>t(!1),P=async n=>{try{const c=localStorage.getItem("reportId");if(!c)throw new Error("No report ID found");const b=n.map(j=>({id:parseInt(j.id),title:j.title,order:j.order,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),body:"",report_id:c})).map(j=>j.id.toString()),h=await new y().updateReportSectionOrder({report_id:c,order:b});if(h.error)throw new Error(h.message||"Failed to update section order");t(!1),N("/member/document-section-editor")}catch(c){console.error("Error updating section order:",c)}};return f.useEffect(()=>((async()=>{try{let c=localStorage.getItem("reportId");if(c!=="null"&&c){const h=await new y().getReportSections(c);if(h.error)throw new Error(h.message||"Failed to fetch report sections");l(h.sections),o(!0),s(!1);return}const g=localStorage.getItem("patientId"),b=localStorage.getItem("projectId");if(g){await v();const u=setInterval(v,2e4);C.current=u;return}}catch(c){a(c instanceof Error?c.message:"An error occurred while initializing the page"),l(null),s(!1)}})(),()=>{C.current&&(clearInterval(C.current),C.current=null)}),[]),r?e.jsxs(e.Fragment,{children:[e.jsx(O,{onProceedReview:D,sections:m,isLoading:i,error:d}),e.jsx(L,{open:p,onClose:H,onBeginReview:P,sections:(m==null?void 0:m.map(n=>({id:n.id.toString(),title:n.title,order:n.order||0})))||[]})]}):e.jsx(S,{jobStatus:x})};export{ce as default};
