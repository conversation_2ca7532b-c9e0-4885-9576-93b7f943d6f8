import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{d as D,r}from"./vendor-489b60f1.js";import{u as E,E as P}from"./@tiptap/react-8d1cdd44.js";import{T as M}from"./@tiptap/extension-table-a2bc9598.js";import{T as R}from"./@tiptap/extension-table-row-159ab625.js";import{T as $}from"./@tiptap/extension-table-cell-b57d510e.js";import{T as U}from"./@tiptap/extension-table-header-412f6f9c.js";import{S as O}from"./@tiptap/starter-kit-8dc7719e.js";import{T as W}from"./@tiptap/extension-text-align-4d325d88.js";import{U as _}from"./@tiptap/extension-underline-5fc56973.js";import{u as H,b as N,s as z,T as F}from"./index-95f0e460.js";import"./@tiptap/extension-highlight-1b3a19b9.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const J=({isOpen:t,onClose:x,onSave:c,projectName:d,setProjectName:h,isSaving:y,doctors:w,selectedDoctor:g,setSelectedDoctor:u})=>t?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50",children:e.jsxs("div",{className:"flex flex-col flex-shrink-0 justify-center items-start gap-6 p-6 w-[400px] h-[19.875rem] rounded-lg border-0 border-gray-200 bg-white",children:[e.jsxs("div",{className:"flex justify-between w-full",children:[e.jsx("div",{className:"flex-shrink-0 text-gray-800 font-['Inter'] text-xl leading-7",children:"Save Project"}),e.jsx("button",{onClick:x,className:"flex items-center justify-center w-8 h-8",children:e.jsx("svg",{width:12,height:16,viewBox:"0 0 12 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z",fill:"#4B5563"})})})]}),e.jsxs("div",{className:"flex flex-col gap-4 w-full",children:[e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("label",{className:"text-gray-600 font-['Inter'] text-sm",children:"Project Name"}),e.jsx("input",{type:"text",value:d,onChange:l=>h(l.target.value),className:"px-3 py-2 rounded-md border border-gray-300 w-full"})]}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("label",{className:"text-gray-600 font-['Inter'] text-sm",children:"Assign to:"}),e.jsxs("select",{value:g,onChange:l=>u(l.target.value),className:"px-3 py-2 rounded-md border border-gray-300 w-full",children:[e.jsx("option",{value:"",children:"Select a Doctor"}),w.map(l=>e.jsx("option",{value:l.id,children:l.name},l.id))]})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx("button",{onClick:x,className:"px-4 py-2 text-gray-600 font-['Inter']",children:"Cancel"}),e.jsx("button",{disabled:y,onClick:()=>{c(d,g)},className:"px-4 py-2 bg-gray-800 text-white rounded-md font-['Inter'] disabled:opacity-50",children:"Save and Assign"})]})]})]})}):null,K=({editor:t})=>{var x,c;return r.useState(!1),r.useState(!1),t?e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex items-center gap-2 border-b px-2 py-2 bg-white",children:[e.jsx("button",{type:"button","aria-label":"Bold","aria-pressed":t.isActive("bold"),className:`px-2 py-1 rounded ${t.isActive("bold")?"font-bold text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBold().run(),children:e.jsx("b",{children:"B"})}),e.jsx("button",{type:"button","aria-label":"Italic","aria-pressed":t.isActive("italic"),className:`px-2 py-1 rounded ${t.isActive("italic")?"italic text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleItalic().run(),children:e.jsx("i",{children:"I"})}),e.jsx("button",{type:"button","aria-label":"Underline","aria-pressed":t.isActive("underline"),className:`px-2 py-1 rounded ${t.isActive("underline")?"underline text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>{var d,h;return(h=(d=t.chain().focus()).toggleUnderline)==null?void 0:h.call(d).run()},disabled:!((c=(x=t.can()).toggleUnderline)!=null&&c.call(x)),children:e.jsx("u",{children:"U"})}),e.jsx("button",{type:"button","aria-label":"Bulleted List","aria-pressed":t.isActive("bulletList"),className:`px-2 py-1 rounded ${t.isActive("bulletList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleBulletList().run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("circle",{cx:"4",cy:"5",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"9",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("circle",{cx:"4",cy:"13",r:"1.5",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"12",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Numbered List","aria-pressed":t.isActive("orderedList"),className:`px-2 py-1 rounded ${t.isActive("orderedList")?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().toggleOrderedList().run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("text",{x:"2",y:"7",fontSize:"6",fill:"currentColor",children:"1."}),e.jsx("rect",{x:"7",y:"4",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("text",{x:"2",y:"13",fontSize:"6",fill:"currentColor",children:"2."}),e.jsx("rect",{x:"7",y:"10",width:"8",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("span",{className:"mx-2 border-l h-6"}),e.jsx("button",{type:"button","aria-label":"Align Left","aria-pressed":t.isActive({textAlign:"left"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"left"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("left").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Center","aria-pressed":t.isActive({textAlign:"center"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"center"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("center").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"5",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Align Right","aria-pressed":t.isActive({textAlign:"right"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"right"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("right").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"7",y:"8",width:"8",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),e.jsx("button",{type:"button","aria-label":"Justify","aria-pressed":t.isActive({textAlign:"justify"}),className:`px-2 py-1 rounded ${t.isActive({textAlign:"justify"})?"text-gray-800 bg-gray-100":"text-gray-600"}`,onClick:()=>t.chain().focus().setTextAlign("justify").run(),children:e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("rect",{x:"3",y:"4",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"8",width:"12",height:"2",rx:"1",fill:"currentColor"}),e.jsx("rect",{x:"3",y:"12",width:"12",height:"2",rx:"1",fill:"currentColor"})]})})]}),e.jsxs("div",{className:"flex items-center justify-between px-2 py-2",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{type:"button","aria-label":"Undo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>t.chain().focus().undo().run(),children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M7 4L3 8L7 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Undo"]}),e.jsxs("button",{type:"button","aria-label":"Redo",className:"flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700",onClick:()=>t.chain().focus().redo().run(),children:[e.jsxs("svg",{width:"18",height:"18",fill:"none",viewBox:"0 0 18 18",children:[e.jsx("path",{d:"M11 4L15 8L11 12",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9",stroke:"#4B5563",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Redo"]})]}),e.jsx("div",{className:"flex gap-6 items-center",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-blue-100 border border-blue-300"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"Original AI Text"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-4 h-4 rounded-sm bg-gray-200 border border-gray-400"}),e.jsx("span",{className:"text-gray-500 text-sm",children:"New Changes"})]})]})})]})]}):null},je=()=>{const t=D(),{globalDispatch:x}=H(),[c,d]=r.useState([]),[h,y]=r.useState(!0),[w,g]=r.useState(null),[u,l]=r.useState(!1),[S,j]=r.useState(!1),[b,k]=r.useState("Case Report "+localStorage.getItem("patientId")),[C,I]=r.useState(""),[A,L]=r.useState([]),[m,T]=r.useState("");r.useEffect(()=>{(async()=>{try{const o=await new N().getDoctors();o.data&&L(o.data)}catch(s){console.error("Error fetching doctors:",s)}})()},[]);const n=E({extensions:[O.configure({heading:{levels:[1,2,3]},bulletList:{keepMarks:!0,keepAttributes:!0},orderedList:{keepMarks:!0,keepAttributes:!0}}),_,W.configure({types:["heading","paragraph"]}),M,R,$,U],content:"",editable:!1,editorProps:{attributes:{class:"prose prose-sm max-w-none min-h-[600px] outline-none text-gray-800 font-['Inter'] whitespace-pre-wrap",spellCheck:"true","aria-label":"Review Changes Document"}},parseOptions:{preserveWhitespace:"full"}});r.useEffect(()=>{const i=localStorage.getItem("reportSections");if(i)try{const s=JSON.parse(i);d(s);const o=s.map(a=>`${a.body}`).join("");n==null||n.commands.setContent(o)}catch(s){g("Failed to load sections from storage"),console.error("Error loading sections:",s)}else g("No sections found in storage");y(!1)},[n]),r.useEffect(()=>{if(n&&c){const i=({transaction:s})=>{if(!s.docChanged)return;const o=s.doc;let a=-1,f="";const p=[...c];o.descendants(v=>{v.type.name==="heading"&&v.attrs.level===2?(a>=0&&(p[a]={...p[a],body:f.trim()}),a++,f=""):a>=0&&(f+=v.textContent)}),a>=0&&(p[a]={...p[a],body:f.trim()}),d(p)};return n.on("transaction",i),()=>{n.off("transaction",i)}}},[n,c]);const B=async()=>{try{l(!0);const i=new N;if(m===""){z(x,"Please select a doctor to save the project",3e3,F.WARNING),l(!1);return}for(const o of c)await i.updateReportSection({id:o.id,title:o.title,content:o.body}),await new Promise(a=>setTimeout(a,100));const s=localStorage.getItem("projectId");s?(await i.assignProject({project_name:b,project_id:s,doctor_id:m}),localStorage.removeItem("reportSections"),localStorage.removeItem("projectId"),localStorage.removeItem("patientId"),localStorage.removeItem("patientName"),localStorage.removeItem("reportId"),t("/projects")):(await i.createProject({name:b,diagnosis:C||"",user_id:m,patient_id:localStorage.getItem("patientId")||"",patient_name:localStorage.getItem("patientName")||"",status:1}),localStorage.removeItem("reportSections"),localStorage.removeItem("projectId"),localStorage.removeItem("patientId"),localStorage.removeItem("patientName"),localStorage.removeItem("reportId"),t("/projects"))}catch(i){console.error("Error saving project:",i),g("Failed to save project. Please try again.")}finally{l(!1),j(!1)}};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsxs("div",{className:"flex max-w-[95%] mx-auto flex-col flex-shrink-0 justify-center items-start gap-6 border-0 border-gray-200 bg-black/0",children:[e.jsx("div",{className:"flex flex-shrink-0  items-center p-4 w-full border h-16 rounded-lg border-0 border-gray-200 bg-white",children:e.jsx("h1",{className:"text-2xl mt- text-gray-800 font-['Inter']",children:"Review Changes"})}),e.jsxs("div",{className:"w-full h-full",children:[e.jsx(K,{editor:n}),e.jsx(P,{editor:n})]}),e.jsxs("div",{className:"flex justify-between items-center w-full p-4 border-t border-gray-200",children:[e.jsxs("span",{className:"text-sm text-gray-600",children:["Last edited by You - ",new Date().toLocaleDateString()]}),e.jsx("div",{className:"flex gap-3",children:e.jsx("button",{onClick:()=>j(!0),disabled:u,className:"px-6 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700 disabled:opacity-50",children:u?"Saving...":"Submit"})})]})]}),e.jsx(J,{isOpen:S,onClose:()=>j(!1),onSave:B,projectName:b,setProjectName:k,projectDescription:C,setProjectDescription:I,isSaving:u,doctors:A,selectedDoctor:m,setSelectedDoctor:T})]})};export{je as ReviewChangesPage,je as default};
