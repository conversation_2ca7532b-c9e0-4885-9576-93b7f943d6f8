const a=()=>typeof window<"u",c=()=>!(!a()||!window.hj),i=(r,...t)=>{if(a()&&window.hj)return window.hj(r,...t);throw Error("Hotjar is not available, make sure init has been called.")},d=(r,t,e)=>{if(!((o,h,s)=>{try{const n=document.getElementById(h)||document.createElement("script");return n.id=h,n.nonce=s,n.innerText=o,n.crossOrigin="anonymous",document.head.appendChild(n),!0}catch{return!1}})(`(function(h,o,t,j,a,r){h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};h._hjSettings={hjid:${r},hjsv:${t},hjdebug:${(e==null?void 0:e.debug)||!1}};a=o.getElementsByTagName('head')[0];r=o.createElement('script');r.async=1;r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;a.appendChild(r);})(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`,"hotjar-init-script",e==null?void 0:e.nonce)||!c())throw Error("Failed to initialize Hotjar tracking script.")},u={init:(r,t,e)=>{try{return d(r,t,e),!0}catch(o){return console.error("Error:",o),!1}},event:r=>{try{return i("event",r),!0}catch(t){return console.error("Error:",t),!1}},identify:(r,t)=>{try{return i("identify",r,t),!0}catch(e){return console.error("Error:",e),!1}},stateChange:r=>{try{return i("stateChange",r),!0}catch(t){return console.error("Error:",t),!1}},isReady:c};export{u as o};
