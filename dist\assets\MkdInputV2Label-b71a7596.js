import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{u as n}from"./MkdInputV2Context-f6333041.js";import"./vendor-489b60f1.js";const d=({children:o,className:r="",...s})=>{const{id:l,required:c,type:i}=n(),t=["radio","checkbox","color","toggle"].includes(i);return e.jsxs("label",{htmlFor:l,className:`${t?"font-inter block h-full cursor-pointer whitespace-nowrap text-[.9375rem] font-bold capitalize text-black":"block cursor-pointer text-[.875rem] font-bold"} ${r}`,...s,children:[o,c&&!t&&e.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]})};export{d as default};
