import{r as o}from"./vendor-489b60f1.js";var ue={cm:!0,mm:!0,in:!0,px:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vw:!0,vh:!0,vmin:!0,vmax:!0,"%":!0};function T(e){if(typeof e=="number")return{value:e,unit:"px"};var n,a=(e.match(/^[0-9.]*/)||"").toString();a.includes(".")?n=parseFloat(a):n=parseInt(a,10);var t=(e.match(/[^0-9]*$/)||"").toString();return ue[t]?{value:n,unit:t}:(console.warn("React Spinners: ".concat(e," is not a valid css value. Defaulting to ").concat(n,"px.")),{value:n,unit:"px"})}function u(e){var n=T(e);return"".concat(n.value).concat(n.unit)}var _=function(e,n,a){var t="react-spinners-".concat(e,"-").concat(a);if(typeof window>"u"||!window.document)return t;var r=document.createElement("style");document.head.appendChild(r);var l=r.sheet,v=`
    @keyframes `.concat(t,` {
      `).concat(n,`
    }
  `);return l&&l.insertRule(v,0),t},$;(function(e){e.maroon="#800000",e.red="#FF0000",e.orange="#FFA500",e.yellow="#FFFF00",e.olive="#808000",e.green="#008000",e.purple="#800080",e.fuchsia="#FF00FF",e.lime="#00FF00",e.teal="#008080",e.aqua="#00FFFF",e.blue="#0000FF",e.navy="#000080",e.black="#000000",e.gray="#808080",e.silver="#C0C0C0",e.white="#FFFFFF"})($||($={}));var oe=function(e,n){if(Object.keys($).includes(e)&&(e=$[e]),e[0]==="#"&&(e=e.slice(1)),e.length===3){var a="";e.split("").forEach(function(r){a+=r,a+=r}),e=a}var t=(e.match(/.{2}/g)||[]).map(function(r){return parseInt(r,16)}).join(", ");return"rgba(".concat(t,", ").concat(n,")")},R=globalThis&&globalThis.__assign||function(){return R=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},R.apply(this,arguments)},fe=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},ge=_("BarLoader","0% {left: -35%;right: 100%} 60% {left: 100%;right: -90%} 100% {left: 100%;right: -90%}","long"),ve=_("BarLoader","0% {left: -200%;right: 100%} 60% {left: 107%;right: -8%} 100% {left: 107%;right: -8%}","short");function at(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.height,i=s===void 0?4:s,m=e.width,b=m===void 0?100:m,p=fe(e,["loading","color","speedMultiplier","cssOverride","height","width"]),f=R({display:"inherit",position:"relative",width:u(b),height:u(i),overflow:"hidden",backgroundColor:oe(r,.2),backgroundClip:"padding-box"},h),d=function(g){return{position:"absolute",height:u(i),overflow:"hidden",backgroundColor:r,backgroundClip:"padding-box",display:"block",borderRadius:2,willChange:"left, right",animationFillMode:"forwards",animation:"".concat(g===1?ge:ve," ").concat(2.1/v,"s ").concat(g===2?"".concat(1.15/v,"s"):""," ").concat(g===1?"cubic-bezier(0.65, 0.815, 0.735, 0.395)":"cubic-bezier(0.165, 0.84, 0.44, 1)"," infinite")}};return a?o.createElement("span",R({style:f},p),o.createElement("span",{style:d(1)}),o.createElement("span",{style:d(2)})):null}var X=globalThis&&globalThis.__assign||function(){return X=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},X.apply(this,arguments)},me=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},be=_("BeatLoader","50% {transform: scale(0.75);opacity: 0.2} 100% {transform: scale(1);opacity: 1}","beat");function ot(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=e.margin,b=m===void 0?2:m,p=me(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=X({display:"inherit"},h),d=function(g){return{display:"inline-block",backgroundColor:r,width:u(i),height:u(i),margin:u(b),borderRadius:"100%",animation:"".concat(be," ").concat(.7/v,"s ").concat(g%2?"0s":"".concat(.35/v,"s")," infinite linear"),animationFillMode:"both"}};return a?o.createElement("span",X({style:f},p),o.createElement("span",{style:d(1)}),o.createElement("span",{style:d(2)}),o.createElement("span",{style:d(3)})):null}var I=globalThis&&globalThis.__assign||function(){return I=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)},ye=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},he=_("BounceLoader","0% {transform: scale(0)} 50% {transform: scale(1.0)} 100% {transform: scale(0)}","bounce");function it(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?60:s,m=ye(e,["loading","color","speedMultiplier","cssOverride","size"]),b=function(f){var d=f===1?"".concat(1/v,"s"):"0s";return{position:"absolute",height:u(i),width:u(i),backgroundColor:r,borderRadius:"100%",opacity:.6,top:0,left:0,animationFillMode:"both",animation:"".concat(he," ").concat(2.1/v,"s ").concat(d," infinite ease-in-out")}},p=I({display:"inherit",position:"relative",width:u(i),height:u(i)},h);return a?o.createElement("span",I({style:p},m),o.createElement("span",{style:b(1)}),o.createElement("span",{style:b(2)})):null}var C=globalThis&&globalThis.__assign||function(){return C=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},C.apply(this,arguments)},Oe=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},we=_("CircleLoader","0% {transform: rotate(0deg)} 50% {transform: rotate(180deg)} 100% {transform: rotate(360deg)}","circle");function lt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?50:s,m=Oe(e,["loading","color","speedMultiplier","cssOverride","size"]),b=C({display:"inherit",position:"relative",width:u(i),height:u(i)},h),p=function(f){var d=T(i),g=d.value,y=d.unit;return{position:"absolute",height:"".concat(g*(1-f/10)).concat(y),width:"".concat(g*(1-f/10)).concat(y),borderTop:"1px solid ".concat(r),borderBottom:"none",borderLeft:"1px solid ".concat(r),borderRight:"none",borderRadius:"100%",transition:"2s",top:"".concat(f*.7*2.5,"%"),left:"".concat(f*.35*2.5,"%"),animation:"".concat(we," ").concat(1/v,"s ").concat(f*.2/v,"s infinite linear")}};return a?o.createElement("span",C({style:b},m),o.createElement("span",{style:p(0)}),o.createElement("span",{style:p(1)}),o.createElement("span",{style:p(2)}),o.createElement("span",{style:p(3)}),o.createElement("span",{style:p(4)})):null}var Y=globalThis&&globalThis.__assign||function(){return Y=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Y.apply(this,arguments)},_e=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},xe=_("ClimbingBoxLoader",`0% {transform:translate(0, -1em) rotate(-45deg)}
  5% {transform:translate(0, -1em) rotate(-50deg)}
  20% {transform:translate(1em, -2em) rotate(47deg)}
  25% {transform:translate(1em, -2em) rotate(45deg)}
  30% {transform:translate(1em, -2em) rotate(40deg)}
  45% {transform:translate(2em, -3em) rotate(137deg)}
  50% {transform:translate(2em, -3em) rotate(135deg)}
  55% {transform:translate(2em, -3em) rotate(130deg)}
  70% {transform:translate(3em, -4em) rotate(217deg)}
  75% {transform:translate(3em, -4em) rotate(220deg)}
  100% {transform:translate(0, -1em) rotate(-225deg)}`,"climbingBox");function ct(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=_e(e,["loading","color","speedMultiplier","cssOverride","size"]),b=Y({display:"inherit",position:"relative",width:"7.1em",height:"7.1em"},h),p={position:"absolute",top:"50%",left:"50%",marginTop:"-2.7em",marginLeft:"-2.7em",width:"5.4em",height:"5.4em",fontSize:u(i)},f={position:"absolute",left:"0",bottom:"-0.1em",height:"1em",width:"1em",backgroundColor:"transparent",borderRadius:"15%",border:"0.25em solid ".concat(r),transform:"translate(0, -1em) rotate(-45deg)",animationFillMode:"both",animation:"".concat(xe," ").concat(2.5/v,"s infinite cubic-bezier(0.79, 0, 0.47, 0.97)")},d={position:"absolute",width:"7.1em",height:"7.1em",top:"1.7em",left:"1.7em",borderLeft:"0.25em solid ".concat(r),transform:"rotate(45deg)"};return a?o.createElement("span",Y({style:b},m),o.createElement("span",{style:p},o.createElement("span",{style:f}),o.createElement("span",{style:d}))):null}var D=globalThis&&globalThis.__assign||function(){return D=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},D.apply(this,arguments)},je=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Ee=_("ClipLoader","0% {transform: rotate(0deg) scale(1)} 50% {transform: rotate(180deg) scale(0.8)} 100% {transform: rotate(360deg) scale(1)}","clip");function st(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?35:s,m=je(e,["loading","color","speedMultiplier","cssOverride","size"]),b=D({background:"transparent !important",width:u(i),height:u(i),borderRadius:"100%",border:"2px solid",borderTopColor:r,borderBottomColor:"transparent",borderLeftColor:r,borderRightColor:r,display:"inline-block",animation:"".concat(Ee," ").concat(.75/v,"s 0s infinite linear"),animationFillMode:"both"},h);return a?o.createElement("span",D({style:b},m)):null}var q=globalThis&&globalThis.__assign||function(){return q=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},q.apply(this,arguments)},Pe=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},re=_("ClockLoader","100% { transform: rotate(360deg) }","rotate");function pt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?50:s,m=Pe(e,["loading","color","speedMultiplier","cssOverride","size"]),b=T(i),p=b.value,f=b.unit,d=q({display:"inherit",position:"relative",width:"".concat(p).concat(f),height:"".concat(p).concat(f),backgroundColor:"transparent",boxShadow:"inset 0px 0px 0px 2px ".concat(r),borderRadius:"50%"},h),g={position:"absolute",backgroundColor:r,width:"".concat(p/3,"px"),height:"2px",top:"".concat(p/2-1,"px"),left:"".concat(p/2-1,"px"),transformOrigin:"1px 1px",animation:"".concat(re," ").concat(8/v,"s linear infinite")},y={position:"absolute",backgroundColor:r,width:"".concat(p/2.4,"px"),height:"2px",top:"".concat(p/2-1,"px"),left:"".concat(p/2-1,"px"),transformOrigin:"1px 1px",animation:"".concat(re," ").concat(2/v,"s linear infinite")};return a?o.createElement("span",q({style:d},m),o.createElement("span",{style:y}),o.createElement("span",{style:g})):null}var H=globalThis&&globalThis.__assign||function(){return H=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},H.apply(this,arguments)},Te=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Me=_("DotLoader","100% {transform: rotate(360deg)}","rotate"),ze=_("DotLoader","0%, 100% {transform: scale(0)} 50% {transform: scale(1.0)}","bounce");function dt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?60:s,m=Te(e,["loading","color","speedMultiplier","cssOverride","size"]),b=H({display:"inherit",position:"relative",width:u(i),height:u(i),animationFillMode:"forwards",animation:"".concat(Me," ").concat(2/v,"s 0s infinite linear")},h),p=function(f){var d=T(i),g=d.value,y=d.unit;return{position:"absolute",top:f%2?"0":"auto",bottom:f%2?"auto":"0",height:"".concat(g/2).concat(y),width:"".concat(g/2).concat(y),backgroundColor:r,borderRadius:"100%",animationFillMode:"forwards",animation:"".concat(ze," ").concat(2/v,"s ").concat(f===2?"1s":"0s"," infinite linear")}};return a?o.createElement("span",H({style:b},m),o.createElement("span",{style:p(1)}),o.createElement("span",{style:p(2)})):null}var j=globalThis&&globalThis.__assign||function(){return j=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},j.apply(this,arguments)},Se=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Le=_("FadeLoader","50% {opacity: 0.3} 100% {opacity: 1}","fade");function ut(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.height,i=s===void 0?15:s,m=e.width,b=m===void 0?5:m,p=e.radius,f=p===void 0?2:p,d=e.margin,g=d===void 0?2:d,y=Se(e,["loading","color","speedMultiplier","cssOverride","height","width","radius","margin"]),w=T(g).value,O=w+18,x=O/2+O/5.5,z=j({display:"inherit",position:"relative",fontSize:"0",top:O,left:O,width:"".concat(O*3,"px"),height:"".concat(O*3,"px")},h),P=function(de){return{position:"absolute",width:u(b),height:u(i),margin:u(g),backgroundColor:r,borderRadius:u(f),transition:"2s",animationFillMode:"both",animation:"".concat(Le," ").concat(1.2/v,"s ").concat(de*.12,"s infinite ease-in-out")}},k=j(j({},P(1)),{top:"".concat(O,"px"),left:"0"}),te=j(j({},P(2)),{top:"".concat(x,"px"),left:"".concat(x,"px"),transform:"rotate(-45deg)"}),F=j(j({},P(3)),{top:"0",left:"".concat(O,"px"),transform:"rotate(90deg)"}),ie=j(j({},P(4)),{top:"".concat(-1*x,"px"),left:"".concat(x,"px"),transform:"rotate(45deg)"}),le=j(j({},P(5)),{top:"".concat(-1*O,"px"),left:"0"}),ce=j(j({},P(6)),{top:"".concat(-1*x,"px"),left:"".concat(-1*x,"px"),transform:"rotate(-45deg)"}),se=j(j({},P(7)),{top:"0",left:"".concat(-1*O,"px"),transform:"rotate(90deg)"}),pe=j(j({},P(8)),{top:"".concat(x,"px"),left:"".concat(-1*x,"px"),transform:"rotate(45deg)"});return a?o.createElement("span",j({style:z},y),o.createElement("span",{style:k}),o.createElement("span",{style:te}),o.createElement("span",{style:F}),o.createElement("span",{style:ie}),o.createElement("span",{style:le}),o.createElement("span",{style:ce}),o.createElement("span",{style:se}),o.createElement("span",{style:pe})):null}var U=globalThis&&globalThis.__assign||function(){return U=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},U.apply(this,arguments)},Fe=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},ke=_("GridLoader","0% {transform: scale(1)} 50% {transform: scale(0.5); opacity: 0.7} 100% {transform: scale(1); opacity: 1}","grid"),S=function(e){return Math.random()*e};function ft(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=e.margin,b=m===void 0?2:m,p=Fe(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=T(i),d=T(b),g=parseFloat(f.value.toString())*3+parseFloat(d.value.toString())*6,y=U({width:"".concat(g).concat(f.unit),fontSize:0,display:"inline-block"},h),w=function(O){return{display:"inline-block",backgroundColor:r,width:"".concat(u(i)),height:"".concat(u(i)),margin:u(b),borderRadius:"100%",animationFillMode:"both",animation:"".concat(ke," ").concat((O/100+.6)/v,"s ").concat(O/100-.2,"s infinite ease")}};return a?o.createElement("span",U({style:y},p,{ref:function(O){O&&O.style.setProperty("width","".concat(g).concat(f.unit),"important")}}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))}),o.createElement("span",{style:w(S(100))})):null}var V=globalThis&&globalThis.__assign||function(){return V=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},V.apply(this,arguments)},$e=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a};function gt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?50:s,m=$e(e,["loading","color","speedMultiplier","cssOverride","size"]),b=T(i),p=b.value,f=b.unit,d=V({display:"inherit",position:"relative",width:u(i),height:u(i),transform:"rotate(165deg)"},h),g=p/5,y=(p-g)/2,w=y-g,O=oe(r,.75),x=_("HashLoader","0% {width: ".concat(g,"px; box-shadow: ").concat(y,"px ").concat(-w,"px ").concat(O,", ").concat(-y,"px ").concat(w,"px ").concat(O,`}
    35% {width: `).concat(u(i),"; box-shadow: 0 ").concat(-w,"px ").concat(O,", 0 ").concat(w,"px ").concat(O,`}
    70% {width: `).concat(g,"px; box-shadow: ").concat(-y,"px ").concat(-w,"px ").concat(O,", ").concat(y,"px ").concat(w,"px ").concat(O,`}
    100% {box-shadow: `).concat(y,"px ").concat(-w,"px ").concat(O,", ").concat(-y,"px ").concat(w,"px ").concat(O,"}"),"before"),z=_("HashLoader","0% {height: ".concat(g,"px; box-shadow: ").concat(w,"px ").concat(y,"px ").concat(r,", ").concat(-w,"px ").concat(-y,"px ").concat(r,`}
    35% {height: `).concat(u(i),"; box-shadow: ").concat(w,"px 0 ").concat(r,", ").concat(-w,"px 0 ").concat(r,`}
    70% {height: `).concat(g,"px; box-shadow: ").concat(w,"px ").concat(-y,"px ").concat(r,", ").concat(-w,"px ").concat(y,"px ").concat(r,`}
    100% {box-shadow: `).concat(w,"px ").concat(y,"px ").concat(r,", ").concat(-w,"px ").concat(-y,"px ").concat(r,"}"),"after"),P=function(k){return{position:"absolute",top:"50%",left:"50%",display:"block",width:"".concat(p/5).concat(f),height:"".concat(p/5).concat(f),borderRadius:"".concat(p/10).concat(f),transform:"translate(-50%, -50%)",animationFillMode:"none",animation:"".concat(k===1?x:z," ").concat(2/v,"s infinite")}};return a?o.createElement("span",V({style:d},m),o.createElement("span",{style:P(1)}),o.createElement("span",{style:P(2)})):null}var L=globalThis&&globalThis.__assign||function(){return L=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},L.apply(this,arguments)},Re=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},ne=_("MoonLoader","100% {transform: rotate(360deg)}","moon");function vt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?60:s,m=Re(e,["loading","color","speedMultiplier","cssOverride","size"]),b=T(i),p=b.value,f=b.unit,d=p/7,g=L({display:"inherit",position:"relative",width:"".concat("".concat(p+d*2).concat(f)),height:"".concat("".concat(p+d*2).concat(f)),animation:"".concat(ne," ").concat(.6/v,"s 0s infinite linear"),animationFillMode:"forwards"},h),y=function(x){return{width:u(x),height:u(x),borderRadius:"100%"}},w=L(L({},y(d)),{backgroundColor:"".concat(r),opacity:"0.8",position:"absolute",top:"".concat("".concat(p/2-d/2).concat(f)),animation:"".concat(ne," ").concat(.6/v,"s 0s infinite linear"),animationFillMode:"forwards"}),O=L(L({},y(p)),{border:"".concat(d,"px solid ").concat(r),opacity:"0.1",boxSizing:"content-box",position:"absolute"});return a?o.createElement("span",L({style:g},m),o.createElement("span",{style:w}),o.createElement("span",{style:O})):null}var Z=globalThis&&globalThis.__assign||function(){return Z=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Z.apply(this,arguments)},Xe=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Ie=[_("PacmanLoader","0% {transform: rotate(0deg)} 50% {transform: rotate(-44deg)}","pacman-1"),_("PacmanLoader","0% {transform: rotate(0deg)} 50% {transform: rotate(44deg)}","pacman-2")];function mt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?25:s,m=e.margin,b=m===void 0?2:m,p=Xe(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=T(i),d=f.value,g=f.unit,y=Z({display:"inherit",position:"relative",fontSize:0,height:"".concat(d*2).concat(g),width:"".concat(d*2).concat(g)},h),w=_("PacmanLoader",`75% {opacity: 0.7}
    100% {transform: translate(`.concat("".concat(-4*d).concat(g),", ").concat("".concat(-d/4).concat(g),")}"),"ball"),O=function(F){return{width:"".concat(d/3).concat(g),height:"".concat(d/3).concat(g),backgroundColor:r,margin:u(b),borderRadius:"100%",transform:"translate(0, ".concat("".concat(-d/4).concat(g),")"),position:"absolute",top:"".concat(d).concat(g),left:"".concat(d*4).concat(g),animation:"".concat(w," ").concat(1/v,"s ").concat(F*.25,"s infinite linear"),animationFillMode:"both"}},x="".concat(u(i)," solid transparent"),z="".concat(u(i)," solid ").concat(r),P=function(F){return{width:0,height:0,borderRight:x,borderTop:F===0?x:z,borderLeft:z,borderBottom:F===0?z:x,borderRadius:u(i),position:"absolute",animation:"".concat(Ie[F]," ").concat(.8/v,"s infinite ease-in-out"),animationFillMode:"both"}},k=P(0),te=P(1);return a?o.createElement("span",Z({style:y},p),o.createElement("span",{style:k}),o.createElement("span",{style:te}),o.createElement("span",{style:O(2)}),o.createElement("span",{style:O(3)}),o.createElement("span",{style:O(4)}),o.createElement("span",{style:O(5)})):null}var A=globalThis&&globalThis.__assign||function(){return A=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)},Ce=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},E=[1,3,5],Ye=[_("PropagateLoader","25% {transform: translateX(-".concat(E[0],`rem) scale(0.75)}
    50% {transform: translateX(-`).concat(E[1],`rem) scale(0.6)}
    75% {transform: translateX(-`).concat(E[2],`rem) scale(0.5)}
    95% {transform: translateX(0rem) scale(1)}`),"propogate-0"),_("PropagateLoader","25% {transform: translateX(-".concat(E[0],`rem) scale(0.75)}
    50% {transform: translateX(-`).concat(E[1],`rem) scale(0.6)}
    75% {transform: translateX(-`).concat(E[1],`rem) scale(0.6)}
    95% {transform: translateX(0rem) scale(1)}`),"propogate-1"),_("PropagateLoader","25% {transform: translateX(-".concat(E[0],`rem) scale(0.75)}
    75% {transform: translateX(-`).concat(E[0],`rem) scale(0.75)}
    95% {transform: translateX(0rem) scale(1)}`),"propogate-2"),_("PropagateLoader","25% {transform: translateX(".concat(E[0],`rem) scale(0.75)}
    75% {transform: translateX(`).concat(E[0],`rem) scale(0.75)}
    95% {transform: translateX(0rem) scale(1)}`),"propogate-3"),_("PropagateLoader","25% {transform: translateX(".concat(E[0],`rem) scale(0.75)}
    50% {transform: translateX(`).concat(E[1],`rem) scale(0.6)}
    75% {transform: translateX(`).concat(E[1],`rem) scale(0.6)}
    95% {transform: translateX(0rem) scale(1)}`),"propogate-4"),_("PropagateLoader","25% {transform: translateX(".concat(E[0],`rem) scale(0.75)}
    50% {transform: translateX(`).concat(E[1],`rem) scale(0.6)}
    75% {transform: translateX(`).concat(E[2],`rem) scale(0.5)}
    95% {transform: translateX(0rem) scale(1)}`),"propogate-5")];function bt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=Ce(e,["loading","color","speedMultiplier","cssOverride","size"]),b=T(i),p=b.value,f=b.unit,d=A({display:"inherit",position:"relative"},h),g=function(y){return{position:"absolute",fontSize:"".concat(p/3).concat(f),width:"".concat(p).concat(f),height:"".concat(p).concat(f),background:r,borderRadius:"50%",animation:"".concat(Ye[y]," ").concat(1.5/v,"s infinite"),animationFillMode:"forwards"}};return a?o.createElement("span",A({style:d},m),o.createElement("span",{style:g(0)}),o.createElement("span",{style:g(1)}),o.createElement("span",{style:g(2)}),o.createElement("span",{style:g(3)}),o.createElement("span",{style:g(4)}),o.createElement("span",{style:g(5)})):null}var G=globalThis&&globalThis.__assign||function(){return G=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},G.apply(this,arguments)},De=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},qe=_("PulseLoader","0% {transform: scale(1); opacity: 1} 45% {transform: scale(0.1); opacity: 0.7} 80% {transform: scale(1); opacity: 1}","pulse");function yt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=e.margin,b=m===void 0?2:m,p=De(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=G({display:"inherit"},h),d=function(g){return{backgroundColor:r,width:u(i),height:u(i),margin:u(b),borderRadius:"100%",display:"inline-block",animation:"".concat(qe," ").concat(.75/v,"s ").concat(g*.12/v,"s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08)"),animationFillMode:"both"}};return a?o.createElement("span",G({style:f},p),o.createElement("span",{style:d(1)}),o.createElement("span",{style:d(2)}),o.createElement("span",{style:d(3)})):null}var W=globalThis&&globalThis.__assign||function(){return W=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)},He=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},ae=[_("PuffLoader","0% {transform: scale(0)} 100% {transform: scale(1.0)}","puff-1"),_("PuffLoader","0% {opacity: 1} 100% {opacity: 0}","puff-2")];function ht(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?60:s,m=He(e,["loading","color","speedMultiplier","cssOverride","size"]),b=W({display:"inherit",position:"relative",width:u(i),height:u(i)},h),p=function(f){return{position:"absolute",height:u(i),width:u(i),border:"thick solid ".concat(r),borderRadius:"50%",opacity:"1",top:"0",left:"0",animationFillMode:"both",animation:"".concat(ae[0],", ").concat(ae[1]),animationDuration:"".concat(2/v,"s"),animationIterationCount:"infinite",animationTimingFunction:"cubic-bezier(0.165, 0.84, 0.44, 1), cubic-bezier(0.3, 0.61, 0.355, 1)",animationDelay:f===1?"-1s":"0s"}};return a?o.createElement("span",W({style:b},m),o.createElement("span",{style:p(1)}),o.createElement("span",{style:p(2)})):null}var B=globalThis&&globalThis.__assign||function(){return B=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)},Ue=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Ve=_("RingLoader","0% {transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg)} 100% {transform: rotateX(180deg) rotateY(360deg) rotateZ(360deg)}","right"),Ze=_("RingLoader","0% {transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg)} 100% {transform: rotateX(360deg) rotateY(180deg) rotateZ(360deg)}","left");function Ot(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?60:s,m=Ue(e,["loading","color","speedMultiplier","cssOverride","size"]),b=T(i),p=b.value,f=b.unit,d=B({display:"inherit",width:u(i),height:u(i),position:"relative"},h),g=function(y){return{position:"absolute",top:"0",left:"0",width:"".concat(p).concat(f),height:"".concat(p).concat(f),border:"".concat(p/10).concat(f," solid ").concat(r),opacity:"0.4",borderRadius:"100%",animationFillMode:"forwards",perspective:"800px",animation:"".concat(y===1?Ve:Ze," ").concat(2/v,"s 0s infinite linear")}};return a?o.createElement("span",B({style:d},m),o.createElement("span",{style:g(1)}),o.createElement("span",{style:g(2)})):null}var N=globalThis&&globalThis.__assign||function(){return N=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)},Ae=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a};function wt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=e.margin,b=m===void 0?2:m,p=Ae(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=N({display:"inherit"},h),d=_("RiseLoader",`0% {transform: scale(1.1)}
    25% {transform: translateY(-`.concat(i,`px)}
    50% {transform: scale(0.4)}
    75% {transform: translateY(`).concat(i,`px)}
    100% {transform: translateY(0) scale(1.0)}`),"even"),g=_("RiseLoader",`0% {transform: scale(0.4)}
    25% {transform: translateY(`.concat(i,`px)}
    50% {transform: scale(1.1)}
    75% {transform: translateY(`).concat(-i,`px)}
    100% {transform: translateY(0) scale(0.75)}`),"odd"),y=function(w){return{backgroundColor:r,width:u(i),height:u(i),margin:u(b),borderRadius:"100%",display:"inline-block",animation:"".concat(w%2===0?d:g," ").concat(1/v,"s 0s infinite cubic-bezier(0.15, 0.46, 0.9, 0.6)"),animationFillMode:"both"}};return a?o.createElement("span",N({style:f},p),o.createElement("span",{style:y(1)}),o.createElement("span",{style:y(2)}),o.createElement("span",{style:y(3)}),o.createElement("span",{style:y(4)}),o.createElement("span",{style:y(5)})):null}var M=globalThis&&globalThis.__assign||function(){return M=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)},Ge=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},We=_("RotateLoader","0% {transform: rotate(0deg)} 50% {transform: rotate(180deg)} 100% {transform: rotate(360deg)}","rotate");function _t(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=e.margin,b=m===void 0?2:m,p=Ge(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=T(b),d=f.value,g=f.unit,y={backgroundColor:r,width:u(i),height:u(i),borderRadius:"100%"},w=M(M(M({},y),{display:"inline-block",position:"relative",animationFillMode:"both",animation:"".concat(We," ").concat(1/v,"s 0s infinite cubic-bezier(0.7, -0.13, 0.22, 0.86)")}),h),O=function(x){var z=(x%2?-1:1)*(26+d);return{opacity:"0.8",position:"absolute",top:"0",left:"".concat(z).concat(g)}};return a?o.createElement("span",M({style:w},p),o.createElement("span",{style:M(M({},y),O(1))}),o.createElement("span",{style:M(M({},y),O(2))})):null}var J=globalThis&&globalThis.__assign||function(){return J=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},J.apply(this,arguments)},Be=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Ne=_("ScaleLoader","0% {transform: scaley(1.0)} 50% {transform: scaley(0.4)} 100% {transform: scaley(1.0)}","scale");function xt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.height,i=s===void 0?35:s,m=e.width,b=m===void 0?4:m,p=e.radius,f=p===void 0?2:p,d=e.margin,g=d===void 0?2:d,y=Be(e,["loading","color","speedMultiplier","cssOverride","height","width","radius","margin"]),w=J({display:"inherit"},h),O=function(x){return{backgroundColor:r,width:u(b),height:u(i),margin:u(g),borderRadius:u(f),display:"inline-block",animation:"".concat(Ne," ").concat(1/v,"s ").concat(x*.1,"s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08)"),animationFillMode:"both"}};return a?o.createElement("span",J({style:w},y),o.createElement("span",{style:O(1)}),o.createElement("span",{style:O(2)}),o.createElement("span",{style:O(3)}),o.createElement("span",{style:O(4)}),o.createElement("span",{style:O(5)})):null}var K=globalThis&&globalThis.__assign||function(){return K=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},K.apply(this,arguments)},Je=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},Ke=_("SkewLoader","25% {transform: perspective(100px) rotateX(180deg) rotateY(0)} 50% {transform: perspective(100px) rotateX(180deg) rotateY(180deg)} 75% {transform: perspective(100px) rotateX(0) rotateY(180deg)} 100% {transform: perspective(100px) rotateX(0) rotateY(0)}","skew");function jt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?20:s,m=Je(e,["loading","color","speedMultiplier","cssOverride","size"]),b=K({width:"0",height:"0",borderLeft:"".concat(u(i)," solid transparent"),borderRight:"".concat(u(i)," solid transparent"),borderBottom:"".concat(u(i)," solid ").concat(r),display:"inline-block",animation:"".concat(Ke," ").concat(3/v,"s 0s infinite cubic-bezier(0.09, 0.57, 0.49, 0.9)"),animationFillMode:"both"},h);return a?o.createElement("span",K({style:b},m)):null}var Q=globalThis&&globalThis.__assign||function(){return Q=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)},Qe=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},et=_("SquareLoader",`25% {transform: rotateX(180deg) rotateY(0)}
  50% {transform: rotateX(180deg) rotateY(180deg)} 
  75% {transform: rotateX(0) rotateY(180deg)} 
  100% {transform: rotateX(0) rotateY(0)}`,"square");function Et(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?50:s,m=Qe(e,["loading","color","speedMultiplier","cssOverride","size"]),b=Q({backgroundColor:r,width:u(i),height:u(i),display:"inline-block",animation:"".concat(et," ").concat(3/v,"s 0s infinite cubic-bezier(0.09, 0.57, 0.49, 0.9)"),animationFillMode:"both"},h);return a?o.createElement("span",Q({style:b},m)):null}var ee=globalThis&&globalThis.__assign||function(){return ee=Object.assign||function(e){for(var n,a=1,t=arguments.length;a<t;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ee.apply(this,arguments)},tt=globalThis&&globalThis.__rest||function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(a[t[r]]=e[t[r]]);return a},rt=_("SyncLoader",`33% {transform: translateY(10px)}
  66% {transform: translateY(-10px)}
  100% {transform: translateY(0)}`,"sync");function Pt(e){var n=e.loading,a=n===void 0?!0:n,t=e.color,r=t===void 0?"#000000":t,l=e.speedMultiplier,v=l===void 0?1:l,c=e.cssOverride,h=c===void 0?{}:c,s=e.size,i=s===void 0?15:s,m=e.margin,b=m===void 0?2:m,p=tt(e,["loading","color","speedMultiplier","cssOverride","size","margin"]),f=ee({display:"inherit"},h),d=function(g){return{backgroundColor:r,width:u(i),height:u(i),margin:u(b),borderRadius:"100%",display:"inline-block",animation:"".concat(rt," ").concat(.6/v,"s ").concat(g*.07,"s infinite ease-in-out"),animationFillMode:"both"}};return a?o.createElement("span",ee({style:f},p),o.createElement("span",{style:d(1)}),o.createElement("span",{style:d(2)}),o.createElement("span",{style:d(3)})):null}export{ot as B,lt as C,dt as D,ut as F,ft as G,gt as H,vt as M,mt as P,Ot as R,Pt as S,at as a,it as b,ct as c,st as d,pt as e,wt as f,bt as g,ht as h,yt as i,_t as j,xt as k,jt as l,Et as m};
