import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{d as L,r as a}from"./vendor-489b60f1.js";import{a as A}from"./index-95f0e460.js";import{f as V}from"./date-fns-66ee9ebe.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Z=()=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10 4.37C13.76 4.37 17.13 6.89 18.54 10.5C17.13 14.11 13.76 16.63 10 16.63C6.24 16.63 2.87 14.11 1.46 10.5C2.87 6.89 6.24 4.37 10 4.37ZM10 2.5C5 2.5 0.73 5.86 0 10.5C0.73 15.14 5 18.5 10 18.5C15 18.5 19.27 15.14 20 10.5C19.27 5.86 15 2.5 10 2.5ZM10 7.5C11.38 7.5 12.5 8.62 12.5 10C12.5 11.38 11.38 12.5 10 12.5C8.62 12.5 7.5 11.38 7.5 10C7.5 8.62 8.62 7.5 10 7.5ZM10 5.5C7.52 5.5 5.5 7.52 5.5 10C5.5 12.48 7.52 14.5 10 14.5C12.48 14.5 14.5 12.48 14.5 10C14.5 7.52 12.48 5.5 10 5.5Z",fill:"#4B5563"})}),H=()=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.5 14.375V17.5H5.625L14.8417 8.28334L11.7167 5.15834L2.5 14.375ZM17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C14.9833 2.41667 14.4583 2.41667 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667Z",fill:"#4B5563"})}),B=()=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5 15.8333C5 16.75 5.75 17.5 6.66667 17.5H13.3333C14.25 17.5 15 16.75 15 15.8333V5.83333H5V15.8333ZM15.8333 3.33333H12.9167L12.0833 2.5H7.91667L7.08333 3.33333H4.16667V5H15.8333V3.33333Z",fill:"#4B5563"})}),R=()=>e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}),F=({message:r})=>e.jsx("div",{className:"flex justify-center items-center p-8",children:e.jsxs("div",{className:"text-red-600 text-center",children:[e.jsx("p",{className:"font-medium",children:"Error loading projects"}),e.jsx("p",{className:"text-sm",children:r})]})}),le=()=>{const r=L(),{sdk:d}=A(),[l,w]=a.useState(""),[i,v]=a.useState(""),[n,b]=a.useState(""),[c,S]=a.useState("All Diagnoses"),[o,C]=a.useState("all"),[x,I]=a.useState([]),[D,g]=a.useState(!0),[u,m]=a.useState(null),[j,_]=a.useState(1),[y]=a.useState(10),[f,N]=a.useState(null),p=a.useCallback(async()=>{try{g(!0),m(null);const t={page:j,limit:y,user_id:localStorage.getItem("user")||"",order:"created_at",direction:"desc",...l&&{dateRange:l},...i&&{projectName:i},...n&&{patientName:n},...c!=="All Diagnoses"&&{diagnosis:c},...o!=="all"&&{status:o}},s=await d.getUserProjects(t);if(s.error)throw new Error(s.message||"Failed to fetch projects");I(s.data)}catch(t){m(t instanceof Error?t.message:"An error occurred")}finally{g(!1)}},[d,j,y,l,i,n,c,o]);a.useEffect(()=>{p()},[p]);const P=t=>{const s=x.find(h=>h.projectId===t);s&&(localStorage.setItem("patientId",s.patientId),localStorage.setItem("patientName",s.patientName),localStorage.setItem("reportId",s.reportId)),r("/doctor/report")},E=t=>{localStorage.setItem("reportId",t);const s=x.find(h=>h.projectId===t);s&&(localStorage.setItem("patientId",s.patientId),localStorage.setItem("patientName",s.patientName),localStorage.setItem("reportId",s.reportId)),r("/doctor/report")},M=async t=>{if(window.confirm("Are you sure you want to delete this project?"))try{N(t),await d.deleteProject(t),await p()}catch(s){m(s instanceof Error?s.message:"Failed to delete project")}finally{N(null)}},k=t=>{try{return V(new Date(t),"MMM d, yyyy")}catch{return t}};return e.jsxs("div",{className:"p-8 bg-gray-50 min-h-screen",children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Projects"}),e.jsxs("div",{className:"grid grid-cols-5 gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"text-sm text-gray-600 mb-1",children:"Date Range"}),e.jsx("input",{type:"date",value:l,onChange:t=>w(t.target.value),className:"px-3 py-2 border border-gray-300 rounded-md",placeholder:"mm/dd/yyyy"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"text-sm text-gray-600 mb-1",children:"Project Name"}),e.jsx("input",{type:"text",value:i,onChange:t=>v(t.target.value),className:"px-3 py-2 border border-gray-300 rounded-md",placeholder:"Search projects..."})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"text-sm text-gray-600 mb-1",children:"Patient Name"}),e.jsx("input",{type:"text",value:n,onChange:t=>b(t.target.value),className:"px-3 py-2 border border-gray-300 rounded-md",placeholder:"Search patients..."})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"text-sm text-gray-600 mb-1",children:"Diagnosis"}),e.jsxs("select",{value:c,onChange:t=>S(t.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white",children:[e.jsx("option",{children:"All Diagnoses"}),e.jsx("option",{children:"Anxiety"}),e.jsx("option",{children:"Depression"}),e.jsx("option",{children:"PTSD"})]})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"text-sm text-gray-600 mb-1",children:"Status"}),e.jsxs("select",{value:o,onChange:t=>C(t.target.value),className:"px-3 py-2 border border-gray-300 rounded-md bg-white",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"1",children:"Reviewed"}),e.jsx("option",{value:"0",children:"Pending"})]})]})]}),e.jsx("div",{className:"bg-white rounded-lg shadow",children:D?e.jsx(R,{}):u?e.jsx(F,{message:u}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Project Name"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Client"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Diagnosis"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:x.map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.projectName})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:t.patientName})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:k(t.date)})}),e.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:t.diagnosis}),e.jsx("td",{className:"px-6 py-4 text-sm",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        ${t.status=="1"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:t.status=="1"?"Reviewed":"Pending"})}),e.jsx("td",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"p-1 hover:bg-gray-100 rounded-full",title:"View",onClick:()=>P(t.projectId),children:e.jsx(Z,{})}),e.jsx("button",{className:"p-1 hover:bg-gray-100 rounded-full",title:"Edit",onClick:()=>E(t.reportId),children:e.jsx(H,{})}),e.jsx("button",{className:"p-1 hover:bg-gray-100 rounded-full",title:"Delete",onClick:()=>M(t.projectId),disabled:f===t.projectId,children:f===t.projectId?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"}):e.jsx(B,{})})]})})]},t.projectId))})]})})})]})};export{le as default};
