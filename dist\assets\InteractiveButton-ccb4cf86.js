import{j as a}from"./@react-google-maps/api-5b2d83cc.js";import{r as t}from"./vendor-489b60f1.js";import{a as C}from"./html2pdf.js-82514bbc.js";import{B as S,S as h,a as R,b as L,C as P,c as B,d as O,e as _,D as j,F as I,G as k,H as v,M as w,R as M,f as N,P as D,g as $,h as p,i as F,j as G,k as H,l as U,m as q}from"./react-spinners-6793d0ce.js";const z="_button_aud1r_1",K={button:z};var m=(e=>(e.BEAT="beat",e.SYNC="sync",e.BAR="bar",e.BOUNCE="bounce",e.CIRCLE="circle",e.CLIMBINGBOX="climbingbox",e.CLIP="clip",e.CLOCK="clock",e.DOT="dot",e.FADE="fade",e.GRID="grid",e.HASH="hash",e.MOON="moon",e.RING="ring",e.RISE="rise",e.PACMAN="pacman",e.PROPAGATE="propagate",e.PUFF="puff",e.PULSE="pulse",e.ROTATE="rotate",e.SCALE="scale",e.SKEW="skew",e.SQUARE="square",e))(m||{});const Q=({type:e="beat",children:r,className:n,loaderclasses:s,loading:i=!1,color:c="#ffffff",size:d=10})=>{const o={borderColor:"#ffffff"},l=t.useId(),f={beat:S,sync:h,bar:R,bounce:L,circle:P,climbingbox:B,clip:O,clock:_,dot:j,fade:I,grid:k,hash:v,moon:w,ring:M,rise:N,pacman:D,propagate:$,puff:p,pulse:F,rotate:G,scale:H,skew:U,square:q}[e];return a.jsx("div",{className:`flex items-center justify-center gap-2 ${n}`,children:a.jsxs(a.Fragment,{children:[r,a.jsx(f,{color:c,loading:i,cssOverride:o,size:d,className:s,"data-testid":l})]})})},V=t.memo(Q),W=Object.freeze(Object.defineProperty({__proto__:null,LoaderTypes:m,default:V},Symbol.toStringTag,{value:"Module"})),X=t.lazy(()=>C(()=>Promise.resolve().then(()=>W),void 0)),Y=({loading:e=!1,animate:r=!1,disabled:n,children:s,type:i="button",className:c,loaderclasses:d,onClick:o,color:l="#ffffff",loaderType:u=m.BEAT,buttonRef:f=null,size:g=10})=>{const E=t.useId(),[x,b]=t.useState(!1),A=()=>{o&&o(),r&&b(!0)};return a.jsx("button",{id:E,type:i,ref:f,disabled:n,className:`${x&&"!animate-wiggle"} ${K.button} relative flex h-[2.125rem] w-fit min-w-fit items-center justify-center gap-2 overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem]  py-[.5625rem] font-['Inter'] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600  ${c}`,onAnimationEnd:()=>b(!1),onClick:A,children:a.jsxs(a.Fragment,{children:[s,a.jsx(X,{size:g,color:l,loading:e,type:u,className:d})]})})},ee=t.memo(Y);export{ee as default};
