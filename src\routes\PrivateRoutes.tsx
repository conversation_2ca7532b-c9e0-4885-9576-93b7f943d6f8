import React, { memo } from "react";
import { NotFound } from "./Routes";
import PublicRoute from "./PublicRoutes";
import AdminRoute from "./AdminRoutes";
import { useContexts } from "@/hooks/useContexts";
import { RoleEnum } from "@/utils/Enums";
import MemberRoute from "./MemberRoutes";
import DoctorRout<PERSON> from "./DoctorRoutes";
interface PrivateRouteProps {
  path: string;
  element: JSX.Element;
  access: string[] | string;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({
  path,
  element,
  access,
}: PrivateRouteProps) => {
  const { authState } = useContexts();

  if (authState?.isAuthenticated) {
    switch (true) {
      case [
        ...(["string"].includes(typeof access) ? [access] : access),
        authState?.role,
      ].includes(RoleEnum.ADMIN):
        return <AdminRoute path={path}>{element}</AdminRoute>;

      case [
        ...(["string"].includes(typeof access) ? [access] : access),
        authState?.role,
      ].includes(RoleEnum.USER):
        return <MemberRoute path={path}>{element}</MemberRoute>;

      case [
        ...(["string"].includes(typeof access) ? [access] : access),
        authState?.role,
      ].includes(RoleEnum.DOCTOR):
        return <DoctorRoute path={path}>{element}</DoctorRoute>;

      default:
        return <PublicRoute path={"*"} element={<NotFound />} />;
    }
  }
  if (!authState?.isAuthenticated) {
    return <PublicRoute path={"*"} element={<NotFound />} />;
  }
};

export default memo(PrivateRoute);
