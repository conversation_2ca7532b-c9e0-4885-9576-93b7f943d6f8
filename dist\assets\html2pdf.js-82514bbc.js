const m="modulepreload",h=function(s){return"/"+s},a={},p=function(t,i,u){if(!i||i.length===0)return t();const c=document.getElementsByTagName("link");return Promise.all(i.map(e=>{if(e=h(e),e in a)return;a[e]=!0;const n=e.endsWith(".css"),y=n?'[rel="stylesheet"]':"";if(!!u)for(let o=c.length-1;o>=0;o--){const l=c[o];if(l.href===e&&(!n||l.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${e}"]${y}`))return;const r=document.createElement("link");if(r.rel=n?"stylesheet":m,n||(r.as="script",r.crossOrigin=""),r.href=e,document.head.appendChild(r),n)return new Promise((o,l)=>{r.addEventListener("load",o),r.addEventListener("error",()=>l(new Error(`Unable to preload CSS for ${e}`)))})})).then(()=>t()).catch(e=>{const n=new Event("vite:preloadError",{cancelable:!0});if(n.payload=e,window.dispatchEvent(n),!n.defaultPrevented)throw e})};function f(s){"@babel/helpers - typeof";return f=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(s)}export{f as _,p as a};
