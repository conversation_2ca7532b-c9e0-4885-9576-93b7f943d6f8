import{j as a}from"./@react-google-maps/api-5b2d83cc.js";import{r as g,R}from"./vendor-489b60f1.js";import{u as se}from"./react-hook-form-7e42b371.js";import{o as le}from"./yup-fe85ba88.js";import{c as te,a as b}from"./yup-5d8330af.js";import{I as ie}from"./index-ec6e151a.js";import{a as me,h as ne,u as re,L as de}from"./index-95f0e460.js";import{M as j}from"./index-e9605eb4.js";import{a as pe}from"./index-32ecee74.js";import{C as G,P as $,T as ce}from"./index-fe4acb22.js";import{M as he}from"./index-235b3e94.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Be=({})=>{var O,T;const{sdk:u}=me(),[z,H]=g.useState(""),[i,K]=R.useState({}),[S,J]=g.useState(""),[v,Q]=g.useState("");g.useState("");const[E,U]=g.useState(!1),[d,k]=g.useState({email:"",first_name:"",last_name:"",phone:"",photo:"",dashboard_image:"",company_name:""}),[D,N]=g.useState({showModal:!1,modal:""}),{profile:n}=ne(),{authDispatch:W,globalDispatch:X,tokenExpireError:I,showToast:w,update:fe,setGlobalState:F}=re(),Y=te({email:b().email().required(),first_name:b().nullable(),last_name:b().nullable(),phone:b().nullable(),photo:b().nullable(),dashboard_image:b().nullable(),company_name:b().nullable()}).required(),{register:_,handleSubmit:Z,setError:L,setValue:h,watch:V,formState:{errors:x}}=se({resolver:le(Y)}),{dashboard_image:ee,photo:ae}=V(),M=(o,s,f=!1)=>{var c,l;let r=i;f?r[o]?r[o]=[...r[o],{file:s.files[0],tempFile:{url:URL.createObjectURL(s.files[0]),name:((c=s.files[0])==null?void 0:c.name)??"",type:((l=s.files[0])==null?void 0:l.type)??""}}]:r[o]=[{file:s.files[0],tempFile:{url:URL.createObjectURL(s.files[0]),name:s.files[0].name,type:s.files[0].type}}]:r[o]={file:s.files[0],name:s.files[0].name,type:s.files[0].type,tempURL:URL.createObjectURL(s.files[0])},K({...r})};async function C(){var o,s,f,r,c,l,t,m,p,y,q;try{const e=await u.getProfile();k(()=>{var A,B;return{...e==null?void 0:e.model,role:((A=e==null?void 0:e.model)==null?void 0:A.role)??((B=e==null?void 0:e.model)==null?void 0:B.role_id)}}),h("email",(o=e==null?void 0:e.model)==null?void 0:o.email),h("first_name",(s=e==null?void 0:e.model)==null?void 0:s.first_name),h("last_name",(f=e==null?void 0:e.model)==null?void 0:f.last_name),h("phone",(r=e==null?void 0:e.model)==null?void 0:r.phone),h("dashboard_image",(c=e==null?void 0:e.model)==null?void 0:c.dashboard_image),h("photo",(l=e==null?void 0:e.model)==null?void 0:l.photo),H((t=e==null?void 0:e.model)==null?void 0:t.email),J((m=e==null?void 0:e.model)==null?void 0:m.photo),Q((p=e==null?void 0:e.model)==null?void 0:p.dashboard_image),W({type:"UPDATE_PROFILE",payload:{...e==null?void 0:e.model,role:((y=e==null?void 0:e.model)==null?void 0:y.role)??((q=e==null?void 0:e.model)==null?void 0:q.role_id)}})}catch(e){console.log("Error",e),I(e.response.data.message?e.response.data.message:e.message)}}const oe=async o=>{var s,f,r,c;k(o);try{if(U(!0),i&&i.photo&&((s=i.photo)!=null&&s.file)){let t=new FormData;t.append("file",(f=i.photo)==null?void 0:f.file);let m=await u.uploadImage(t);console.log("uploadResult"),console.log(m),o.photo=m.url,w("Profile Photo Updated",1e3)}if(i&&i.dashboard_image&&((r=i.dashboard_image)!=null&&r.file)){let t=new FormData;t.append("file",(c=i.dashboard_image)==null?void 0:c.file);let m=await u.uploadImage(t);console.log("uploadResult"),console.log(m),o.dashboard_image=m.url,w("Profile Photo Updated",1e3)}const l=await u.updateProfile({first_name:o.first_name||(d==null?void 0:d.first_name),last_name:o.last_name||(d==null?void 0:d.last_name),phone:o.phone||(d==null?void 0:d.phone),photo:o.photo||S,dashboard_image:o.dashboard_image||v});if(!l.error)w("Profile Updated",4e3),P();else{if(l.validation){const t=Object.keys(l.validation);for(let m=0;m<t.length;m++){const p=t[m];L(p,{type:"manual",message:l.validation[p]})}}P()}if(z!==o.email){const t=await u.updateEmail(o.email);if(!t.error)w("Email Updated",1e3);else if(t.validation){const m=Object.keys(t.validation);for(let p=0;p<m.length;p++){const y=m[p];L(y,{type:"manual",message:t.validation[y]})}}P()}await C(),U(!1)}catch(l){U(!1),console.log("Error",l),L("email",{type:"manual",message:l.response.data.message?l.response.data.message:l.message}),I(l.response.data.message?l.response.data.message:l.message)}};R.useEffect(()=>{X({type:"SETPATH",payload:{path:"profile"}}),C()},[]);const P=()=>{N(o=>({...o,modal:"",showModal:!1}))};return R.useEffect(()=>{F("backpanel","headerType"),F("Profile","pageTitle"),["user"].includes(n==null?void 0:n.role)&&h("company_name",n==null?void 0:n.company_name)},[n==null?void 0:n.role]),a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"mx-auto flex h-full max-h-full min-h-full flex-col items-center justify-start gap-5 overflow-auto rounded bg-soft-200 !font-inter leading-snug tracking-wide shadow-md md:p-5",children:a.jsxs("form",{onSubmit:Z(oe),className:"!px-2 !w-full space-y-5 ",children:[a.jsxs(G,{children:[["user"].includes(n==null?void 0:n.role)&&a.jsxs("section",{className:"space-y-5",children:[a.jsx("div",{children:a.jsx($,{image:((O=i==null?void 0:i.dashboard_image)==null?void 0:O.tempURL)||ee||v,title:"Dashboard Logo",name:"dashboard_image",onUpload:M})}),a.jsx("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-4",children:a.jsx(j,{label:"Company",name:"company_name",register:_,errors:x,disabled:!0})}),a.jsx("hr",{})]}),a.jsxs("section",{className:"grid grid-cols-1 gap-[1.5rem] ",children:[a.jsxs(ce,{className:"!border-0 !p-0 !shadow-none ",children:[" ","User Details"," "]}),a.jsx("div",{children:a.jsx($,{image:((T=i==null?void 0:i.photo)==null?void 0:T.tempURL)||ae||S,title:"Profile Image",name:"photo",onUpload:M})}),a.jsxs("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-4",children:[a.jsx(j,{label:"First Name",name:"first_name",register:_,errors:x}),a.jsx(j,{label:"Last Name",name:"last_name",register:_,errors:x})]}),a.jsxs("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-4",children:[a.jsx(j,{label:"Phone",name:"phone",register:_,errors:x}),a.jsx(j,{label:"Email",name:"email",register:_,errors:x})]}),a.jsx(de,{children:a.jsx(he,{className:"!shadow w-fit !border-0 !bg-white font-[700] !text-black",onClick:()=>{N(o=>({...o,modal:"password",showModal:!0}))},children:"Change Password"})})]})]}),a.jsx(G,{className:"!bg-transparent !shadow-none !p-0",children:a.jsx(ie,{type:"submit",loading:E,disabled:E,className:"w-full rounded px-4 !py-2  font-bold !bg-[black] !shadow text-white md:w-[auto]",children:"Save Changes"})})]})}),a.jsx(pe,{isOpen:D.showModal&&["password"].includes(D.modal),onClose:P})]})};export{Be as default};
