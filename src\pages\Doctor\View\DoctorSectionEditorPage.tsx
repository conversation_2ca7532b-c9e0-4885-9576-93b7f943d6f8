import React, { useState, useEffect, useCallback } from "react";
import { Editor<PERSON>onte<PERSON>, useEditor, Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Highlight from "@tiptap/extension-highlight";
import { useNavigate } from "react-router-dom";
import MkdSDK from "../../../utils/MkdSDK";
import { useSDK } from "@/hooks/useSDK";

interface Section {
  id: number;
  title: string;
  body: string;
  order?: number;
  report_id?: string;
  created_at?: string;
  updated_at?: string;
}

interface Comment {
  id: string;
  text: string;
  created_at: string;
  user: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

interface SidePanel {
  type: "comments" | "cheatSheet" | "oldReport";
  isVisible: boolean;
}

interface CheatsheetSubsection {
  subtitle: string;
  content: string;
}

interface CheatsheetSection {
  title: string;
  isExpanded: boolean;
  subsections: CheatsheetSubsection[];
}

interface PreviousReportSection {
  title: string;
  content: string;
}

const Toolbar: React.FC<{ editor: Editor | null }> = ({ editor }) => {
  const [showNewChanges, setShowNewChanges] = useState(false);
  const [showOriginalAI, setShowOriginalAI] = useState(false);
  if (!editor) return null;
  return (
    <div className="w-full">
      {/* Main Toolbar */}
      <div className="flex items-center gap-2 border-b px-2 py-2 bg-white">
        {/* Bold */}
        <button
          type="button"
          aria-label="Bold"
          aria-pressed={editor.isActive("bold")}
          className={`px-2 py-1 rounded ${editor.isActive("bold") ? "font-bold text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <b>B</b>
        </button>
        {/* Italic */}
        <button
          type="button"
          aria-label="Italic"
          aria-pressed={editor.isActive("italic")}
          className={`px-2 py-1 rounded ${editor.isActive("italic") ? "italic text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <i>I</i>
        </button>
        {/* Underline */}
        <button
          type="button"
          aria-label="Underline"
          aria-pressed={editor.isActive("underline")}
          className={`px-2 py-1 rounded ${editor.isActive("underline") ? "underline text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleUnderline?.().run()}
          disabled={!editor.can().toggleUnderline?.()}
        >
          <u>U</u>
        </button>
        {/* List Buttons */}
        <button
          type="button"
          aria-label="Bulleted List"
          aria-pressed={editor.isActive("bulletList")}
          className={`px-2 py-1 rounded ${editor.isActive("bulletList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <circle cx="4" cy="5" r="1.5" fill="currentColor" />
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="9" r="1.5" fill="currentColor" />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="13" r="1.5" fill="currentColor" />
            <rect
              x="7"
              y="12"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Numbered List"
          aria-pressed={editor.isActive("orderedList")}
          className={`px-2 py-1 rounded ${editor.isActive("orderedList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <text x="2" y="7" fontSize="6" fill="currentColor">
              1.
            </text>
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <text x="2" y="13" fontSize="6" fill="currentColor">
              2.
            </text>
            <rect
              x="7"
              y="10"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        {/* Divider */}
        <span className="mx-2 border-l h-6" />
        {/* Alignment Buttons */}
        <button
          type="button"
          aria-label="Align Left"
          aria-pressed={editor.isActive({ textAlign: "left" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "left" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="3" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Center"
          aria-pressed={editor.isActive({ textAlign: "center" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "center" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="5" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Right"
          aria-pressed={editor.isActive({ textAlign: "right" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "right" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Justify"
          aria-pressed={editor.isActive({ textAlign: "justify" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "justify" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("justify").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="8"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      {/* Undo/Redo and Checkboxes */}
      <div className="flex items-center justify-between px-2 py-2">
        <div className="flex gap-2">
          <button
            type="button"
            aria-label="Undo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().undo().run()}
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M7 4L3 8L7 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Undo
          </button>
          <button
            type="button"
            aria-label="Redo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().redo().run()}
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M11 4L15 8L11 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Redo
          </button>
        </div>
        <div className="flex gap-6 items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-blue-100 border border-blue-300" />
              <span className="text-gray-500 text-sm">Original AI Text</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-gray-200 border border-gray-400" />
              <span className="text-gray-500 text-sm">New Changes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const DoctorSectionEditorPage: React.FC = () => {
  const navigate = useNavigate();
  const [sections, setSections] = useState<Section[]>([]);
  const [activeSection, setActiveSection] = useState(0);
  const [sidePanel, setSidePanel] = useState<SidePanel>({
    type: "comments",
    isVisible: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const { sdk } = useSDK();
  const [cheatsheetSections, setCheatsheetSections] = useState<
    CheatsheetSection[]
  >([]);
  const [isLoadingCheatsheet, setIsLoadingCheatsheet] = useState(false);
  const [cheatsheetError, setCheatsheetError] = useState<string | null>(null);
  const [previousReport, setPreviousReport] = useState<PreviousReportSection[]>(
    []
  );
  const [isLoadingPreviousReport, setIsLoadingPreviousReport] = useState(false);
  const [previousReportError, setPreviousReportError] = useState<string | null>(
    null
  );
  const [originalContent, setOriginalContent] = useState<string>("");

  const togglePanel = (type: SidePanel["type"]) => {
    setSidePanel((prev) => ({
      type: type,
      isVisible: prev.type === type ? !prev.isVisible : true,
    }));
  };

  // Fetch sections from backend
  useEffect(() => {
    const fetchSections = async () => {
      try {
        const reportId = localStorage.getItem("reportId");
        if (!reportId) {
          throw new Error("No report ID found");
        }

        const response = await sdk.getReportSections(reportId);

        if (response.error) {
          throw new Error(response.message || "Failed to fetch report data");
        }

        if (!response.sections || !Array.isArray(response.sections)) {
          throw new Error("Invalid response format");
        }

        // Sort sections by order if available
        const sortedSections = response.sections.sort(
          (a, b) => (a.order || 0) - (b.order || 0)
        );

        setSections(sortedSections);
        setError(null);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while fetching the report"
        );
        setSections([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSections();
  }, [sdk]);

  // Fetch comments for the current section
  useEffect(() => {
    const fetchComments = async () => {
      if (!sections[activeSection]) return;

      try {
        const response = await sdk.getSectionComments({
          report_section_id: sections[activeSection].id.toString(),
        });
        if (response.error) {
          throw new Error(response.message || "Failed to fetch comments");
        }
        setComments(response.data);
      } catch (err) {
        console.error("Error fetching comments:", err);
        setComments([]);
      }
    };

    fetchComments();
  }, [sdk, activeSection, sections]);

  // Initialize TipTap editor
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
      }),
      Underline,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      Highlight.configure({
        multicolor: true,
        HTMLAttributes: {
          class: "bg-gray-200",
        },
      }),
    ],
    content: sections[activeSection]?.body || "",
    onUpdate: ({ editor, transaction }) => {
      const currentContent = editor.getHTML();

      if (originalContent) {
        // Only process if there are actual changes
        if (transaction.docChanged) {
          // Get the current selection
          const { from, to } = editor.state.selection;

          // Apply highlight to the selected/changed text
          editor
            .chain()
            .focus()
            .setTextSelection({ from, to })
            .setHighlight({ color: "#D1D5DB" })
            .run();
        }
      }
    },
    onBlur: ({ editor }) => {
      const html = editor.getHTML();
      setSections((prev) =>
        prev.map((section, idx) =>
          idx === activeSection ? { ...section, body: html } : section
        )
      );
    },
    editorProps: {
      attributes: {
        class:
          "prose prose-sm max-w-none min-h-[600px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",
        spellCheck: "true",
        "aria-label": `Edit ${sections[activeSection]?.title || ""}`,
      },
    },
    parseOptions: { preserveWhitespace: "full" },
  });

  // When activeSection changes, update editor content and original content
  useEffect(() => {
    if (editor && sections[activeSection]) {
      editor.commands.setContent(sections[activeSection].body || "", false);
      // Update original content from localStorage
      const storedSections = localStorage.getItem("reportSections");
      if (storedSections) {
        const parsedSections = JSON.parse(storedSections);
        setOriginalContent(parsedSections[activeSection]?.body || "");
      }
    }
  }, [activeSection, editor, sections]);

  // Navigation handlers
  const goToSection = useCallback(
    (idx: number) => {
      if (idx >= 0 && idx < sections.length) setActiveSection(idx);
    },
    [sections.length]
  );

  const handleSaveChanges = async () => {
    try {
      setIsSaving(true);
      const reportId = localStorage.getItem("reportId");
      if (!reportId) {
        throw new Error("No report ID found");
      }

      // Save each section
      for (const section of sections) {
        await sdk.updateReportSection({
          id: section.id.toString(),
          title: section.title,
          content: section.body,
        });
      }

      // Show success message or handle as needed
      console.log("Changes saved successfully");
    } catch (err) {
      console.error("Error saving changes:", err);
      // Handle error appropriately
    } finally {
      setIsSaving(false);
    }
  };

  const handleProceed = () => {
    localStorage.setItem("reportSections", JSON.stringify(sections));
    navigate("/doctor/report-overview");
  };

  // Add fetchCheatsheet function
  const fetchCheatsheet = async () => {
    try {
      setIsLoadingCheatsheet(true);
      setCheatsheetError(null);
      const response = await sdk.getDoctorCheatsheet();

      if (response.error) {
        throw new Error(response.message || "Failed to fetch cheatsheet");
      }

      if (!response.data || !Array.isArray(response.data.sections)) {
        throw new Error("Invalid response format from server");
      }

      const transformedSections = response.data.sections.map(
        (section: any, index: number) => ({
          ...section,
          isExpanded: index === 0,
          subsections: Array.isArray(section.subsections)
            ? section.subsections
            : [],
        })
      );

      setCheatsheetSections(transformedSections);
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || err.message || "An error occurred";
      setCheatsheetError(errorMessage);
      setCheatsheetSections([]);
    } finally {
      setIsLoadingCheatsheet(false);
    }
  };

  // Add useEffect to fetch cheatsheet when panel is opened
  useEffect(() => {
    if (sidePanel.type === "cheatSheet" && sidePanel.isVisible) {
      fetchCheatsheet();
    }
  }, [sidePanel.type, sidePanel.isVisible]);

  // Add fetchPreviousReport function
  const fetchPreviousReport = async () => {
    try {
      setIsLoadingPreviousReport(true);
      setPreviousReportError(null);
      const reportId = localStorage.getItem("reportId");
      if (!reportId) {
        throw new Error("No report ID found");
      }

      const response = await sdk.getPreviousReport({ project_id: reportId });
      if (response.error) {
        throw new Error(response.message || "Failed to fetch previous report");
      }

      setPreviousReport(response.data || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An error occurred";
      setPreviousReportError(errorMessage);
      setPreviousReport([]);
    } finally {
      setIsLoadingPreviousReport(false);
    }
  };

  // Add useEffect to fetch previous report when panel is opened
  useEffect(() => {
    if (sidePanel.type === "oldReport" && sidePanel.isVisible) {
      fetchPreviousReport();
    }
  }, [sidePanel.type, sidePanel.isVisible]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-red-600">Error loading report: {error}</div>
      </div>
    );
  }

  if (!sections.length) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-gray-600">No sections available</div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="flex-1 p-6">
        {/* Top Navigation */}
        <div className="justify-between items-center mb-6">
          <div className="flex mb-2 justify-end items-center gap-4">
            <button
              onClick={() => togglePanel("comments")}
              className="flex border-2 items-center gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="14"
                viewBox="0 0 18 14"
                fill="none"
              >
                <g clip-path="url(#clip0_1_2300)">
                  <path
                    d="M2.61483 8.45195C2.8828 7.95156 2.80077 7.33633 2.40976 6.92617C1.82733 6.31367 1.51562 5.57812 1.51562 4.8125C1.51562 3.07617 3.26015 1.3125 5.89062 1.3125C8.52108 1.3125 10.2656 3.07617 10.2656 4.8125C10.2656 6.54883 8.52108 8.3125 5.89062 8.3125C5.53241 8.3125 5.18515 8.27695 4.85702 8.21406C4.57265 8.15938 4.27733 8.19766 4.01757 8.32891C3.90546 8.38633 3.79062 8.44102 3.67304 8.49297C3.23554 8.68984 2.77343 8.86211 2.30858 8.98516C2.38515 8.85938 2.45624 8.73633 2.5246 8.61328C2.55468 8.56133 2.58476 8.50664 2.6121 8.45195H2.61483ZM0.203116 4.8125C0.203116 5.95547 0.673428 7.00273 1.45819 7.82852C1.43358 7.875 1.40624 7.92422 1.38163 7.96797C1.09999 8.47109 0.771866 8.96602 0.38085 9.39258C0.200382 9.58398 0.153897 9.86289 0.255069 10.1008C0.36171 10.3441 0.596866 10.5 0.859366 10.5C2.03515 10.5 3.2246 10.1363 4.21444 9.68789C4.34569 9.62773 4.47694 9.56484 4.60273 9.50195C5.01562 9.58398 5.44765 9.625 5.89062 9.625C9.03241 9.625 11.5781 7.47031 11.5781 4.8125C11.5781 2.15469 9.03241 0 5.89062 0C2.74882 0 0.203116 2.15469 0.203116 4.8125ZM12.0156 13.125C12.4586 13.125 12.8879 13.0813 13.3035 13.002C13.4293 13.0648 13.5605 13.1277 13.6918 13.1879C14.6816 13.6363 15.8711 14 17.0469 14C17.3094 14 17.5445 13.8441 17.6484 13.6035C17.7523 13.3629 17.7031 13.084 17.5226 12.8953C17.1344 12.4688 16.8062 11.9738 16.5219 11.4707C16.4973 11.4242 16.4699 11.3777 16.4453 11.3313C17.2328 10.5027 17.7031 9.45547 17.7031 8.3125C17.7031 5.73125 15.2996 3.62305 12.2836 3.50547C12.3957 3.92109 12.4531 4.35859 12.4531 4.8125V4.82891C14.8375 5.01211 16.3906 6.67461 16.3906 8.3125C16.3906 9.07812 16.0789 9.81367 15.4965 10.4234C15.1055 10.8336 15.0234 11.4516 15.2914 11.9492C15.3215 12.0039 15.3516 12.0586 15.3789 12.1105C15.4473 12.2336 15.5211 12.3566 15.5949 12.4824C15.1301 12.3594 14.668 12.1898 14.2305 11.9902C14.1129 11.9383 13.998 11.8836 13.8859 11.8262C13.6262 11.6949 13.3309 11.6566 13.0465 11.7113C12.7156 11.777 12.3711 11.8098 12.0129 11.8098C10.3258 11.8098 9.00507 11.0852 8.27226 10.1062C7.83476 10.2539 7.37538 10.3633 6.90507 10.4289C7.83202 12.0258 9.77343 13.125 12.0156 13.125Z"
                    fill="#374151"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_2300">
                    <path d="M0.203125 0H17.7031V14H0.203125V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              {sidePanel.type === "comments" && sidePanel.isVisible
                ? "Hide"
                : "View"}{" "}
              Comments
            </button>
            <button
              onClick={() => togglePanel("cheatSheet")}
              className="flex items-center border-2 gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="14"
                viewBox="0 0 15 14"
                fill="none"
              >
                <g clip-path="url(#clip0_1_2260)">
                  <path
                    d="M4.84651 1.04438C5.11721 1.28774 5.13909 1.70063 4.89573 1.97133L2.92698 4.15883C2.80667 4.29282 2.63713 4.37211 2.45667 4.37485C2.2762 4.37758 2.10393 4.30922 1.97541 4.18344L0.878931 3.08969C0.624634 2.83266 0.624634 2.41703 0.878931 2.16C1.13323 1.90297 1.55159 1.90297 1.80588 2.16L2.41018 2.7643L3.91682 1.09086C4.16018 0.820159 4.57307 0.798284 4.84377 1.04164L4.84651 1.04438ZM4.84651 5.41938C5.11721 5.66274 5.13909 6.07563 4.89573 6.34633L2.92698 8.53383C2.80667 8.66782 2.63713 8.74711 2.45667 8.74985C2.2762 8.75258 2.10393 8.68422 1.97541 8.55844L0.878931 7.46469C0.621899 7.20766 0.621899 6.79203 0.878931 6.53774C1.13596 6.28344 1.55159 6.28071 1.80588 6.53774L2.41018 7.14203L3.91682 5.4686C4.16018 5.19789 4.57307 5.17602 4.84377 5.41938H4.84651ZM6.81252 2.62485C6.81252 2.14086 7.20354 1.74985 7.68752 1.74985H13.8125C14.2965 1.74985 14.6875 2.14086 14.6875 2.62485C14.6875 3.10883 14.2965 3.49985 13.8125 3.49985H7.68752C7.20354 3.49985 6.81252 3.10883 6.81252 2.62485ZM6.81252 6.99985C6.81252 6.51586 7.20354 6.12485 7.68752 6.12485H13.8125C14.2965 6.12485 14.6875 6.51586 14.6875 6.99985C14.6875 7.48383 14.2965 7.87485 13.8125 7.87485H7.68752C7.20354 7.87485 6.81252 7.48383 6.81252 6.99985ZM5.06252 11.3748C5.06252 10.8909 5.45354 10.4998 5.93752 10.4998H13.8125C14.2965 10.4998 14.6875 10.8909 14.6875 11.3748C14.6875 11.8588 14.2965 12.2498 13.8125 12.2498H5.93752C5.45354 12.2498 5.06252 11.8588 5.06252 11.3748ZM2.00002 10.0623C2.34812 10.0623 2.68196 10.2006 2.9281 10.4468C3.17424 10.6929 3.31252 11.0267 3.31252 11.3748C3.31252 11.7229 3.17424 12.0568 2.9281 12.3029C2.68196 12.5491 2.34812 12.6873 2.00002 12.6873C1.65193 12.6873 1.31809 12.5491 1.07195 12.3029C0.825805 12.0568 0.687524 11.7229 0.687524 11.3748C0.687524 11.0267 0.825805 10.6929 1.07195 10.4468C1.31809 10.2006 1.65193 10.0623 2.00002 10.0623Z"
                    fill="#374151"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_2260">
                    <path d="M0.6875 0H14.6875V14H0.6875V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              Show Cheat Sheet
            </button>
            <button
              onClick={() => togglePanel("oldReport")}
              className="flex items-center border-2 gap-2 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="14"
                viewBox="0 0 12 14"
                fill="none"
              >
                <g clip-path="url(#clip0_1_2265)">
                  <path
                    d="M2.65625 12.6875C2.41562 12.6875 2.21875 12.4906 2.21875 12.25V1.75C2.21875 1.50937 2.41562 1.3125 2.65625 1.3125H7.03125V3.5C7.03125 3.98398 7.42227 4.375 7.90625 4.375H10.0938V12.25C10.0938 12.4906 9.89688 12.6875 9.65625 12.6875H2.65625ZM2.65625 0C1.69102 0 0.90625 0.784766 0.90625 1.75V12.25C0.90625 13.2152 1.69102 14 2.65625 14H9.65625C10.6215 14 11.4062 13.2152 11.4062 12.25V4.22461C11.4062 3.75977 11.223 3.31406 10.8949 2.98594L8.41758 0.511328C8.08945 0.183203 7.64648 0 7.18164 0H2.65625ZM4.1875 7C3.82383 7 3.53125 7.29258 3.53125 7.65625C3.53125 8.01992 3.82383 8.3125 4.1875 8.3125H8.125C8.48867 8.3125 8.78125 8.01992 8.78125 7.65625C8.78125 7.29258 8.48867 7 8.125 7H4.1875ZM4.1875 9.625C3.82383 9.625 3.53125 9.91758 3.53125 10.2812C3.53125 10.6449 3.82383 10.9375 4.1875 10.9375H8.125C8.48867 10.9375 8.78125 10.6449 8.78125 10.2812C8.78125 9.91758 8.48867 9.625 8.125 9.625H4.1875Z"
                    fill="#374151"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_2265">
                    <path d="M0.90625 0H11.4062V14H0.90625V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              Show Old Report
            </button>
            <button
              onClick={handleSaveChanges}
              disabled={isSaving}
              className="px-4 flex items-center gap-2 py-2 bg-gray-800 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="13"
                    height="14"
                    viewBox="0 0 13 14"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_1_2303)">
                      <path
                        d="M2.03125 2.625V11.375C2.03125 11.6156 2.22812 11.8125 2.46875 11.8125H11.2188C11.4594 11.8125 11.6562 11.6156 11.6562 11.375V4.66211C11.6562 4.54727 11.6098 4.43516 11.5277 4.35312L12.4547 3.42617C12.7828 3.7543 12.966 4.2 12.966 4.66484V11.375C12.966 12.3402 12.1812 13.125 11.216 13.125H2.46875C1.50352 13.125 0.71875 12.3402 0.71875 11.375V2.625C0.71875 1.65977 1.50352 0.875 2.46875 0.875H9.18164C9.64648 0.875 10.0922 1.0582 10.4203 1.38633L12.4574 3.42344L11.5305 4.35039L9.49062 2.31602C9.48242 2.30781 9.47695 2.30234 9.46875 2.29414V5.03125C9.46875 5.39492 9.17617 5.6875 8.8125 5.6875H3.5625C3.19883 5.6875 2.90625 5.39492 2.90625 5.03125V2.1875H2.46875C2.22812 2.1875 2.03125 2.38437 2.03125 2.625ZM4.21875 2.1875V4.375H8.15625V2.1875H4.21875ZM5.09375 8.75C5.09375 8.28587 5.27812 7.84075 5.60631 7.51256C5.9345 7.18437 6.37962 7 6.84375 7C7.30788 7 7.753 7.18437 8.08119 7.51256C8.40938 7.84075 8.59375 8.28587 8.59375 8.75C8.59375 9.21413 8.40938 9.65925 8.08119 9.98744C7.753 10.3156 7.30788 10.5 6.84375 10.5C6.37962 10.5 5.9345 10.3156 5.60631 9.98744C5.27812 9.65925 5.09375 9.21413 5.09375 8.75Z"
                        fill="white"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_1_2303">
                        <path
                          d="M0.71875 0H12.9688V14H0.71875V0Z"
                          fill="white"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                  Save Changes
                </>
              )}
            </button>
            <button
              onClick={handleProceed}
              className="px-4 flex items-center gap-2 py-2 bg-violet-600 text-white rounded-md"
            >
              Proceed
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="13"
                height="14"
                viewBox="0 0 13 14"
                fill="none"
              >
                <g clipPath="url(#clip0_1_2306)">
                  <path
                    d="M12.743 7.61807C13.0848 7.27627 13.0848 6.72119 12.743 6.3794L8.36797 2.00439C8.02617 1.6626 7.47109 1.6626 7.1293 2.00439C6.7875 2.34619 6.7875 2.90127 7.1293 3.24307L10.0141 6.1251H1.625C1.14102 6.1251 0.75 6.51611 0.75 7.0001C0.75 7.48408 1.14102 7.8751 1.625 7.8751H10.0113L7.13203 10.7571C6.79023 11.0989 6.79023 11.654 7.13203 11.9958C7.47383 12.3376 8.02891 12.3376 8.3707 11.9958L12.7457 7.6208L12.743 7.61807Z"
                    fill="white"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_2306">
                    <path d="M0.75 0H13V14H0.75V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </button>
          </div>

          <div className="flex mt-4 justify-center items-center gap-2">
            <button
              onClick={() => goToSection(activeSection - 1)}
              disabled={activeSection === 0}
              className="p-2 border rounded-full"
              aria-label="Previous section"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="16"
                viewBox="0 0 15 16"
                fill="none"
                style={{ rotate: "180deg" }}
              >
                <g clip-path="url(#clip0_1_2279)">
                  <path
                    d="M13.7219 8.70615C14.1125 8.31553 14.1125 7.68115 13.7219 7.29053L8.72188 2.29053C8.33125 1.8999 7.69688 1.8999 7.30625 2.29053C6.91563 2.68115 6.91563 3.31553 7.30625 3.70615L10.6031 6.9999H1.01562C0.4625 6.9999 0.015625 7.44678 0.015625 7.9999C0.015625 8.55303 0.4625 8.9999 1.01562 8.9999H10.6L7.30937 12.2937C6.91875 12.6843 6.91875 13.3187 7.30937 13.7093C7.7 14.0999 8.33438 14.0999 8.725 13.7093L13.725 8.70928L13.7219 8.70615Z"
                    fill="#4B5563"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_2279">
                    <path d="M0.015625 0H14.0156V16H0.015625V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </button>
            <div className="min-w-[300px] flex items-center justify-center">
              <h1 className="text-xl mx-3 font-semibold">
                {sections[activeSection]?.title}
              </h1>
            </div>
            <button
              onClick={() => goToSection(activeSection + 1)}
              disabled={activeSection === sections.length - 1}
              className="p-2 border rounded-full"
              aria-label="Next section"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="15"
                height="16"
                viewBox="0 0 15 16"
                fill="none"
              >
                <g clip-path="url(#clip0_1_2279)">
                  <path
                    d="M13.7219 8.70615C14.1125 8.31553 14.1125 7.68115 13.7219 7.29053L8.72188 2.29053C8.33125 1.8999 7.69688 1.8999 7.30625 2.29053C6.91563 2.68115 6.91563 3.31553 7.30625 3.70615L10.6031 6.9999H1.01562C0.4625 6.9999 0.015625 7.44678 0.015625 7.9999C0.015625 8.55303 0.4625 8.9999 1.01562 8.9999H10.6L7.30937 12.2937C6.91875 12.6843 6.91875 13.3187 7.30937 13.7093C7.7 14.0999 8.33438 14.0999 8.725 13.7093L13.725 8.70928L13.7219 8.70615Z"
                    fill="#4B5563"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_2279">
                    <path d="M0.015625 0H14.0156V16H0.015625V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </button>
          </div>
        </div>
        {/* Editor Toolbar */}
        <Toolbar editor={editor} />
        {/* Editor Content */}
        <div className="bg-white p-6 rounded-lg min-h-[600px]">
          <EditorContent editor={editor} />
        </div>
      </div>
      {/* Side Panel */}
      {sidePanel.isVisible && (
        <div className="w-96 bg-white border-l border-gray-200 p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900">
              {sidePanel.type === "comments" && "Section Comments"}
              {sidePanel.type === "cheatSheet" && "Cheat Sheet"}
              {sidePanel.type === "oldReport" && "Previous Report"}
            </h2>
            <button
              onClick={() =>
                setSidePanel((prev) => ({ ...prev, isVisible: false }))
              }
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close side panel"
            >
              ×
            </button>
          </div>
          {sidePanel.type === "comments" && (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.id} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-violet-100 rounded-full flex items-center justify-center">
                      {comment.user.name[0]}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {comment.user.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(comment.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-700">{comment.text}</p>
                </div>
              ))}
            </div>
          )}
          {sidePanel.type === "cheatSheet" && (
            <div className="space-y-4">
              {isLoadingCheatsheet ? (
                <div className="flex items-center justify-center p-4">
                  <div className="w-6 h-6 border-2 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
                </div>
              ) : cheatsheetError ? (
                <div className="text-red-600 p-4 text-center">
                  {cheatsheetError}
                  <button
                    onClick={fetchCheatsheet}
                    className="block mx-auto mt-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                  >
                    Retry
                  </button>
                </div>
              ) : cheatsheetSections.length === 0 ? (
                <div className="text-gray-500 p-4 text-center">
                  No cheatsheet data available
                </div>
              ) : (
                cheatsheetSections.map((section, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">
                      {section.title}
                    </h3>
                    {section.subsections.map((subsection, subIndex) => (
                      <div key={subIndex} className="mb-2">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">
                          {subsection.subtitle}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {subsection.content}
                        </p>
                      </div>
                    ))}
                  </div>
                ))
              )}
            </div>
          )}
          {sidePanel.type === "oldReport" && (
            <div className="space-y-4">
              {isLoadingPreviousReport ? (
                <div className="flex items-center justify-center p-4">
                  <div className="w-6 h-6 border-2 border-gray-200 border-t-gray-800 rounded-full animate-spin" />
                </div>
              ) : previousReportError ? (
                <div className="text-red-600 p-4 text-center">
                  {previousReportError}
                  <button
                    onClick={fetchPreviousReport}
                    className="block mx-auto mt-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                  >
                    Retry
                  </button>
                </div>
              ) : previousReport.length === 0 ? (
                <div className="text-gray-500 p-4 text-center">
                  No previous report available
                </div>
              ) : (
                previousReport.map((section, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">
                      {section.title}
                    </h3>
                    <p className="text-sm text-gray-600">{section.content}</p>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DoctorSectionEditorPage;
