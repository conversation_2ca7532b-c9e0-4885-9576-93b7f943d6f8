import React, { useContext } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { GlobalContext } from "@/context/Global";
import { AuthContext } from "@/context/Auth";
import { useProfile } from "@/hooks/useProfile";
import CircularImagePreview from "@/components/CircularImagePreview/CircularImagePreview";

const DOCTOR_NAV_ITEMS = [
  {
    to: "/doctor/projects",
    label: "View Projects",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
      >
        <path d="M16 16H0V0H16V16Z" stroke="#E5E7EB" />
        <path
          d="M0 3C0 1.89688 0.896875 1 2 1H6.12813C6.725 1 7.29688 1.2375 7.71875 1.65937L9.05937 3H14C15.1031 3 16 3.89687 16 5V13C16 14.1031 15.1031 15 14 15H2C0.896875 15 0 14.1031 0 13V3ZM2 2.5C1.725 2.5 1.5 2.725 1.5 3V13C1.5 13.275 1.725 13.5 2 13.5H14C14.275 13.5 14.5 13.275 14.5 13V5C14.5 4.725 14.275 4.5 14 4.5H8.95625C8.625 4.5 8.30625 4.36875 8.07187 4.13438L6.65938 2.71875C6.51875 2.57812 6.32812 2.5 6.12813 2.5H2Z"
          fill="#7C3AED"
        />
      </svg>
    ),
  },
  {
    to: "/doctor/profile",
    label: "My Profile",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="14"
        height="16"
        viewBox="0 0 14 16"
        fill="none"
      >
        <g clip-path="url(#clip0_1_1504)">
          <path
            d="M9.5 4C9.5 3.33696 9.23661 2.70107 8.76777 2.23223C8.29893 1.76339 7.66304 1.5 7 1.5C6.33696 1.5 5.70107 1.76339 5.23223 2.23223C4.76339 2.70107 4.5 3.33696 4.5 4C4.5 4.66304 4.76339 5.29893 5.23223 5.76777C5.70107 6.23661 6.33696 6.5 7 6.5C7.66304 6.5 8.29893 6.23661 8.76777 5.76777C9.23661 5.29893 9.5 4.66304 9.5 4ZM3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4ZM1.54062 14.5H12.4594C12.1813 12.5219 10.4813 11 8.42813 11H5.57188C3.51875 11 1.81875 12.5219 1.54062 14.5ZM0 15.0719C0 11.9937 2.49375 9.5 5.57188 9.5H8.42813C11.5063 9.5 14 11.9937 14 15.0719C14 15.5844 13.5844 16 13.0719 16H0.928125C0.415625 16 0 15.5844 0 15.0719Z"
            fill="#4B5563"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_1504">
            <path d="M0 0H14V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  },
  // {
  //   to: "/doctor/settings",
  //   label: "Settings",
  //   icon: (
  //     <svg
  //       xmlns="http://www.w3.org/2000/svg"
  //       width="16"
  //       height="16"
  //       viewBox="0 0 16 16"
  //       fill="none"
  //     >
  //       <g clip-path="url(#clip0_1_1509)">
  //         <path
  //           d="M4.89062 13.9906L4.49687 14.9125C3.9125 14.6156 3.375 14.25 2.8875 13.8219L3.59687 13.1125C3.9875 13.4531 4.42188 13.75 4.89062 13.9906ZM1.26875 8.5H0.265625C0.309375 9.1625 0.434375 9.80313 0.63125 10.4094L1.5625 10.0375C1.40937 9.54688 1.30625 9.03125 1.26875 8.5ZM1.26875 7.5C1.3125 6.9125 1.43125 6.34375 1.61563 5.80937L0.69375 5.41563C0.459375 6.07188 0.3125 6.77187 0.265625 7.5H1.26875ZM2.00938 4.89062C2.25313 4.425 2.54688 3.99062 2.8875 3.59375L2.17812 2.88438C1.75 3.37188 1.38125 3.90938 1.0875 4.49375L2.00938 4.89062ZM12.4062 13.1125C11.9719 13.4875 11.4875 13.8094 10.9656 14.0625L11.3375 14.9937C11.9844 14.6844 12.5813 14.2875 13.1156 13.8188L12.4062 13.1125ZM3.59375 2.8875C4.02813 2.5125 4.5125 2.19062 5.03438 1.9375L4.6625 1.00625C4.01562 1.31562 3.41875 1.7125 2.8875 2.18125L3.59375 2.8875ZM13.9906 11.1094C13.7469 11.575 13.4531 12.0094 13.1125 12.4062L13.8219 13.1156C14.25 12.6281 14.6188 12.0875 14.9125 11.5063L13.9906 11.1094ZM14.7312 8.5C14.6875 9.0875 14.5687 9.65625 14.3844 10.1906L15.3062 10.5844C15.5406 9.925 15.6875 9.225 15.7312 8.49687H14.7312V8.5ZM10.0375 14.4375C9.54688 14.5938 9.03125 14.6937 8.5 14.7312V15.7344C9.1625 15.6906 9.80313 15.5656 10.4094 15.3687L10.0375 14.4375ZM7.5 14.7312C6.9125 14.6875 6.34375 14.5687 5.80937 14.3844L5.41563 15.3062C6.075 15.5406 6.775 15.6875 7.50313 15.7312V14.7312H7.5ZM14.4375 5.9625C14.5938 6.45312 14.6937 6.96875 14.7312 7.5H15.7344C15.6906 6.8375 15.5656 6.19687 15.3687 5.59062L14.4375 5.9625ZM2.8875 12.4062C2.5125 11.9719 2.19062 11.4875 1.9375 10.9656L1.00625 11.3375C1.31562 11.9844 1.7125 12.5813 2.18125 13.1156L2.8875 12.4062ZM8.5 1.26875C9.0875 1.3125 9.65312 1.43125 10.1906 1.61563L10.5844 0.69375C9.92813 0.459375 9.22812 0.3125 8.5 0.265625V1.26875ZM5.9625 1.5625C6.45312 1.40625 6.96875 1.30625 7.5 1.26875V0.265625C6.8375 0.309375 6.19687 0.434375 5.59062 0.63125L5.9625 1.5625ZM13.8219 2.88438L13.1125 3.59375C13.4875 4.02813 13.8094 4.5125 14.0656 5.03438L14.9969 4.6625C14.6875 4.01562 14.2906 3.41875 13.8219 2.88438ZM12.4062 2.8875L13.1156 2.17812C12.6281 1.75 12.0906 1.38125 11.5063 1.0875L11.1125 2.00938C11.575 2.25313 12.0125 2.54688 12.4062 2.8875Z"
  //           fill="#4B5563"
  //         />
  //         <path
  //           d="M8 12.25C8.48325 12.25 8.875 11.8582 8.875 11.375C8.875 10.8918 8.48325 10.5 8 10.5C7.51675 10.5 7.125 10.8918 7.125 11.375C7.125 11.8582 7.51675 12.25 8 12.25Z"
  //           fill="#4B5563"
  //         />
  //         <path
  //           d="M8.24069 9.75H7.74069C7.53444 9.75 7.36569 9.58125 7.36569 9.375C7.36569 7.15625 9.78444 7.37812 9.78444 6.00625C9.78444 5.38125 9.22819 4.75 7.99069 4.75C7.08132 4.75 6.60632 5.05 6.14069 5.64687C6.01882 5.80312 5.79382 5.83438 5.63444 5.72188L5.22507 5.43437C5.05007 5.3125 5.00944 5.06562 5.14382 4.89687C5.80632 4.04687 6.59382 3.5 7.99382 3.5C9.62819 3.5 11.0376 4.43125 11.0376 6.00625C11.0376 8.11875 8.61882 7.99062 8.61882 9.375C8.61569 9.58125 8.44694 9.75 8.24069 9.75Z"
  //           fill="#4B5563"
  //         />
  //       </g>
  //       <defs>
  //         <clipPath id="clip0_1_1509">
  //           <path d="M0 0H16V16H0V0Z" fill="white" />
  //         </clipPath>
  //       </defs>
  //     </svg>
  //   ),
  // },
];

export const DoctorHeader: React.FC = () => {
  const {
    state: { isOpen },
    dispatch: globalDispatch,
  } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { profile } = useProfile();
  const navigate = useNavigate();
  // Fallbacks

  console.log(profile, "profile");
  const name =
    profile?.firstName && profile?.lastName
      ? `Dr. ${profile.firstName} ${profile.lastName}`
      : profile?.email;
  const specialty = profile?.specialty || "Clinical Psychologist";
  const photo =
    profile?.photo || "https://randomuser.me/api/portraits/men/32.jpg";

  // Handle logout
  const handleLogout = () => {
    authDispatch({ type: "LOGOUT" });
    navigate("/doctor/login");
  };

  return (
    <nav
      className="flex flex-col flex-shrink-0 justify-between items-start w-64 h-screen border-r border-r-[#e5e7eb] bg-white"
      aria-label="Sidebar"
    >
      {/* Header Section */}
      <div className="flex justify-center items-center w-full border-b border-b-[#e5e7eb] px-4 py-6">
        <div className="flex-shrink-0">
          <CircularImagePreview image={photo} className="h-12 w-12" />
        </div>
        <div className="flex flex-col items-start text-base font-semibold text-gray-900 w-full truncate ml-2">
          <div className="text-base font-semibold text-gray-900 w-full truncate">
            {name}
          </div>
          <div className="text-xs text-gray-400 w-full truncate">
            {specialty}
          </div>
        </div>
      </div>
      {/* Navigation Section */}
      <div className="flex flex-col gap-1 w-full mt-4">
        {DOCTOR_NAV_ITEMS.map((item) => (
          <NavLink
            key={item.to}
            to={item.to}
            className={({ isActive }) =>
              `flex items-center mb-1 gap-3 py-3 px-4 rounded-lg transition-colors w-full text-sm font-medium ${isActive ? "bg-violet-50 text-violet-700" : "text-gray-700 hover:bg-gray-50"}`
            }
            aria-label={item.label}
          >
            {item.icon}
            <span>{item.label}</span>
          </NavLink>
        ))}
      </div>
      {/* Footer/Logout Section */}
      <div className="w-full border-t border-t-[#e5e7eb] flex items-center px-4 py-4 mt-auto">
        <button
          onClick={handleLogout}
          className="flex items-center gap-2 text-gray-500 hover:text-gray-900 focus:outline-none text-sm font-medium"
          aria-label="Logout"
        >
          <svg width={18} height={18} fill="none" viewBox="0 0 18 18">
            <path
              d="M9 1.5A7.5 7.5 0 1 1 1.5 9"
              stroke="#A1A1AA"
              strokeWidth="1.2"
            />
            <path
              d="M6.75 9l2.25 2.25L11.25 9"
              stroke="#A1A1AA"
              strokeWidth="1.2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span>Logout</span>
        </button>
      </div>
    </nav>
  );
};
