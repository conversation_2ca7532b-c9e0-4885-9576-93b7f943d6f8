import{j as p}from"./@react-google-maps/api-5b2d83cc.js";import{r as b}from"./vendor-489b60f1.js";const x="_button_3urdt_1",w={button:x},g=({onClick:t,children:n="Add New",showPlus:r=!1,className:o,title:s,disabled:a=!1,showChildren:i=!0,type:l="button",loading:m=!1,icon:u=null,animation:d=!0})=>{const[f,e]=b.useState(!1),c=()=>{t&&t(),d&&e(!0)};return p.jsxs("button",{type:l,title:s,disabled:a,onAnimationEnd:()=>e(!1),onClick:c,className:`${f&&"animate-wiggle"} ${w.button} relative flex h-[2.125rem] w-fit min-w-fit  items-center justify-center overflow-hidden rounded-md border border-primary bg-primary px-[.6125rem]  py-[.5625rem] font-['Inter'] text-sm font-medium leading-none text-white shadow-md shadow-primary  ${o}`,children:[m?null:u,r?"+":null," ",i?n:null]})};export{g as default};
