import{g as n,s as r}from"../react-hook-form-7e42b371.js";const c=(t,o,e)=>{if(t&&"reportValidity"in t){const s=n(e,o);t.setCustomValidity(s&&s.message||""),t.reportValidity()}},l=(t,o)=>{for(const e in o.fields){const s=o.fields[e];s&&s.ref&&"reportValidity"in s.ref?c(s.ref,e,t):s.refs&&s.refs.forEach(i=>c(i,e,t))}},g=(t,o)=>{o.shouldUseNativeValidation&&l(t,o);const e={};for(const s in t){const i=n(o.fields,s),f=Object.assign(t[s]||{},{ref:i&&i.ref});if(d(o.names||Object.keys(t),s)){const a=Object.assign({},n(e,s));r(a,"root",f),r(e,s,a)}else r(e,s,f)}return e},d=(t,o)=>t.some(e=>e.startsWith(o+"."));export{l as o,g as r};
