var ze=Object.defineProperty;var Ge=(r,e,t)=>e in r?ze(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var I=(r,e,t)=>(Ge(r,typeof e!="symbol"?e+"":e,t),t);import{j as a}from"./@react-google-maps/api-5b2d83cc.js";import{a as Be,r as i,R as M,N as _e,c as q,u as fe,d as te,f as be,h as He,i as y,B as Fe}from"./vendor-489b60f1.js";import{a as _}from"./html2pdf.js-82514bbc.js";import{q as le,_ as de}from"./@headlessui/react-15af3249.js";import{S as Qe}from"./react-loading-skeleton-b6c0da5e.js";import{l as We}from"./@stripe/stripe-js-6b714a86.js";import{E as Ke}from"./@stripe/react-stripe-js-209b94d8.js";import{Q as Ze,a as Je}from"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import{o as Xe}from"./@hotjar/browser-b90112fa.js";import{l as Ye}from"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import{i as et}from"./@fortawesome/free-solid-svg-icons-82da594a.js";import{i as tt}from"./@fortawesome/free-regular-svg-icons-a38012c9.js";import{i as rt}from"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const n of s)if(n.type==="childList")for(const p of n.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&o(p)}).observe(document,{childList:!0,subtree:!0});function t(s){const n={};return s.integrity&&(n.integrity=s.integrity),s.referrerPolicy&&(n.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?n.credentials="include":s.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function o(s){if(s.ep)return;s.ep=!0;const n=t(s);fetch(s.href,n)}})();var pe={},ye=Be;pe.createRoot=ye.createRoot,pe.hydrateRoot=ye.hydrateRoot;function L(r){return r===""||r===null||r===void 0||r==="undefined"}class re{stringCaser(e,t={}){if(!e||typeof e!="string")return"";const{separator:o,casetype:s="lowercase",exclude:n}=t;let d=(n!=null&&n.length?e.replace(new RegExp(`[^a-zA-Z0-9${n.join("")}]`,"g")," "):e.replace(/[^a-zA-Z0-9]/g," ")).split(/[\s_-]/).filter(Boolean);switch(s){case"UPPERCASE":d=d.map(l=>l.toUpperCase());break;case"lowercase":d=d.map(l=>l.toLowerCase());break;case"Capitalize":d=d.map(l=>l.charAt(0).toUpperCase()+l.slice(1).toLowerCase());break;case"camelCase":d=d.map((l,c)=>c===0?l.toLowerCase():l.charAt(0).toUpperCase()+l.slice(1).toLowerCase());break;case"PascalCase":d=d.map(l=>l.charAt(0).toUpperCase()+l.slice(1).toLowerCase());break}return o==="space"?d.join(" "):o?d.join(o):d.join("")}UPPERCASE(e,t={exclude:[],separator:""}){return this.stringCaser(e,{casetype:"UPPERCASE",separator:t==null?void 0:t.separator,exclude:t==null?void 0:t.exclude})}lowercase(e,t={exclude:[],separator:""}){return this.stringCaser(e,{casetype:"lowercase",separator:t==null?void 0:t.separator,exclude:t==null?void 0:t.exclude})}Capitalize(e,t={exclude:[],separator:""}){return this.stringCaser(e,{casetype:"Capitalize",separator:t==null?void 0:t.separator,exclude:t==null?void 0:t.exclude})}camelCase(e,t={exclude:[],separator:""}){return this.stringCaser(e,{casetype:"camelCase",separator:t==null?void 0:t.separator,exclude:t==null?void 0:t.exclude})}PascalCase(e,t={exclude:[],separator:""}){return this.stringCaser(e,{casetype:"PascalCase",separator:t==null?void 0:t.separator,exclude:t==null?void 0:t.exclude})}}const X={super_admin:"admin",admin:"admin",doctor:"doctor"},xe=(r,e)=>{const t=r?X==null?void 0:X[r]:X.admin;return t||(e.pathname.includes("admin")?"admin":"user")},ge={primary:"#4F46E5",signup:"signup",add:"add",edit:"edit",search:"search",custom:"custom"},at={EQUAL:"eq",NOT_EQUAL:"neq",CONTAINS:"cs",NOT_CONTAINS:"ncs",START_WITH:"sw",NOT_START_WITH:"nsw",END_WITH:"ew",NOT_END_WITH:"new",LESS_THAN:"lt",NOT_LESS_THAN:"nlt",GREATER_THAN:"gt",BETWEEN:"bt",NOT_BETWEEN:"nbt",LESS_THAN_OR_EQUAL:"le",NOT_LESS_THAN_OR_EQUAL:"nle",GREATER_THAN_OR_EQUAL:"ge",NOT_GREATER_THAN_OR_EQUAL:"nge",NOT_GREATER_THAN:"ngt",IS_NULL:"is",IS_NOT_NULL:"nis",IN:"in",NOT_IN:"nin"},ca="manaknightdigital.com";var u=(r=>(r.GET="GET",r.POST="POST",r.PUT="PUT",r.PUTWHERE="PUTWHERE",r.DELETE="DELETE",r.DELETEALL="DELETEALL",r.GETALL="GETALL",r.PAGINATE="PAGINATE",r.CURSORPAGINATE="CURSORPAGINATE",r.AUTOCOMPLETE="AUTOCOMPLETE",r))(u||{}),C=(r=>(r.ADMIN="admin",r.SUPER_ADMIN="super_admin",r.USER="member",r.DOCTOR="doctor",r))(C||{}),K=(r=>(r.SUCCESS="success",r.ERROR="error",r.WARNING="warning",r.INFO="info",r))(K||{});class k{constructor(e={}){I(this,"_baseurl");I(this,"_project_id");I(this,"_secret");I(this,"_base64Encode");this._baseurl=e.baseurl||"https://baas.mytechpassport.com",this._project_id=e.project_id||"core",this._secret=e.secret||"3c3w0u0scvmcv39q0brods1iv11q6n5lf";const t=this._project_id+":"+this._secret;this._base64Encode=btoa(t)}updateXProject(e,t){console.log(e,t),this._base64Encode=btoa(e+":"+t)}resetXProject(){const e=this._project_id+":"+this._secret;this._base64Encode=btoa(e)}getHeader(e,t){const o={"Content-Type":"application/json","x-project":this._base64Encode,Authorization:`Bearer ${localStorage.getItem("token")||""}`},s=new Headers;return Object.entries({...o,...e||{}}).forEach(([n,p])=>{t!=null&&t.includes(n)||s.append(n,p)}),s}baseUrl(){return this._baseurl}getProjectId(){return this._project_id}treeBaseUrl(){return this._baseurl+"/v1/api/records"}treeBaseUrl_1(){return this._baseurl+"/v5/api/deployments/"}getJoins(e={}){let t=Object.prototype.hasOwnProperty.call(e,"join"),o=e.join;t&&typeof o=="string"?o=o.split(","):Array.isArray(o)?o=o:o=[];let s="";return o.forEach(n=>{s+=`join=${n}&`}),[t,o,s]}getOrdering(e){const t=L(e.order)?"id":e.order,o=L(e.direction)?"desc":e.direction;return`order=${t},${o}&`}getFilters(e){const t=Object.prototype.hasOwnProperty.call(e,"filter"),o=e.filter;let s="";return t&&Array.isArray(o)&&o.forEach(n=>{s+=`filter=${n}&`}),[t,o,s]}getRole(){return localStorage.getItem("role")||"user"}async request(e){const{endpoint:t="",method:o="GET",body:s=null,params:n={},signal:p,dynamicEndpoint:d=!1,additionalHeaders:l={}}=e;let c=d?Object.entries(n).reduce((j,[f,v])=>j.replace(`{${f}}`,String(v)),t):t;const m=this.getHeader(l),g={method:o,headers:m,body:s?JSON.stringify(s):void 0,signal:p},b=this.getRole();try{const j=await fetch(`${this.treeBaseUrl()}${c==null?void 0:c.replaceAll("{{project}}",this.getProjectId()).replaceAll("{{role}}",b)}`,g);return this.handleFetchResponse(j)}catch(j){throw console.error("API Call Error:",j),j}}async handleFetchResponse(e){try{const t=await e.json();if(!e.ok)throw new Error(t.message||`HTTP error! status: ${e.status}`);return t}catch(t){throw console.error("Fetch response handling error:",t),t}}async getOne(e,t,o={}){if(L(e)||L(t))throw new Error("table, id is required.");const[s,n,p]=this.getJoins(o);return this.request({endpoint:`/{{project}}/{{role}}/${e}/${t}?${p}`,method:u.GET})}async getOneFilter(e,t={}){if(L(e)||L(t==null?void 0:t.filter))throw new Error("table and filter is required.");this.getJoins(t);const[o,s,n]=this.getFilters(t);return this.request({endpoint:`/{{project}}/{{role}}/${e}?${n}`,method:u.GET})}async getMany(e,t,o={}){if(L(e)||L(t))throw new Error("table id is required.");const[s,n,p]=this.getJoins(o),d=Array.isArray(t)?t.join(","):t;return this.request({endpoint:`/{{project}}/{{role}}/${e}/${d}?${p}`,method:u.GET})}async getList(e,t={}){if(L(e))throw new Error("table is required.");const[o,s,n]=this.getJoins(t),[p,d,l]=this.getFilters(t),c=this.getOrdering(t),m=t.size??10,g=t.page??1;return this.request({endpoint:`/{{project}}/{{role}}/${e}?${n}${l}${c}size=${m}&page=${g}`,method:u.GET})}async getPaginate(e,t={},o){if(L(e))throw new Error("table is required.");const[s,n,p]=this.getJoins(t),[d,l,c]=this.getFilters(t),m=this.getOrdering(t),g=t.size??20,b=t.page??1;return this.request({endpoint:`/{{project}}/{{role}}/${e}?${p}${c}${m}size=${g}&page=${b}`,method:u.GET,signal:o})}async create(e,t){if(L(e))throw new Error("table is required.");return this.request({endpoint:`/{{project}}/{{role}}/${e}`,method:u.POST,body:t})}async update(e,t,o){if(L(e)||L(t))throw new Error("table, id is required.");return this.request({endpoint:`/{{project}}/{{role}}/${e}/${t}`,method:u.PUT,body:o})}async updateWhere(e,t,o){if(L(e))throw new Error("table is required.");if(Object.keys(t).length===0)throw new Error("condition is required.");return this.request({endpoint:`/{{project}}/{{role}}/${e}`,method:u.PUT,body:{...o,updateCondition:t}})}async delete(e,t,o){if(L(e)||L(t))throw new Error("table, id is required.");return this.request({endpoint:`/{{project}}/{{role}}/${e}/${t}`,method:u.DELETE,body:o})}}class Y{constructor(e={}){I(this,"_baseurl");I(this,"_fe_baseurl");I(this,"_project_id");I(this,"_secret");I(this,"_table");I(this,"_GOOGLE_CAPTCHA_SITEKEY");I(this,"_base64Encode");this._baseurl=e.baseurl||"https://baas.mytechpassport.com",this._fe_baseurl=e.fe_baseurl||"http://localhost:3000",this._project_id=e.project_id||"deanna",this._secret=e.secret||"3c3w0u0scvmcv39q0brods1iv11q6n5lf",this._table=e.table||"",this._GOOGLE_CAPTCHA_SITEKEY=e.GOOGLE_CAPTCHA_SITEKEY||"6LfmBc8jAAAAAKfz4zIiX1HoAwuH-9kcx68-7hhd";const t=this._project_id+":"+this._secret;this._base64Encode=btoa(t)}updateXProject(e,t){this._base64Encode=btoa(`${e}:${t}`)}resetXProject(){const e=`${this._project_id}:${this._secret}`;this._base64Encode=btoa(e)}setTable(e){this._table=e}getProjectId(){return this._project_id}logout(){window.localStorage.clear()}getRole(){return localStorage.getItem("role")||"user"}async request(e){const{endpoint:t,method:o="GET",body:s=null,params:n={},signal:p,dynamicEndpoint:d=!1,additionalHeaders:l={},excludeHeaders:c=[]}=e;let m=d?Object.entries(n).reduce((f,[v,w])=>f.replace(`{${v}}`,String(w)),t):t;const g=this.getHeader({"Content-Type":"application/json",...l},c),b={method:o,headers:g,body:s?JSON.stringify(s):void 0,signal:p},j=this.getRole();try{const f=await fetch(`${this._baseurl}${m==null?void 0:m.replaceAll("{{project}}",this.getProjectId()).replaceAll("{{role}}",j)}`,b);return this.handleFetchResponse(f)}catch(f){throw console.error("API Call Error:",f),f}}async handleFetchResponse(e){try{const t=await e.json();if(!e.ok)throw new Error(t.message||`HTTP error! status: ${e.status}`);return t}catch(t){throw console.error("Fetch response handling error:",t),t}}getHeader(e,t){const o={"Content-Type":"application/json","x-project":this._base64Encode,Authorization:`Bearer ${localStorage.getItem("token")||""}`},s=new Headers;return Object.entries({...o,...e||{}}).forEach(([n,p])=>{t!=null&&t.includes(n)||s.append(n,p)}),s}async editorUploadImage(e){const t=new FormData;t.append("file",e);const o=`${this.baseUrl()}/v1/api/${this.getProjectId()}/${this.getRole()}/lambda/s3/upload`,s=await fetch(o,{method:u.POST,headers:this.getHeader(null,["Content-Type"]),body:t});return this.handleFetchResponse(s)}async createRoom(e){return this.request({endpoint:"/v3/api/lambda/realtime/room",method:u.POST,body:e})}async getAllUsers(){return this.request({endpoint:"/v1/api/rest/user/GETALL",method:u.POST})}async startPooling(e,t){return this.request({endpoint:`/v3/api/lambda/realtime/room/poll?user_id=${e}`,method:u.GET,signal:t})}async addStripeProduct(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/product",method:u.POST,body:e})}async getStripeProducts(e,t){const o=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/products?${o}&${t}`,method:u.GET})}async getStripeProduct(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/product/${e}`,method:u.GET})}async updateStripeProduct(e,t){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/product/${e}`,method:"PUT",body:t})}async addStripePrice(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/price",method:u.POST,body:e})}async getStripePrices(e,t){const o=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/prices?${o}&${t}`,method:u.GET})}async getStripePaymentLinks(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/paymentlinks",method:u.GET,additionalHeaders:{"x-project":this._base64Encode}})}async getStripePrice(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/price/${e}`,method:u.GET})}async updateStripePrice(e,t){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/price/${e}`,method:"PUT",body:t})}async getStripeSubscriptions(e,t){const o=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/subscriptions?${o}&${t}`,method:u.GET})}async adminCancelStripeSubscription(e,t){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/subscription/${e}`,method:u.DELETE,body:t})}async adminCreateUsageCharge(e,t){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/subscription/usage-charge",method:u.POST,body:{subId:e,quantity:t}})}async getStripeInvoices(e,t){const o=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/invoices?${o}`,method:u.GET})}async getStripeInvoicesV2(e,t){const o=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/invoices-v2?${o}&${t}`,method:u.GET})}async getStripeOrders(e,t){const o=new URLSearchParams(e),s=new URLSearchParams(t);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/orders?${o}&${s}`,method:u.GET})}async login(e,t,o){return await this.request({endpoint:`/v1/api/${this.getProjectId()}/{role}/lambda/login`,method:u.POST,body:{email:e,password:t,is_refresh:!0},dynamicEndpoint:!0,params:{role:o}})}async upload(e){const t=new FormData;t.append("file",e);const o=await fetch(`${this.uploadUrl()}`,{method:u.POST,headers:this.getHeader(null,["Content-Type"]),body:t});return this.handleFetchResponse(o)}async createRole(e){return await this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/role",method:u.POST,body:e})}async deleteRole(e){return await this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/role/${e}`,method:u.DELETE,dynamicEndpoint:!0,params:{roleId:e}})}resetXPRoject(){const e=this._project_id+":"+this._secret;this._base64Encode=btoa(e)}baseUrl(){return this._baseurl}uploadUrl(){return`${this.baseUrl()}/v1/api/${this.getProjectId()}/${this.getRole()}/lambda/upload`}getFrontendBaseUrl(){return this._fe_baseurl}getGoogleCaptchaSiteKey(){return this._GOOGLE_CAPTCHA_SITEKEY}async callRestAPI(e,t){const o=new k;try{switch(t){case u.GET:const s={join:e.join};return await o.getOne(this._table,e.id,s);case u.POST:return await o.create(this._table,e);case u.PUT:return await o.update(this._table,e.id,e);case u.PUTWHERE:return await this.request({endpoint:`/v1/api/rest/${this._table}/${u.PUTWHERE}`,method:u.POST,body:e});case u.DELETE:return await o.delete(this._table,e.id);case u.DELETEALL:return await this.request({endpoint:`/v1/api/rest/${this._table}/${u.DELETEALL}`,method:u.POST,body:e});case u.GETALL:return e.order=e.orderBy,await o.getList(this._table,e);case u.PAGINATE:const n={size:e.limit||10,page:e.page||1,order:e.sortId,direction:e.direction,join:e.join},p=e.payload||{};return n.filter=Object.keys(p).filter(d=>p[d]!==void 0).map(d=>d==="id"?`${d},eq,${p[d]}`:`${d},cs,${p[d]}`),await o.getPaginate(this._table,n);case u.CURSORPAGINATE:return await this.request({endpoint:`/v1/api/rest/${this._table}/${u.CURSORPAGINATE}`,method:u.POST,body:{page:e.page||1,limit:e.limit||10,...e}});case u.AUTOCOMPLETE:return await this.request({endpoint:`/v1/api/rest/${this._table}/${u.AUTOCOMPLETE}`,method:u.POST,body:e});default:throw new Error(`Unsupported REST API method: ${t}`)}}catch(s){throw console.error("REST API Call Error:",s),s}}async callRawAPI(e,t,o){try{const s={method:o,headers:this.getHeader(),body:o!==u.GET?JSON.stringify(t):void 0},n=await fetch(this._baseurl+e,s);return this.handleFetchResponse(n)}catch(s){throw console.error("Raw API Call Error:",s),s}}async oauthLoginApi(e,t){const o=await fetch(`${this.baseUrl()}/v1/api/${this.getProjectId()}/${t}/lambda/${e}/login?role=${t}`,{headers:{"x-project":this._base64Encode}}),s=await o.text();if(o.status===401||o.status===403)throw new Error(s);return s}async getProfile(){const e="/v1/api/{{project}}/{{role}}/lambda/profile";return this.request({endpoint:e,method:u.GET})}async updateProfile(e){const t="/v1/api/{{project}}/{{role}}/lambda/profile";return this.request({endpoint:t,method:u.POST,body:e})}async check(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/check",method:u.POST,body:{role:e}})}async register(e,t,o){return this.request({endpoint:`/v1/api/{{project}}/${o}/lambda/register`,method:u.POST,body:{email:e,password:t,role:o}})}async getSession(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/user-sessions/data",method:u.GET})}async sessionPost(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/analytics/user-sessions/",method:u.POST,body:e})}async enable2FA(e={}){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/2fa/enable",method:u.POST,body:e})}async disable2FA(e={}){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/2fa/disable",method:u.POST,body:e})}async verify2FA(e,t){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/2fa/verify",method:u.POST,body:{access_token:e,token:t},additionalHeaders:{Authorization:`Bearer ${e}`}})}async authorize2FA(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/2fa/authorize",method:u.POST,additionalHeaders:{Authorization:`Bearer ${e}`}})}async captchaValidation(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/google-captcha",method:u.POST,body:{captchaToken:e}})}async magicLoginAttempt(e,t){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/magic-login/generate",method:u.POST,body:{email:e,role:t,url:`${this._baseurl}/magic-login/verify`}})}async magicLoginVerify(e=""){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/magic-login",method:u.POST,body:{token:e}})}async exportCSV(){const e=await fetch(`${this._baseurl}/rest/${this._table}/EXPORT`,{method:u.POST,headers:this.getHeader()}),t=await e.text(),o=document.createElement("a");o.href=`data:text/csv;charset=utf-8,${encodeURI(t)}`,o.target="_blank",o.download=`${this._table}.csv`,o.click(),await this.handleFetchResponse(e)}async analyticsPost(e,t,o=u.POST){return this.request({endpoint:e,method:o,body:t})}async getProfilePreference(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/preference",method:u.GET})}async updateEmail(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/update/email",method:u.POST,body:{email:e}})}async updatePhoto(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/update/photo",method:u.POST,body:{photo:e}})}async updatePassword(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/update/password",method:u.POST,body:{password:e}})}async updateEmailByAdmin(e,t){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/admin/update/email",method:u.POST,body:{email:e,id:t}})}async updatePasswordByAdmin(e,t){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/admin/update/password",method:u.POST,body:{password:e,id:t}})}async verifyUser(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/verify/user",method:u.POST,body:{user_id:e}})}async createUser(e,t,o){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/register",method:u.POST,body:{email:e,password:t,role:o}})}async forgot(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/forgot",method:u.POST,body:{email:e}})}async reset(e,t,o){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/reset",method:u.POST,body:{token:e,code:t,password:o}})}async callJoinRestAPI(e,t,o,s,n,p,d="GETALL",l=1,c=10){const m={join_id_1:o,join_id_2:s,select:n,where:p||"",...d==="PAGINATE"&&{page:l,limit:c}};return this.request({endpoint:`/v1/api/{{project}}/{{role}}/join/${e}/${t}/${d}`,method:u.POST,body:m})}async callMultiJoinRestAPI(e,t,o,s,n=1,p=10,d=u.PAGINATE){const l={tables:e,joinIds:t,selectStr:o,where:s,page:n,limit:p};return this.request({endpoint:`/v1/api/{{project}}/{{role}}/multi-join/${d}`,method:u.POST,body:l})}async subscribeChannel(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/subscription/channel/room",method:u.POST,body:{room:e}})}async subscribeListen(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/subscription/channel/poll?room=${e}`,method:u.GET})}async unSubscribeChannel(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/subscription/channel/unsubscribe?room=${e}`,method:u.GET})}async channelOnline(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/subscription/channel/online?room=${e}`,method:u.GET})}async broadcast(e,t){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/subscription/channel/send",method:u.POST,body:{payload:e,room:t}})}async cmsAdd(e,t,o,s){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/cms",method:u.POST,body:{page:e,key:t,type:o,value:s}})}async cmsEdit(e,t,o,s,n){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/cms/${e}`,method:u.PUT,body:{page:t,key:o,type:s,value:n}})}getToken(){return window.localStorage.getItem("token")}async getMyRoom(){return this.request({endpoint:"/v3/api/{{project}}/{{role}}/lambda/realtime/room/my",method:u.GET})}async getChatId(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/room?room_id=${e}`,method:u.GET})}async getChats(e,t,o){return this.request({endpoint:"/v3/api/{{project}}/{{role}}/lambda/realtime/chat",method:u.POST,body:{room_id:e,chat_id:t,date:o}})}async restoreChat(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/v1/api/lambda/{{project}}/{{role}}/room/poll?room=${e}`,method:u.GET})}async postMessage(e){return this.request({endpoint:"/v3/api/{{project}}/{{role}}/lambda/realtime/send",method:u.POST,body:e})}async uploadImage(e){const t=`${this.baseUrl()}/v1/api/deanna/user/lambda/s3/upload`,o=await fetch(t,{method:u.POST,headers:this.getHeader(null,["Content-Type"]),body:e});return this.handleFetchResponse(o)}async initCheckoutSession(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/checkout",method:u.POST,body:e})}async registerAndSubscribe(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer/register-subscribe",method:u.POST,body:e})}async createStripeCustomer(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer",method:u.POST,body:e})}async createCustomerStripeCard(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer/card",method:u.POST,body:e})}async createStripeSubscription(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer/subscription",method:u.POST,body:e})}async getCustomerStripeSubscription(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer/subscription",method:u.GET})}async getCustomerStripeSubscriptions(e,t){const o=new URLSearchParams(e),s=new URLSearchParams(t);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/subscriptions?${o}&${s}`,method:u.GET})}async changeStripeSubscription(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer/subscription",method:u.PUT,body:e})}async cancelStripeSubscription(e,t){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/subscription/${e}`,method:u.DELETE,body:t})}async getCustomerStripeDetails(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/stripe/customer",method:u.GET})}async getCustomerStripeCards(e){const t=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/cards?${t}`,method:u.GET})}async getCustomerStripeInvoices(e){const t=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/invoices?${t}`,method:u.GET})}async getCustomerStripeCharges(e){const t=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/charges?${t}`,method:u.GET})}async getCustomerStripeOrders(e){const t=new URLSearchParams(e);return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/orders?${t}`,method:u.GET})}async setStripeCustomerDefaultCard(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/card/${e}/set-default`,method:u.PUT})}async deleteCustomerStripeCard(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/stripe/customer/card/${e}`,method:u.DELETE})}async addEventToGC(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/scheduling/google-calendar-event",method:u.POST,body:e})}async getFilteredBlogs(e=[],t=[],o){const s=new URLSearchParams;return e.length&&s.append("tags",e.join(",")),t.length&&s.append("categories",t.join(",")),o&&s.append("rule",o),this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/blog/filter?${s.toString()}`,method:u.GET})}async getAllBlogs(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/blog/all",method:u.GET})}async createBlog(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/blog/create",method:u.POST,body:e})}async getSingleBlog(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/blog/single/${e}`,method:u.GET})}async editBlog(e,t){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/blog/edit/${e}`,method:u.POST,body:t})}async deleteBlog(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/blog/delete/${e}`,method:u.DELETE})}async getallBlogCategories(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/blog/category",method:u.GET})}async createBlogCategory(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/blog/category",method:u.POST,body:e})}async deleteBlogCategory(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/blog/category/${e}`,method:u.DELETE})}async getallBlogTags(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/blog/tags",method:u.GET})}async createBlogTag(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/blog/tags",method:u.POST,body:e})}async deleteBlogTag(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/blog/tags/${e}`,method:u.DELETE})}async getAllWorkspaces(){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/pm/workspaces",method:u.GET,params:{limit:"99999"}})}async getWorkspace(e){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/pm/workspaces/${e}`,method:u.GET})}async createWorkspace(e){return this.request({endpoint:"/v1/api/{{project}}/{{role}}/lambda/pm/workspaces",method:u.POST,body:e})}async createUserBoards(e,t){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/pm/workspaces/${e}/boards`,method:u.POST,body:{...t,workspace_id:e}})}async getUserBoards(e,t=1,o=10,s="id",n="desc"){return this.request({endpoint:`/v1/api/{{project}}/{{role}}/lambda/pm/workspaces/${e}/boards?page=${t}&limit=${o}&sortId=${s}&direction=${n}`,method:u.GET})}async chatGPT(e){return this.request({endpoint:"/v1/api/qna/query",method:u.POST,body:{user_prompt:e}})}async gptTranscribe(e){return this.request({endpoint:"/v5/api/deployments/sow/gpt-transcribe",method:u.POST,body:e})}async gptTranslate(e){return this.request({endpoint:"/v5/api/deployments/sow/gpt-translate",method:u.POST,body:e})}async runPodStatus(){return this.request({endpoint:"/v1/api/qna/server/status",method:u.GET})}async getUserProjects(e){const t=new URLSearchParams;return e.diagnosis&&t.append("diagnosis",e.diagnosis),e.projectName&&t.append("projectName",e.projectName),e.clientName&&t.append("clientName",e.clientName),e.dateRange&&t.append("dateRange",e.dateRange),e.status&&t.append("status",e.status),e.user_id&&t.append("user_id",e.user_id),this.request({endpoint:`/v1/api/deanna/user/lambda/projects?${t.toString()}`,method:"GET"})}async uploadDocument(e,t,o,s){const n=new FormData;n.append("patient_id",t),n.append("patient_name",o),n.append("dob",s.toString()),e.forEach(c=>{n.append("files",c)});const p={Authorization:"Bearer "+localStorage.getItem("token")},d=await fetch(`${this._baseurl}/v1/api/deanna/user/lambda/upload-document`,{method:"POST",headers:p,body:n}),l=await d.json();if([400,401,403,404,500].includes(d.status))throw new Error(l.message);return l}async listDocuments(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/list-documents",method:"GET",params:e})}async deleteDocument(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/delete-document",method:"POST",body:{id:e}})}async analyzeDocument(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/analyze-document",method:"POST",body:{patient_id:e}})}async generateAIReport(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/generate-ai-report",method:"POST",body:e})}async getJobStatus(e){const t=new URLSearchParams;return e.job_id&&t.append("job_id",e.job_id),e.project_id&&t.append("project_id",e.project_id),this.request({endpoint:`/v1/api/deanna/user/lambda/job-status?${t.toString()}`,method:"GET"})}async getDSJobStatus(e){return this.request({endpoint:`/v1/api/deanna/user/lambda/ds-job-status?patient_id=${e}`,method:"GET"})}async fetchDSReport(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/fetch-ds-report",method:"POST",body:e})}async getReportSections(e){return this.request({endpoint:`/v1/api/deanna/user/lambda/sections?report_id=${e}`,method:"GET"})}async updateReportSectionOrder(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/sections",method:"POST",body:e,requiresAuth:!0})}async updateReportSection(e){return this.request({endpoint:`/v1/api/deanna/user/lambda/sections/${e.id}`,method:"PUT",body:e,requiresAuth:!0})}async uploadToS3(e,t){const o=new FormData;o.append("file",e),t&&o.append("caption",t);const s={Authorization:"Bearer "+localStorage.getItem("token")},n=await fetch(`${this._baseurl}/v1/api/deanna/user/lambda/s3/upload`,{method:"POST",headers:s,body:o}),p=await n.json();if([400,401,403,404,500].includes(n.status))throw new Error(p.message);return p}async getDoctors(){return this.request({endpoint:"/v1/api/deanna/user/lambda/doctors",method:"GET",requiresAuth:!0})}async createProject(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/create-project",method:"POST",body:e,requiresAuth:!0})}async assignProject(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/assign-project",method:"POST",body:e,requiresAuth:!0})}async updateProject(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/update-project",method:"PUT",body:e,requiresAuth:!0})}async deleteProject(e){return this.request({endpoint:`/v1/api/deanna/user/lambda/projects/${e}`,method:"DELETE",requiresAuth:!0})}async getSectionComments(e){return this.request({endpoint:`/v1/api/deanna/user/lambda/section-comments?report_section_id=${e.report_section_id}`,method:"GET",requiresAuth:!0,params:e})}async addSectionComment(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/section-comments",method:"POST",requiresAuth:!0,body:e})}async getDoctorCheatsheet(){return this.request({endpoint:"/v1/api/deanna/doctor/lambda/cheatsheet",method:u.GET})}async getPreviousReport(e){return this.request({endpoint:`/v1/api/deanna/doctor/lambda/previous-report?${e.patient_id?`patient_id=${e.patient_id}`:`project_id=${e.project_id}`}`,method:u.GET,params:e})}async renderPdf(e){return this.request({endpoint:"/v1/api/deanna/user/lambda/render-pdf",method:"POST",requiresAuth:!0,body:e})}}const we=(r={})=>{const e=i.useMemo(()=>new Y(r),[Y]),t=i.useMemo(()=>new k(r),[k]),o=e.getProjectId();return{sdk:e,tdk:t,projectId:o,operations:at}},ee={isAuthenticated:!1,user:null,userDetails:{firstName:null,lastName:null,photo:null},token:null,role:null,sessionExpired:null},ae=M.createContext({...ee,dispatch:()=>null,state:ee}),ot=(r,e)=>{switch(e.type){case"LOGIN":{const{user_id:t,token:o,role:s,first_name:n,last_name:p,photo:d}=e.payload;localStorage.setItem("user",t),localStorage.setItem("token",o),localStorage.setItem("role",s);const l={firstName:n??null,lastName:p??null,photo:d??null};return{...r,isAuthenticated:!0,user:Number(t),token:o,role:s,userDetails:l}}case"UPDATE_PROFILE":return{...r,role:localStorage.getItem("role"),profile:e.payload};case"LOGOUT":return localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("role"),{...ee};case"SESSION_EXPIRED":return{...r,sessionExpired:e.payload};default:return r}},V=(r,e)=>{e==="TOKEN_EXPIRED"&&r({type:"SESSION_EXPIRED",payload:!0})},st=({children:r})=>{const{sdk:e}=we(),[t,o]=i.useReducer(ot,ee),s=async(n,p,d)=>{try{const l=await e.getProfile();if(l.error)throw new Error(l.message);o({type:"LOGIN",payload:{user_id:p,token:n,role:d}})}catch{o({type:"LOGOUT"});const c=xe(d,window.location);c?window.location.href=`/${c}/login`:window.location.href="/"}};return i.useEffect(()=>{const n=localStorage.getItem("user"),p=localStorage.getItem("token"),d=localStorage.getItem("role");p&&n&&d&&s(p,n,d)},[]),a.jsx(ae.Provider,{value:{state:t,dispatch:o},children:r})},Te="REQUEST_LOADING",Pe="REQUEST_SUCCESS",Se="REQUEST_FAILED",Ce="SET_GLOBAL_PROPERTY",h={viewModel:"viewModel",createModel:"createModel",updateModel:"updateModel",listModel:"listModel",deleteModel:"deleteModel",customRequest:"customRequest"},nt={RouteList:"routelist",BasicRouteEditor:"basicrouteeditor",AdvancedRouteEditor:"advancedrouteeditor"},Oe={globalMessage:"",toastStatus:K.SUCCESS,isOpen:!0,showBackButton:!1,path:"",sessionExpired:!1,projectRow:null,leftPanel:[],middlePanel:[],rightPanel:{},selectedComponent:0,rightComponentId:"",selectedPageComponent:0,rooms:[],openRouteChangeModal:!1,nodes:[],edges:[],selectedNode:null,models:[],roles:[],routes:[],activeRoute:null,settings:{globalKey:`key_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,databaseType:"mysql",authType:"session",timezone:"UTC",dbHost:"localhost",dbPort:"3306",dbUser:"root",dbPassword:"root",dbName:`database_${new Date().toISOString().split("T")[0]}`,id:`project_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,isPWA:!1,isMultiTenant:!1,model_namespace:"",payment_option:"none"},defaultTablesShown:!1},oe=M.createContext({state:Oe,dispatch:()=>null}),it=(r,e)=>{var t,o,s,n,p,d,l,c,m;switch(e.type){case"SNACKBAR":return{...r,globalMessage:e.payload.message,toastStatus:e.payload.toastStatus??r.toastStatus};case"SETPATH":return{...r,path:e.payload.path};case"OPEN_SIDEBAR":return{...r,isOpen:e.payload.isOpen};case"SHOW_BACKBUTTON":return{...r,showBackButton:e.payload.showBackButton};case"SET_PROJECT_ROW":return{...r,projectRow:e.payload};case"SET_LEFT_PANEL":return{...r,leftPanel:e.payload};case"SET_MIDDLE_PANEL":return{...r,middlePanel:e.payload};case"SET_RIGHT_PANEL":return{...r,rightPanel:e.payload,rightComponentId:e.rightComponentId};case"SET_SELECTED_COMPONENT":return{...r,selectedComponent:e.payload};case"SET_SELECTED_PAGE_COMPONENT":return{...r,selectedPageComponent:e.payload};case"SETROOM":const g=r.rooms.findIndex(v=>v.position===e.payload.position);if(g!==-1){const v=[...r.rooms];return v[g]=e.payload,{...r,rooms:v}}else return{...r,rooms:[...r.rooms,e.payload]};case Ce:if((t=e.property)!=null&&t.toString().includes(".")){const[v,w]=e.property.toString().split("."),P=r[v];return{...r,[v]:{...typeof P=="object"&&P!==null?P:{},[w]:e.payload}}}return{...r,[e.property]:e.payload};case Te:const b=r[e.item];return{...r,[e.item]:{...typeof b=="object"&&b!==null?b:{},loading:e.payload}};case Pe:const j=r[e.item];return{...r,[e.item]:{...typeof j=="object"&&j!==null?j:{},...e.payload,error:!1,success:!0,loading:!1}};case Se:const f=r[e.item];return{...r,[e.item]:{...typeof f=="object"&&f!==null?f:{},...e.payload,error:!0,success:!1,loading:!1}};case"UPDATE_SETTINGS":return{...r,settings:e.payload};case"SET_DEFAULT_TABLES_SHOWN":return{...r,defaultTablesShown:e.payload};case"UPDATE_NODE":{const v={...(o=r==null?void 0:r.nodes)==null?void 0:o.find(A=>A.id===e.payload.id),data:{...(n=(s=r==null?void 0:r.nodes)==null?void 0:s.find(A=>A.id===e.payload.id))==null?void 0:n.data,...e.payload.data}},w={...r==null?void 0:r.activeRoute,flowData:{...(p=r==null?void 0:r.activeRoute)==null?void 0:p.flowData,nodes:[...((l=(d=r==null?void 0:r.activeRoute)==null?void 0:d.flowData)==null?void 0:l.nodes)||[],v]}},P=(c=r==null?void 0:r.routes)==null?void 0:c.map(A=>A.id===(w==null?void 0:w.id)?{...A,...w}:A);return{...r,selectedNode:v,activeRoute:w,routes:P,nodes:(m=r==null?void 0:r.nodes)==null?void 0:m.map(A=>A.id===e.payload.id?{...A,data:{...A.data,...e.payload.data}}:A)}}case"UPDATE_MODELS":return{...r,models:e.payload};case"UPDATE_ROLES":return{...r,roles:e.payload};case"UPDATE_ROUTES":return{...r,routes:e.payload};default:return r}},O=(r,e,t=3e3,o=K.SUCCESS)=>{r({type:"SNACKBAR",payload:{message:e,toastStatus:o}}),setTimeout(()=>r({type:"SNACKBAR",payload:{message:"",toastStatus:K.SUCCESS}}),t)},lt=({children:r})=>{const[e,t]=i.useReducer(it,Oe),o=c=>{t({type:"UPDATE_SETTINGS",payload:c})},s=c=>{t({type:"SET_DEFAULT_TABLES_SHOWN",payload:c})},n=(c,m)=>{t({type:"UPDATE_NODE",payload:{id:c,data:m}})},p=c=>{t({type:"UPDATE_MODELS",payload:c})},d=c=>{t({type:"UPDATE_ROLES",payload:c})},l=c=>{t({type:"UPDATE_ROUTES",payload:c})};return a.jsx(oe.Provider,{value:{state:e,dispatch:t,updateSettings:o,setDefaultTablesShown:s,updateNode:n,updateModels:p,updateRoles:d,updateRoutes:l},children:r})},dt=(r,e,t)=>{r({property:t,type:Ce,payload:e})};function E(r,e,t,o){r({item:t,type:Te,payload:e})}const F=(r,e,t)=>{r({item:t,type:Pe,payload:e})},B=(r,e,t)=>{r({item:t,type:Se,payload:e})},ct=async(r,e,t,o,s={method:"GET",join:null,allowToast:!0,state:h==null?void 0:h.viewModel,isPublic:!1})=>{var l,c;const n=new Y,p=(s==null?void 0:s.state)??h.viewModel,d=(s==null?void 0:s.method)??"GET";E(r,!0,p);try{n.setTable(t.trim());const m=await n.callRestAPI({id:Number(o),...s!=null&&s.join?{join:s==null?void 0:s.join}:null},d);return m!=null&&m.error||F(r,{data:m==null?void 0:m.model},p),E(r,!1,p),{error:!1,data:m==null?void 0:m.model,message:(m==null?void 0:m.message)??"Success"}}catch(m){const g=m,b=((c=(l=g==null?void 0:g.response)==null?void 0:l.data)==null?void 0:c.message)??(g==null?void 0:g.message)??"An error occurred";return E(r,!1,p),B(r,{message:b,id:o},p),s!=null&&s.allowToast&&O(r,b,4e3,"error"),s!=null&&s.isPublic||V(e,b),{error:!0,message:b}}},ut=async(r,e,t,o={filter:[],join:null,allowToast:!0})=>{var n,p,d;const s=new k;E(r,!0,h==null?void 0:h.listModel);try{const l=await s.getList(t,{...o!=null&&o.join?{join:o==null?void 0:o.join}:null,...o!=null&&o.filter&&((n=o==null?void 0:o.filter)!=null&&n.length)?{filter:o==null?void 0:o.filter}:null});return l!=null&&l.error||F(r,{data:l==null?void 0:l.list},h==null?void 0:h.listModel),E(r,!1,h==null?void 0:h.listModel),{error:!1,data:l==null?void 0:l.list}}catch(l){const c=l,m=((d=(p=c==null?void 0:c.response)==null?void 0:p.data)==null?void 0:d.message)??(c==null?void 0:c.message)??"An error occurred";return E(r,!1,h==null?void 0:h.listModel),B(r,{message:m},h==null?void 0:h.listModel),o!=null&&o.allowToast&&O(r,m,4e3,"error"),V(e,m),{error:!0,message:m}}},pt=async(r,e,t,o,s={allowToast:!0,join:null})=>{var p,d;const n=new k;E(r,!0,h==null?void 0:h.listModel);try{const l=await n.getList(t,{...s!=null&&s.join?{join:s==null?void 0:s.join}:null,filter:[`id,in,${o.join(",")}`]});return l!=null&&l.error||F(r,{data:l==null?void 0:l.list},h==null?void 0:h.listModel),E(r,!1,h==null?void 0:h.listModel),{error:!1,data:l==null?void 0:l.list}}catch(l){const c=l,m=((d=(p=c==null?void 0:c.response)==null?void 0:p.data)==null?void 0:d.message)??(c==null?void 0:c.message)??"An error occurred";return E(r,!1,h==null?void 0:h.listModel),B(r,{message:m,ids:o},h==null?void 0:h.listModel),s!=null&&s.allowToast&&O(r,m,4e3,"error"),V(e,m),{error:!0,message:m}}},mt=async(r,e,t,o,s={allowToast:!0})=>{var p,d;const n=new k;E(r,!0,h==null?void 0:h.createModel);try{const l=await n.create(t,o);return l!=null&&l.error?(E(r,!1,h==null?void 0:h.createModel),s!=null&&s.allowToast&&O(r,(l==null?void 0:l.message)??"An error occurred",4e3,"error"),{error:!0,message:l==null?void 0:l.message}):(F(r,{message:l==null?void 0:l.message,data:l==null?void 0:l.data},h==null?void 0:h.createModel),E(r,!1,h==null?void 0:h.createModel),s!=null&&s.allowToast&&O(r,(l==null?void 0:l.message)??"Success",4e3,"success"),{error:!1,data:l==null?void 0:l.data,message:l==null?void 0:l.message})}catch(l){const c=l,m=((d=(p=c==null?void 0:c.response)==null?void 0:p.data)==null?void 0:d.message)??(c==null?void 0:c.message)??"An error occurred";return E(r,!1,h==null?void 0:h.createModel),B(r,{message:m},h==null?void 0:h.createModel),s!=null&&s.allowToast&&O(r,m,4e3,"error"),V(e,m),{error:!0,message:m}}},ht=async(r,e,t,o,s,n={allowToast:!0})=>{var d,l;const p=new k;E(r,!0,h==null?void 0:h.updateModel);try{const c=await p.update(t,o,s);return c!=null&&c.error?(E(r,!1,h==null?void 0:h.updateModel),n!=null&&n.allowToast&&O(r,(c==null?void 0:c.message)??"An error occurred",4e3,"error"),{error:!0}):(E(r,!1,h==null?void 0:h.updateModel),n!=null&&n.allowToast&&O(r,(c==null?void 0:c.message)??"Success",4e3,"success"),{error:!1})}catch(c){const m=c,g=((l=(d=m==null?void 0:m.response)==null?void 0:d.data)==null?void 0:l.message)??(m==null?void 0:m.message)??"An error occurred";return E(r,!1,h==null?void 0:h.updateModel),B(r,{message:g},h==null?void 0:h.updateModel),n!=null&&n.allowToast&&O(r,g,4e3,"error"),V(e,g),{error:!0,message:g}}},_t=async(r,e,t,o,s,n={allowToast:!0})=>{var d,l;const p=new k;E(r,!0,h==null?void 0:h.deleteModel);try{const c=await p.delete(t,o,s);return c!=null&&c.error?(E(r,!1,h==null?void 0:h.deleteModel),n!=null&&n.allowToast&&O(r,(c==null?void 0:c.message)??"An error occurred",4e3,"error"),{error:!0}):(F(r,{message:c==null?void 0:c.message},h==null?void 0:h.deleteModel),E(r,!1,h==null?void 0:h.deleteModel),n!=null&&n.allowToast&&O(r,(c==null?void 0:c.message)??"Success",4e3,"success"),{error:!1,data:c==null?void 0:c.data})}catch(c){const m=c,g=((l=(d=m==null?void 0:m.response)==null?void 0:d.data)==null?void 0:l.message)??(m==null?void 0:m.message)??"An error occurred";return E(r,!1,h==null?void 0:h.deleteModel),B(r,{message:g},h==null?void 0:h.deleteModel),n!=null&&n.allowToast&&O(r,g,4e3,"error"),V(e,g),{error:!0,message:g}}},ft=async(r,e,t,o,s=void 0)=>{var p,d;const n=new k;E(r,!0,s??t);try{const l=await n.getList(t,{...o});return l!=null&&l.error?(E(r,!1,s??t),l):(F(r,{message:l==null?void 0:l.message,data:l==null?void 0:l.list},s??t),E(r,!1,s??t),{error:!1,data:l==null?void 0:l.list})}catch(l){const c=l,m=((d=(p=c==null?void 0:c.response)==null?void 0:p.data)==null?void 0:d.message)??(c==null?void 0:c.message)??"An error occurred";return E(r,!1,s??t),B(r,{message:m},s??t),V(e,m),{error:!0,message:m}}},gt=async(r,e,t={allowToast:!0,endpoint:"",payload:{},method:"GET",signal:null},o=h.customRequest)=>{var n,p;if(!t.endpoint)return O(r,"options.endpoint is a required field",4e3,"error"),{error:!0};const s=new Y;E(r,!0,o);try{const d=await s.request({endpoint:t==null?void 0:t.endpoint,method:t==null?void 0:t.method,body:t==null?void 0:t.payload,signal:t==null?void 0:t.signal});return d!=null&&d.error?(E(r,!1,o),t!=null&&t.allowToast&&O(r,(d==null?void 0:d.message)??"An Error Occurred",4e3,"error"),{...d,error:!0,validation:d==null?void 0:d.validation,message:d==null?void 0:d.message}):(F(r,{message:d==null?void 0:d.message,data:d==null?void 0:d.data,error:!1},o),E(r,!1,o),t!=null&&t.allowToast&&O(r,(d==null?void 0:d.message)??"Success",4e3,"success"),{...d,error:!1,data:(d==null?void 0:d.data)||(d==null?void 0:d.model)||(d==null?void 0:d.list),message:d==null?void 0:d.message})}catch(d){const l=d,c=((p=(n=l==null?void 0:l.response)==null?void 0:n.data)==null?void 0:p.message)??(l==null?void 0:l.message)??"An error occurred";return E(r,!1,o),B(r,{message:c,error:!0},o),t!=null&&t.allowToast&&O(r,c,4e3,"error"),console.log("error?.response >>",l==null?void 0:l.response),V(e,c),{error:!0,message:c}}},Ae="SET_TABLE_PROPERTY",Le={},Re=M.createContext({state:Le,dispatch:()=>null}),yt=(r,e)=>{var t;switch(e.type){case Ae:if((t=e.property)!=null&&t.toString().includes(".")){const[o,s]=e.property.toString().split("."),n=r[o];return{...r,[o]:{...typeof n=="object"&&n!==null?n:{},[s]:e.payload}}}return{...r,[e.property]:e.payload};default:return r}},jt=({children:r})=>{const[e,t]=i.useReducer(yt,Le);return a.jsx(Re.Provider,{value:{state:e,dispatch:t},children:r})},Et=(r,e,t)=>{r({property:t,type:Ae,payload:e})},Z={"/":{title:"Beyong Packaging",description:"Here are a set of free tools to use for you company",twitter_image:""},"/login":{title:"Login Title",description:""},"/account":{title:"Account Title",description:""},"/explore":{title:"Explore Title",description:""},"/favorites":{title:"Your Favorites",description:""},"/faq":{title:"FAQ",description:""},"/contact-us":{title:"Contact Us",description:""},"/account/my-bookings":{title:"Bookings",description:""},"/account/my-spaces":{title:"My Spaces",description:""},"/account/profile":{title:"Profile",description:""},"/account/payments":{title:"Payments",description:""},"/account/billings":{title:"Billings",description:""},"/account/reviews":{title:"Reviews",description:""},"/account/my-spaces/:id":{title:"Dynamic",description:""},"/admin/add-cms":{title:" admin add-cms",description:""},"/admin/add-email":{title:" admin add-email",description:""},"/admin/add-photo":{title:" admin add-photo",description:""},"/admin/cms":{title:" admin cms",description:""},"/admin/email":{title:" admin email",description:""},"/admin/photo":{title:" admin photo",description:""},"/admin/edit-cms/:id":{title:" admin edit-cms :id",description:""},"/admin/edit-email/:id":{title:" admin edit-email :id",description:""},"/magic-login/:role":{title:" magic-login :role",description:""},"/magic-login/verify":{title:" magic-login verify",description:""},"/admin/design":{title:"admin design",description:""},"/admin/add-design":{title:"admin add design",description:""},"/admin/edit-design":{title:"admin edit design",description:""},"/admin/view-design":{title:"admin view design",description:""},"/admin/embellishment":{title:"admin embellishment",description:""},"/admin/add-embellishment":{title:"admin add embellishment",description:""},"/admin/edit-embellishment":{title:"admin edit embellishment",description:""},"/admin/view-embellishment":{title:"admin view embellishment",description:""},"/admin/background":{title:"admin background",description:""},"/admin/add-background":{title:"admin add background",description:""},"/admin/edit-background":{title:"admin edit background",description:""},"/admin/view-background":{title:"admin view background",description:""},"/admin/qr_code":{title:"admin qr_code",description:""},"/admin/add-qr_code":{title:"admin add qr_code",description:""},"/admin/edit-qr_code":{title:"admin edit qr_code",description:""},"/admin/view-qr_code":{title:"admin view qr_code",description:""},"/admin/project":{title:"admin project",description:""},"/admin/add-project":{title:"admin add project",description:""},"/admin/edit-project":{title:"admin edit project",description:""},"/admin/view-project":{title:"admin view project",description:""},"/admin/add-price":{title:"admin add price",description:""},"/admin/add-product":{title:"admin add product",description:""},"/admin/invoice":{title:"admin invoice",description:""},"/admin/order":{title:"admin order",description:""},"/admin/price":{title:"admin price",description:""},"/admin/product":{title:"admin product",description:""},"/admin/subscription":{title:"admin subscription",description:""},"/admin/edit-price":{title:"admin edit price",description:""},"/admin/edit-product":{title:"admin edit product",description:""},"/admin/sign-up":{title:" admin sign-up",description:""},"/admin/login":{title:" admin login",description:""},"/admin/forgot":{title:" admin forgot",description:""},"/admin/reset":{title:" admin reset",description:""},"/admin/profile":{title:"Admin Profile",description:""},"/admin/dashboard":{title:" admin dashboard",description:""},"/admin/users":{title:"admin users",description:""},"/admin/add-user":{title:"admin add user",description:""},"/admin/edit-user/:id":{title:" admin edit-user :id",description:""},"/product/design/edit":{title:"Edit Design",description:""},"/product/design":{title:"Product Design",description:""},"/admin/edit-wireframe/:id":{title:"Edit Wireframe",description:""},"/magic-login":{title:"Magic Login",description:""},"/user/login":{title:"User Login",description:""},"/user/signup":{title:"User Signup",description:""},"/user/forgot-password":{title:"User Forgot Password",description:""},"/user/reset-password":{title:"User Reset Password",description:""},"/test-components":{title:"Test Components",description:""},"/projects":{title:"Projects",description:""},"/member/uploads":{title:"Document Uploads",description:""},"/member/analysis":{title:"Document Analysis Results",description:""},"/member/ai-report":{title:"AI Report",description:""},"/member/document-section-editor":{title:"Document Section Editor",description:""},"/member/review-changes":{title:"Review Changes",description:""},"/member/profile":{title:"User Profile",description:""},"/doctor/login":{title:"Doctor Login",description:""},"/doctor/forgot-password":{title:"Doctor Forgot Password",description:""},"/doctor/reset-password":{title:"Doctor Reset Password",description:""},"/doctor/signup":{title:"Doctor Signup",description:""},"/doctor/report":{title:"Doctor Report",description:""},"/doctor/cheatsheet":{title:"Doctor Cheatsheet",description:""},"/doctor/section-editor":{title:"Doctor Section Editor",description:""},"/doctor/report-overview":{title:"Doctor Report Overview",description:""},"/doctor/projects":{title:"Doctor Projects",description:""},"/doctor/profile":{title:"Doctor Profile",description:""},"*":{title:"404 Page not found",description:"Oops. Looks like this page doesn't exists"}},vt=({element:r,path:e})=>{const t=new re;return i.useEffect(()=>{const s=Z[e||"/"]||Z[""];document.title=s!=null&&s.title?t.Capitalize(s.title,{separator:" "}):"Wireframe Tool | Manaknight Digital"},[e]),r},S=i.memo(vt),z=()=>{const{state:r,dispatch:e,updateSettings:t,setDefaultTablesShown:o,updateNode:s,updateModels:n,updateRoles:p,updateRoutes:d}=i.useContext(oe),{state:l,dispatch:c}=i.useContext(ae),{state:m,dispatch:g}=i.useContext(Re),b=i.useCallback((x,T=5e3,U=K.SUCCESS)=>{O(e,x,T,U)},[e]),j=i.useCallback((x,T)=>{dt(e,T,x)},[e]),f=i.useCallback((x,T)=>{Et(g,T,x)},[g]),v=i.useCallback((x,T,U)=>{E(e,T,x)},[e]),w=i.useCallback(x=>{V(c,x)},[c]),P=i.useCallback(async(x,T)=>await ft(e,c,x,T),[e,c]),A=i.useCallback(async(x,T)=>await ut(e,c,x,T),[e,c]),ie=i.useCallback(async(x,T,U)=>await pt(e,c,x,T,U),[e,c]),J=i.useCallback(async(x,T,U)=>await ct(e,c,x,T,U),[e,c]),Ue=i.useCallback(async(x,T,U,Ve)=>await ht(e,c,x,T,U,Ve),[e,c]),Me=i.useCallback(async(x,T,U)=>await mt(e,c,x,T,U),[e,c]),qe=i.useCallback(async(x,T,U)=>await _t(e,c,x,T,{},U),[e,c]),ke=i.useCallback(async x=>await gt(e,c,x),[e,c]);return{globalState:r,authState:l,tableState:m,globalDispatch:e,authDispatch:c,tableDispatch:g,showToast:b,setGlobalState:j,tokenExpireError:w,getMany:P,getListByFilter:A,getManyByIds:ie,getSingle:J,update:Ue,create:Me,remove:qe,custom:ke,setLoading:v,setTableState:f,projectConfig:{updateSettings:t||(()=>{}),setDefaultTablesShown:o||(()=>{}),updateNode:s||(()=>{}),updateModels:n||(()=>{}),updateRoles:p||(()=>{}),updateRoutes:d||(()=>{})}}},bt=({path:r,children:e})=>{const{authState:t}=z(),o=new re,{isAuthenticated:s}=t;return i.useEffect(()=>{const n=Z[r??"/"];n!==void 0?document.title=n!=null&&n.title?o.Capitalize(n==null?void 0:n.title,{separator:" "}):"":document.title=""},[r]),a.jsx(a.Fragment,{children:s?a.jsx(a.Fragment,{children:e}):a.jsx(_e,{to:"/admin/login",replace:!0})})},xt=i.memo(bt),wt=({path:r,children:e})=>{const{authState:t}=z(),o=new re,{isAuthenticated:s}=t;return i.useEffect(()=>{const n=Z[r??"/"];n!==void 0?document.title=n!=null&&n.title?o.Capitalize(n==null?void 0:n.title,{separator:" "}):"":document.title=""},[r]),a.jsx(a.Fragment,{children:s?a.jsx(a.Fragment,{children:e}):a.jsx(_e,{to:"/user/login",replace:!0})})},Tt=i.memo(wt),Pt=({path:r,children:e})=>{const{authState:t}=z(),o=new re,{isAuthenticated:s}=t;return i.useEffect(()=>{const n=Z[r??"/"];n!==void 0?document.title=n!=null&&n.title?o.Capitalize(n==null?void 0:n.title,{separator:" "}):"":document.title=""},[r]),a.jsx(a.Fragment,{children:s?a.jsx(a.Fragment,{children:e}):a.jsx(_e,{to:"/doctor/login",replace:!0})})},St=i.memo(Pt),Ct=({path:r,element:e,access:t})=>{const{authState:o}=z();if(o!=null&&o.isAuthenticated)switch(!0){case[...["string"].includes(typeof t)?[t]:t,o==null?void 0:o.role].includes(C.ADMIN):return a.jsx(xt,{path:r,children:e});case[...["string"].includes(typeof t)?[t]:t,o==null?void 0:o.role].includes(C.USER):return a.jsx(Tt,{path:r,children:e});case[...["string"].includes(typeof t)?[t]:t,o==null?void 0:o.role].includes(C.DOCTOR):return a.jsx(St,{path:r,children:e});default:return a.jsx(S,{path:"*",element:a.jsx(he,{})})}if(!(o!=null&&o.isAuthenticated))return a.jsx(S,{path:"*",element:a.jsx(he,{})})},D=i.memo(Ct);var Ne={},W={};Object.defineProperty(W,"__esModule",{value:!0});W.cssValue=W.parseLengthAndUnit=void 0;var Ot={cm:!0,mm:!0,in:!0,px:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vw:!0,vh:!0,vmin:!0,vmax:!0,"%":!0};function De(r){if(typeof r=="number")return{value:r,unit:"px"};var e,t=(r.match(/^[0-9.]*/)||"").toString();t.includes(".")?e=parseFloat(t):e=parseInt(t,10);var o=(r.match(/[^0-9]*$/)||"").toString();return Ot[o]?{value:e,unit:o}:(console.warn("React Spinners: ".concat(r," is not a valid css value. Defaulting to ").concat(e,"px.")),{value:e,unit:"px"})}W.parseLengthAndUnit=De;function At(r){var e=De(r);return"".concat(e.value).concat(e.unit)}W.cssValue=At;var se={};Object.defineProperty(se,"__esModule",{value:!0});se.createAnimation=void 0;var Lt=function(r,e,t){var o="react-spinners-".concat(r,"-").concat(t);if(typeof window>"u"||!window.document)return o;var s=document.createElement("style");document.head.appendChild(s);var n=s.sheet,p=`
    @keyframes `.concat(o,` {
      `).concat(e,`
    }
  `);return n&&n.insertRule(p,0),o};se.createAnimation=Lt;var G=q&&q.__assign||function(){return G=Object.assign||function(r){for(var e,t=1,o=arguments.length;t<o;t++){e=arguments[t];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=e[s])}return r},G.apply(this,arguments)},Rt=q&&q.__createBinding||(Object.create?function(r,e,t,o){o===void 0&&(o=t);var s=Object.getOwnPropertyDescriptor(e,t);(!s||("get"in s?!e.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(r,o,s)}:function(r,e,t,o){o===void 0&&(o=t),r[o]=e[t]}),Nt=q&&q.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Dt=q&&q.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&Rt(e,r,t);return Nt(e,r),e},$t=q&&q.__rest||function(r,e){var t={};for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&e.indexOf(o)<0&&(t[o]=r[o]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,o=Object.getOwnPropertySymbols(r);s<o.length;s++)e.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(r,o[s])&&(t[o[s]]=r[o[s]]);return t};Object.defineProperty(Ne,"__esModule",{value:!0});var ce=Dt(i),ue=W,It=se,je=(0,It.createAnimation)("MoonLoader","100% {transform: rotate(360deg)}","moon");function Ut(r){var e=r.loading,t=e===void 0?!0:e,o=r.color,s=o===void 0?"#000000":o,n=r.speedMultiplier,p=n===void 0?1:n,d=r.cssOverride,l=d===void 0?{}:d,c=r.size,m=c===void 0?60:c,g=$t(r,["loading","color","speedMultiplier","cssOverride","size"]),b=(0,ue.parseLengthAndUnit)(m),j=b.value,f=b.unit,v=j/7,w=G({display:"inherit",position:"relative",width:"".concat("".concat(j+v*2).concat(f)),height:"".concat("".concat(j+v*2).concat(f)),animation:"".concat(je," ").concat(.6/p,"s 0s infinite linear"),animationFillMode:"forwards"},l),P=function(J){return{width:(0,ue.cssValue)(J),height:(0,ue.cssValue)(J),borderRadius:"100%"}},A=G(G({},P(v)),{backgroundColor:"".concat(s),opacity:"0.8",position:"absolute",top:"".concat("".concat(j/2-v/2).concat(f)),animation:"".concat(je," ").concat(.6/p,"s 0s infinite linear"),animationFillMode:"forwards"}),ie=G(G({},P(j)),{border:"".concat(v,"px solid ").concat(s),opacity:"0.1",boxSizing:"content-box",position:"absolute"});return t?ce.createElement("span",G({style:w},g),ce.createElement("span",{style:A}),ce.createElement("span",{style:ie})):null}var Mt=Ne.default=Ut;const qt={borderColor:"red"},ne=({size:r=20,color:e="#ffffff"})=>{const t=i.useId();return a.jsx(Mt,{color:e,loading:!0,cssOverride:qt,size:r,"data-testid":t})};i.lazy(()=>_(()=>import("./ControlIcon-8ba92e1f.js"),["assets/ControlIcon-8ba92e1f.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./KebabIcon-1790c369.js"),["assets/KebabIcon-1790c369.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./TrashIcon-96548b2a.js"),["assets/TrashIcon-96548b2a.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./ChevronUpIcon-f1dc5640.js"),["assets/ChevronUpIcon-f1dc5640.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./LoadCheckIcon-238ce5c5.js"),["assets/LoadCheckIcon-238ce5c5.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./XIcon-0424321c.js"),["assets/XIcon-0424321c.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./EditIcon-c851b460.js"),["assets/EditIcon-c851b460.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./PlusIcon-7a5fb19d.js"),["assets/PlusIcon-7a5fb19d.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./ThreeDotsHorizontal-c21c693e.js"),["assets/ThreeDotsHorizontal-c21c693e.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./DownloadIcon-13b1c259.js"),["assets/DownloadIcon-13b1c259.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./GitIcon-f24b517c.js"),["assets/GitIcon-f24b517c.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./SettingIcon-dfe413c5.js"),["assets/SettingIcon-dfe413c5.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./SyncIcon-ea6a3c9e.js"),["assets/SyncIcon-ea6a3c9e.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./PostmanIcon-6c1a5a60.js"),["assets/PostmanIcon-6c1a5a60.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./CaptureSpatialIcon-3f54326f.js"),["assets/CaptureSpatialIcon-3f54326f.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./RobotIcon-3f42ed1d.js"),["assets/RobotIcon-3f42ed1d.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./FolderIcon-6d883fb1.js"),["assets/FolderIcon-6d883fb1.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./FileLineIcon-76e762ce.js"),["assets/FileLineIcon-76e762ce.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./FileVerticalLineIcon-13025512.js"),["assets/FileVerticalLineIcon-13025512.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./FileMemoIcon-da47494a.js"),["assets/FileMemoIcon-da47494a.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./HamburgerMenuIcon-ba6d0a75.js"),["assets/HamburgerMenuIcon-ba6d0a75.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./PdfFileIcon-948ebc94.js"),["assets/PdfFileIcon-948ebc94.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./FileAudioIcon-860c5dd2.js"),["assets/FileAudioIcon-860c5dd2.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./DatabaseIcon-8cd0c513.js"),["assets/DatabaseIcon-8cd0c513.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./TreeStructureIcon-ebbfafca.js"),["assets/TreeStructureIcon-ebbfafca.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./ReactIcon-8e604125.js"),["assets/ReactIcon-8e604125.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./AudioFileIcon-aba669e3.js"),["assets/AudioFileIcon-aba669e3.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./RightBendArrow-96e9d5ed.js"),["assets/RightBendArrow-96e9d5ed.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./LeftBendArrow-301a1425.js"),["assets/LeftBendArrow-301a1425.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./AlertCircle-2e19b598.js"),["assets/AlertCircle-2e19b598.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./NarrowUpArrowIcon-67092361.js"),["assets/NarrowUpArrowIcon-67092361.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));const ua=i.lazy(()=>_(()=>import("./CalendarIcon-75b59db1.js"),["assets/CalendarIcon-75b59db1.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./CsvIcon-c5ca79f4.js"),["assets/CsvIcon-c5ca79f4.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./CircleCheckMarkIcon-d3b5304a.js"),["assets/CircleCheckMarkIcon-d3b5304a.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));i.lazy(()=>_(()=>import("./CloudUploadIcon-c6545c9d.js"),["assets/CloudUploadIcon-c6545c9d.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));const kt=({children:r})=>a.jsx(a.Fragment,{children:a.jsx("div",{className:"min-h-full w-full h-full max-h-full",children:a.jsx(i.Suspense,{fallback:a.jsx("div",{className:"flex min-h-full w-full h-full max-h-full  items-center justify-center",children:a.jsx(ne,{size:40,color:"#4F46E5"})}),children:a.jsx("div",{className:"min-h-full w-full h-full max-h-full",children:r})})})}),N=i.memo(kt),Ee=i.lazy(()=>_(()=>import("./NotFoundPage-0709f2ba.js"),["assets/NotFoundPage-0709f2ba.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js"])),Vt=i.lazy(()=>_(()=>import("./SnackBar-5b1c12fa.js"),["assets/SnackBar-5b1c12fa.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]));let ve;function zt(){const{authState:r,authDispatch:e}=z(),t=fe(),{pathname:o}=t,s=te(),n=r==null?void 0:r.role,p=r==null?void 0:r.sessionExpired,d=()=>{e({type:"SESSION_EXPIRED",payload:!1}),e({type:"LOGOUT"}),s(n?`${xe(n,t)}/login?redirect_uri=${o}`:`/login?redirect_uri=${o}`),clearTimeout(ve)};return i.useEffect(()=>{p&&(ve=setTimeout(()=>{d()},4e3))},[p]),p?a.jsx("div",{className:"relative min-h-svh max-h-svh h-svh w-full",children:a.jsx(le,{appear:!0,show:!0,as:i.Fragment,children:a.jsxs(de,{as:"div",className:"relative z-[99999999]",onClose:()=>{},children:[a.jsx(le.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),a.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:a.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:a.jsx(le.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:a.jsxs(de.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[a.jsx(de.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Session Expired"}),a.jsx("div",{className:"mt-2",children:a.jsx("p",{className:"text-sm text-gray-500",children:"Your current login session has expired. Redirecting to login page shortly"})}),a.jsx("div",{className:"mt-4",children:a.jsx("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",onClick:()=>d(),children:"Got it, thanks!"})})]})})})})]})})}):null}const Gt=i.lazy(()=>_(()=>import("./AdminHeader-c1dd4aae.js"),["assets/AdminHeader-c1dd4aae.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-icons-fe0a0adf.js","assets/index.esm-1e38c052.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),$e=()=>{const{globalState:{showBackButton:r,isOpen:e},globalDispatch:t}=z(),[o,s]=i.useState(""),n=fe();i.useEffect(()=>{const d=n.pathname.split("/");d[1]!=="user"&&d[1]!=="admin"?s(d[1]):s(d[2])},[n]);const p=()=>{t({type:"OPEN_SIDEBAR",payload:{isOpen:!0}})};return a.jsx("div",{className:"sticky right-0 top-0 z-20 flex h-14 max-h-14 w-full items-center justify-between bg-white px-6 py-4 shadow-sm",children:a.jsx("div",{className:"flex items-center gap-3",children:!e&&a.jsx("button",{onClick:p,className:"flex items-center justify-center w-10 h-10 rounded-md hover:bg-gray-100 transition-colors","aria-label":"Open sidebar",children:a.jsxs("svg",{width:"24",height:"24",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("rect",{x:"4",y:"6",width:"16",height:"2",rx:"1",fill:"currentColor"}),a.jsx("rect",{x:"4",y:"11",width:"16",height:"2",rx:"1",fill:"currentColor"}),a.jsx("rect",{x:"4",y:"16",width:"16",height:"2",rx:"1",fill:"currentColor"})]})})})})},Bt=({className:r="",count:e=5,counts:t=[2,1,3,1,1],circle:o=!1})=>a.jsx("div",{className:`flex overflow-hidden flex-col gap-5 p-4 w-full max-h-screen h-fit min-h-fit ${r}`,children:Array.from({length:e}).map((s,n)=>a.jsx(Qe,{count:t[n]??1,height:t[n]&&t[n]>1||n+1===e?25:80,circle:o,style:{marginBottom:"0.6rem"}},`${s}${n}`))}),Ht="/assets/mkd_logo-4ac1e585.png",Ft=({children:r,counts:e=[1],count:t=1,className:o,circle:s=!1,brand:n=!1,view:p})=>{var c,m;const d=M.Children.toArray(r).filter(Boolean),l=o||d.length>0&&((m=(c=d[0])==null?void 0:c.props)==null?void 0:m.className)||"";return a.jsx(i.Suspense,{fallback:n?a.jsxs("div",{className:"flex h-svh max-h-svh min-h-svh w-full min-w-full max-w-full flex-col items-center justify-center bg-black",children:[a.jsx("img",{src:Ht,className:"!h-[12.25rem]"}),a.jsx("span",{className:"text-[2.8125rem] text-white",children:"Wireframe v5"})]}):a.jsx(Bt,{counts:e,count:t,className:l,circle:s}),children:r})},$=i.memo(Ft),Qt=({children:r})=>a.jsxs(a.Fragment,{children:[a.jsx("div",{}),a.jsx($,{children:a.jsxs("div",{className:"relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden",children:[a.jsx(Gt,{}),a.jsxs("div",{className:"grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden",children:[a.jsx($e,{}),a.jsx(i.Suspense,{fallback:a.jsx("div",{className:"flex h-full max-h-full min-h-full w-full items-center justify-center",children:a.jsx(ne,{size:40,color:ge.primary})}),children:a.jsx("div",{className:"h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden",children:r})})]})]})})]}),me=i.memo(Qt),Wt=i.lazy(()=>{const r=_(()=>import("./AdminForgotPage-6dd47f95.js"),["assets/AdminForgotPage-6dd47f95.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index-e9605eb4.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),Kt=i.lazy(()=>{const r=_(()=>import("./AdminLoginPage-95257026.js").then(e=>e.A),["assets/AdminLoginPage-95257026.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index-c6183aa1.js"]);return r.finally(()=>{}),r}),Zt=i.lazy(()=>{const r=_(()=>import("./AdminProfilePage-eabb2f94.js"),["assets/AdminProfilePage-eabb2f94.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index-32ecee74.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),Jt=i.lazy(()=>{const r=_(()=>import("./AdminResetPage-6f318bee.js"),["assets/AdminResetPage-6f318bee.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index-e9605eb4.js","assets/index-c6183aa1.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),Xt=i.lazy(()=>{const r=_(()=>import("./AdminSignUpPage-e1505357.js"),["assets/AdminSignUpPage-e1505357.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index-e9605eb4.js","assets/index-c6183aa1.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r});i.lazy(()=>{const r=_(()=>import("./LandingPage-e02462be.js"),["assets/LandingPage-e02462be.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r});const Yt=i.lazy(()=>{const r=_(()=>import("./MagicLoginVerifyPage-033b7af9.js"),["assets/MagicLoginVerifyPage-033b7af9.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),er=i.lazy(()=>{const r=_(()=>import("./UserMagicLoginPage-1083f439.js"),["assets/UserMagicLoginPage-1083f439.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),tr=i.lazy(()=>{const r=_(()=>import("./index-6285e4bf.js"),["assets/index-6285e4bf.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),rr=i.lazy(()=>{const r=_(()=>import("./index-95109315.js"),["assets/index-95109315.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),ar=i.lazy(()=>{const r=_(()=>import("./index-448278ff.js"),["assets/index-448278ff.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),or=i.lazy(()=>{const r=_(()=>import("./index-eeac56d2.js"),["assets/index-eeac56d2.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),sr=i.lazy(()=>{const r=_(()=>import("./index-97109fe0.js"),["assets/index-97109fe0.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/date-fns-66ee9ebe.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),nr=i.lazy(()=>{const r=_(()=>import("./DocumentUploadPage-b403dce6.js"),["assets/DocumentUploadPage-b403dce6.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),ir=i.lazy(()=>{const r=_(()=>import("./DocumentAnalysisResultsPage-9bca3657.js"),["assets/DocumentAnalysisResultsPage-9bca3657.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),lr=i.lazy(()=>{const r=_(()=>import("./AIReportPage-29537c8f.js"),["assets/AIReportPage-29537c8f.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-beautiful-dnd-047ecf8c.js","assets/@mantine/core-d9c5c65c.js","assets/redux-3b07d581.js","assets/react-select-8c03feb0.js","assets/html2pdf.js-82514bbc.js","assets/@craftjs/core-ae02137e.js","assets/deanna_logo-c6d631d7.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),dr=i.lazy(()=>{const r=_(()=>import("./DocumentSectionEditorPage-5fcabf9e.js"),["assets/DocumentSectionEditorPage-5fcabf9e.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tiptap/react-8d1cdd44.js","assets/@tiptap/extension-highlight-1b3a19b9.js","assets/@tiptap/starter-kit-8dc7719e.js","assets/@tiptap/extension-text-align-4d325d88.js","assets/@tiptap/extension-underline-5fc56973.js","assets/@tiptap/extension-table-a2bc9598.js","assets/@tiptap/extension-table-row-159ab625.js","assets/@tiptap/extension-table-cell-b57d510e.js","assets/@tiptap/extension-table-header-412f6f9c.js","assets/date-fns-66ee9ebe.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),cr=i.lazy(()=>{const r=_(()=>import("./ReviewChangesPage-bdc60de9.js"),["assets/ReviewChangesPage-bdc60de9.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tiptap/react-8d1cdd44.js","assets/@tiptap/extension-highlight-1b3a19b9.js","assets/@tiptap/extension-table-a2bc9598.js","assets/@tiptap/extension-table-row-159ab625.js","assets/@tiptap/extension-table-cell-b57d510e.js","assets/@tiptap/extension-table-header-412f6f9c.js","assets/@tiptap/starter-kit-8dc7719e.js","assets/@tiptap/extension-text-align-4d325d88.js","assets/@tiptap/extension-underline-5fc56973.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),ur=i.lazy(()=>{const r=_(()=>import("./UserProfilePage-3a8ff772.js"),["assets/UserProfilePage-3a8ff772.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index-32ecee74.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),pr=i.lazy(()=>{const r=_(()=>import("./TestComponents-95f61d7f.js"),["assets/TestComponents-95f61d7f.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),mr=i.lazy(()=>{const r=_(()=>import("./DoctorLoginPage-26f8b7c5.js"),["assets/DoctorLoginPage-26f8b7c5.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/yup-5d8330af.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),hr=i.lazy(()=>{const r=_(()=>import("./DoctorForgotPage-b42c5804.js"),["assets/DoctorForgotPage-b42c5804.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),_r=i.lazy(()=>{const r=_(()=>import("./DoctorResetPage-9f20fb23.js"),["assets/DoctorResetPage-9f20fb23.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),fr=i.lazy(()=>{const r=_(()=>import("./DoctorSignUpPage-4c64d698.js"),["assets/DoctorSignUpPage-4c64d698.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),gr=i.lazy(()=>{const r=_(()=>import("./DoctorAiGeneratedSheet-6f82e787.js"),["assets/DoctorAiGeneratedSheet-6f82e787.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),yr=i.lazy(()=>{const r=_(()=>import("./DoctorReportPage-5ca8458e.js"),["assets/DoctorReportPage-5ca8458e.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tiptap/react-8d1cdd44.js","assets/@tiptap/extension-highlight-1b3a19b9.js","assets/@tiptap/starter-kit-8dc7719e.js","assets/@tiptap/extension-text-align-4d325d88.js","assets/@tiptap/extension-underline-5fc56973.js","assets/@tiptap/extension-table-a2bc9598.js","assets/@tiptap/extension-table-row-159ab625.js","assets/@tiptap/extension-table-cell-b57d510e.js","assets/@tiptap/extension-table-header-412f6f9c.js","assets/react-beautiful-dnd-047ecf8c.js","assets/@mantine/core-d9c5c65c.js","assets/redux-3b07d581.js","assets/react-select-8c03feb0.js","assets/html2pdf.js-82514bbc.js","assets/@craftjs/core-ae02137e.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),jr=i.lazy(()=>{const r=_(()=>import("./DoctorSectionEditorPage-cd1b3313.js"),["assets/DoctorSectionEditorPage-cd1b3313.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tiptap/react-8d1cdd44.js","assets/@tiptap/extension-highlight-1b3a19b9.js","assets/@tiptap/starter-kit-8dc7719e.js","assets/@tiptap/extension-text-align-4d325d88.js","assets/@tiptap/extension-underline-5fc56973.js","assets/@tiptap/extension-table-a2bc9598.js","assets/@tiptap/extension-table-row-159ab625.js","assets/@tiptap/extension-table-cell-b57d510e.js","assets/@tiptap/extension-table-header-412f6f9c.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),Er=i.lazy(()=>{const r=_(()=>import("./DoctorReportOverviewPage-abe21d7e.js"),["assets/DoctorReportOverviewPage-abe21d7e.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tiptap/react-8d1cdd44.js","assets/@tiptap/extension-highlight-1b3a19b9.js","assets/@tiptap/starter-kit-8dc7719e.js","assets/@tiptap/extension-text-align-4d325d88.js","assets/@tiptap/extension-underline-5fc56973.js","assets/@tiptap/extension-table-a2bc9598.js","assets/@tiptap/extension-table-row-159ab625.js","assets/@tiptap/extension-table-cell-b57d510e.js","assets/@tiptap/extension-table-header-412f6f9c.js","assets/deanna_logo-c6d631d7.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),vr=i.lazy(()=>{const r=_(()=>import("./index-ee74eef1.js"),["assets/index-ee74eef1.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/date-fns-66ee9ebe.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),br=i.lazy(()=>{const r=_(()=>import("./DoctorProfilePage-64741e9c.js"),["assets/DoctorProfilePage-64741e9c.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]);return r.finally(()=>{}),r}),R={Requirements:"Requirements",API:"API",Deployment:"Deployment"},xr=i.lazy(()=>_(()=>import("./EditWireframeTabs-81361e65.js"),["assets/EditWireframeTabs-81361e65.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),wr=i.lazy(()=>_(()=>import("./Modal-d7e9c903.js"),["assets/Modal-d7e9c903.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index.esm-1e38c052.js","assets/react-icons-fe0a0adf.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]).then(r=>({default:r.Modal}))),Tr=i.lazy(()=>_(()=>import("./ModalPrompt-1d9cd19b.js"),["assets/ModalPrompt-1d9cd19b.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"]));i.lazy(()=>_(()=>import("./ModalAlert-0d211506.js"),["assets/ModalAlert-0d211506.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js"]));const Pr=i.lazy(()=>_(()=>import("./ProjectDeployment-18bd5ad2.js"),["assets/ProjectDeployment-18bd5ad2.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tanstack/react-query-dc4b6186.js","assets/models-4d813338.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),pa=i.lazy(()=>_(()=>import("./FrontendDeploy-10c9c06b.js"),["assets/FrontendDeploy-10c9c06b.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index.esm-05b2469a.js","assets/react-icons-fe0a0adf.js","assets/@tanstack/react-query-dc4b6186.js","assets/models-4d813338.js","assets/react-hook-form-7e42b371.js","assets/yup-fe85ba88.js","assets/@hookform/resolvers-6b9dee20.js","assets/yup-5d8330af.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),ma=i.lazy(()=>_(()=>import("./BackendDeploy-a8537939.js"),["assets/BackendDeploy-a8537939.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/@tanstack/react-query-dc4b6186.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/index.esm-05b2469a.js","assets/react-icons-fe0a0adf.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),ha=i.lazy(()=>_(()=>import("./Tabs-e94fd1eb.js"),["assets/Tabs-e94fd1eb.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),Sr=i.lazy(()=>_(()=>import("./ViewWrapper-38289b26.js"),["assets/ViewWrapper-38289b26.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),Cr=i.lazy(()=>_(()=>import("./UploadConfig-fe46248a.js"),["assets/UploadConfig-fe46248a.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/index-ec6e151a.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),Or=()=>{const{globalDispatch:r,setGlobalState:e}=z(),[t,o]=i.useState(R==null?void 0:R.API),[s,n]=i.useState(!1),[p,d]=i.useState(!1),[l,c]=i.useState({API_CONFIGURATION:{value:"api-configuration",hasCount:!1,count:0},UPLOAD_CONFIGURATION:{value:"upload-configuration",hasCount:!1,count:0}}),[m,g]=i.useState(l.API_CONFIGURATION.value),b=te(),j=fe();M.useEffect(()=>(r({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}}),()=>{r({type:"SHOW_BACKBUTTON",payload:{showBackButton:!1}})}),[]),M.useEffect(()=>{r({type:"SETPATH",payload:{path:"wireframe"}})},[]);const f=w=>{const P=new URLSearchParams(j.search);P.set("tab",w),b({search:P.toString()})},v=w=>{o(w),f(w)};return M.useEffect(()=>{const P=new URLSearchParams(j.search).get("tab");P&&o(P)},[j.search]),M.useEffect(()=>{t===(R==null?void 0:R.API)&&e("view",nt.RouteList)},[t]),a.jsxs("div",{className:"mx-auto grid h-full max-h-full min-h-full grid-rows-[auto_1fr]",children:[a.jsx("div",{className:"w-full h-fit m-auto sticky top-0 z-10 inset-x-0",children:a.jsx($,{children:a.jsx(xr,{activeTab:t,handleClick:v,filterArr:[R==null?void 0:R.Requirements]})})}),a.jsxs("div",{className:"h-full min-h-full max-h-full",children:[t===R.Requirements&&a.jsx($,{counts:[1,3,2,1,2],count:5,children:a.jsx(a.Fragment,{})}),t===(R==null?void 0:R.API)&&a.jsx($,{counts:[1,3,2,1,2],count:5,children:a.jsxs(Sr,{view:m,views:[...Object.keys(l)],viewsMap:l,setView:w=>{var P;g((P=l[w])==null?void 0:P.value)},tabContainerClassName:"!px-10",children:[a.jsx($,{view:l.API_CONFIGURATION.value,children:a.jsx(a.Fragment,{children:a.jsx("iframe",{src:"https://resilient-fairy-1f9082.netlify.app/",className:"h-full w-full",loading:"lazy"})})}),a.jsx($,{view:l.UPLOAD_CONFIGURATION.value,children:a.jsx(Cr,{})})]})}),t===(R==null?void 0:R.Deployment)&&a.jsx($,{counts:[1,3,2,1,2],count:5,children:a.jsx(Pr,{})})]}),a.jsx($,{children:a.jsx(Tr,{open:p,closeModalFunction:()=>d(!1),actionHandler:()=>{n(!1),d(!1)},title:"Unsaved Changes",message:"You have unsaved changes, do you wish to skip unsaved changes?.",titleClasses:"text-blue-950 text-2xl font-semibold font-['Inter']",messageClasses:"text-slate-500 text-sm font-medium font-['Inter'] leading-tight",acceptText:"Skip",rejectText:"OK"})})]})},_a=i.lazy(()=>_(()=>import("./RouteChange-54c1ae7a.js"),["assets/RouteChange-54c1ae7a.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),Ar=i.lazy(()=>_(()=>import("./RouteChangeModal-d5937462.js"),["assets/RouteChangeModal-d5937462.js","assets/@react-google-maps/api-5b2d83cc.js","assets/vendor-489b60f1.js","assets/html2pdf.js-82514bbc.js","assets/@headlessui/react-15af3249.js","assets/react-loading-skeleton-b6c0da5e.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-209b94d8.js","assets/@fortawesome/react-fontawesome-78cc4a29.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@tanstack/react-query-dc4b6186.js","assets/@hotjar/browser-b90112fa.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/inter-07b8bb97.css","assets/roboto-mono-281fa166.css"])),Lr=[{to:"/member/uploads",label:"Create New",icon:a.jsxs("svg",{width:14,height:16,viewBox:"0 0 14 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[a.jsx("path",{d:"M14 16H0V0H14V16Z",stroke:"#E5E7EB"}),a.jsx("path",{d:"M8 2.5C8 1.94687 7.55312 1.5 7 1.5C6.44688 1.5 6 1.94687 6 2.5V7H1.5C0.946875 7 0.5 7.44688 0.5 8C0.5 8.55312 0.946875 9 1.5 9H6V13.5C6 14.0531 6.44688 14.5 7 14.5C7.55312 14.5 8 14.0531 8 13.5V9H12.5C13.0531 9 13.5 8.55312 13.5 8C13.5 7.44688 13.0531 7 12.5 7H8V2.5Z",fill:"#374151"})]})},{to:"/projects",label:"Saved Projects",icon:a.jsxs("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[a.jsx("path",{d:"M16 16H0V0H16V16Z",stroke:"#E5E7EB"}),a.jsx("path",{d:"M0 3C0 1.89688 0.896875 1 2 1H6.12813C6.725 1 7.29688 1.2375 7.71875 1.65937L9.05937 3H14C15.1031 3 16 3.89687 16 5V13C16 14.1031 15.1031 15 14 15H2C0.896875 15 0 14.1031 0 13V3ZM2 2.5C1.725 2.5 1.5 2.725 1.5 3V13C1.5 13.275 1.725 13.5 2 13.5H14C14.275 13.5 14.5 13.275 14.5 13V5C14.5 4.725 14.275 4.5 14 4.5H8.95625C8.625 4.5 8.30625 4.36875 8.07187 4.13438L6.65938 2.71875C6.51875 2.57812 6.32812 2.5 6.12813 2.5H2Z",fill:"#374151"})]})},{to:"/member/profile",label:"My Account",icon:a.jsxs("svg",{width:14,height:16,viewBox:"0 0 14 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[a.jsx("g",{clipPath:"url(#clip0_1_290)",children:a.jsx("path",{d:"M9.5 4C9.5 3.33696 9.23661 2.70107 8.76777 2.23223C8.29893 1.76339 7.66304 1.5 7 1.5C6.33696 1.5 5.70107 1.76339 5.23223 2.23223C4.76339 2.70107 4.5 3.33696 4.5 4C4.5 4.66304 4.76339 5.29893 5.23223 5.76777C5.70107 6.23661 6.33696 6.5 7 6.5C7.66304 6.5 8.29893 6.23661 8.76777 5.76777C9.23661 5.29893 9.5 4.66304 9.5 4ZM3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4ZM1.54062 14.5H12.4594C12.1813 12.5219 10.4813 11 8.42813 11H5.57188C3.51875 11 1.81875 12.5219 1.54062 14.5ZM0 15.0719C0 11.9937 2.49375 9.5 5.57188 9.5H8.42813C11.5063 9.5 14 11.9937 14 15.0719C14 15.5844 13.5844 16 13.0719 16H0.928125C0.415625 16 0 15.5844 0 15.0719Z",fill:"#374151"})}),a.jsx("defs",{children:a.jsx("clipPath",{id:"clip0_1_290",children:a.jsx("rect",{width:"14",height:"16",fill:"white"})})})]})}],Rr=()=>{const{state:{isOpen:r},dispatch:e}=i.useContext(oe),{dispatch:t}=i.useContext(ae),o=te(),s=()=>{e({type:"OPEN_SIDEBAR",payload:{isOpen:!r}})},n=()=>{t({type:"LOGOUT"}),o("/user/login")};return a.jsxs("nav",{className:`flex flex-col flex-shrink-0 justify-between items-start pr-px min-h-screen border-r border-r-[#e5e7eb] bg-white transition-all duration-300 ease-in-out ${r?"w-64":"w-16"}`,"aria-label":"Sidebar",children:[a.jsxs("div",{className:"w-full",children:[a.jsxs("div",{className:`flex relative flex-col justify-center items-start pb-7 p-6 border-b border-b-[#e5e7eb] bg-transparent ${r?"pr-20 w-[15.9375rem]":"w-full h-14"}`,children:[a.jsxs("div",{className:"flex justify-between items-center w-full",children:[a.jsx("div",{className:`flex justify-center items-center p-3 w-12 h-12 rounded-full bg-gray-800 ${r?"":"hidden"}`,children:a.jsx("div",{className:"flex flex-shrink-0 justify-center items-center w-6 h-6",children:a.jsxs("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[a.jsx("g",{clipPath:"url(#clip0_1_285)",children:a.jsx("path",{d:"M8.625 0C10.0734 0 11.25 1.17656 11.25 2.625V21.375C11.25 22.8234 10.0734 24 8.625 24C7.27031 24 6.15469 22.9734 6.01406 21.6516C5.77031 21.7172 5.5125 21.75 5.25 21.75C3.59531 21.75 2.25 20.4047 2.25 18.75C2.25 18.4031 2.31094 18.0656 2.41875 17.7562C1.00312 17.2219 0 15.8531 0 14.25C0 12.7547 0.876562 11.4609 2.14687 10.8609C1.73906 10.35 1.5 9.70312 1.5 9C1.5 7.56094 2.5125 6.36094 3.8625 6.06562C3.7875 5.80781 3.75 5.53125 3.75 5.25C3.75 3.84844 4.71563 2.66719 6.01406 2.33906C6.15469 1.02656 7.27031 0 8.625 0ZM15.375 0C16.7297 0 17.8406 1.02656 17.9859 2.33906C19.2891 2.66719 20.25 3.84375 20.25 5.25C20.25 5.53125 20.2125 5.80781 20.1375 6.06562C21.4875 6.35625 22.5 7.56094 22.5 9C22.5 9.70312 22.2609 10.35 21.8531 10.8609C23.1234 11.4609 24 12.7547 24 14.25C24 15.8531 22.9969 17.2219 21.5812 17.7562C21.6891 18.0656 21.75 18.4031 21.75 18.75C21.75 20.4047 20.4047 21.75 18.75 21.75C18.4875 21.75 18.2297 21.7172 17.9859 21.6516C17.8453 22.9734 16.7297 24 15.375 24C13.9266 24 12.75 22.8234 12.75 21.375V2.625C12.75 1.17656 13.9266 0 15.375 0Z",fill:"white"})}),a.jsx("defs",{children:a.jsx("clipPath",{id:"clip0_1_285",children:a.jsx("rect",{width:"24",height:"24",fill:"white"})})})]})})}),r&&a.jsx("button",{onClick:s,className:"flex absolute top-4 right-4 items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 transition-colors","aria-label":"Collapse sidebar",children:a.jsxs("svg",{width:"24",height:"24",fill:"none",viewBox:"0 0 24 24",children:[a.jsx("rect",{x:"4",y:"6",width:"16",height:"2",rx:"1",fill:"currentColor"}),a.jsx("rect",{x:"4",y:"11",width:"16",height:"2",rx:"1",fill:"currentColor"}),a.jsx("rect",{x:"4",y:"16",width:"16",height:"2",rx:"1",fill:"currentColor"})]})})]}),r&&a.jsx("div",{className:"w-[8.8125rem] h-7 text-gray-800 font-['Inter'] text-xl font-semibold leading-7 mt-2",children:"Psychometrist"})]}),a.jsx("div",{className:`flex flex-col flex-shrink-0 items-start gap-x-8 gap-y-3 p-4 bg-transparent ${r?"w-[15.9375rem]":"w-full"}`,children:Lr.map(p=>a.jsxs(be,{to:p.to,className:({isActive:d})=>`flex items-center gap-3 py-2 px-2 rounded-lg transition-colors w-full ${d?"bg-gray-100 text-gray-900":"text-gray-700 hover:bg-gray-50"}`,"aria-label":p.label,title:r?void 0:p.label,children:[p.icon,r&&a.jsx("span",{className:"ml-2 font-['Inter'] leading-6",children:p.label})]},p.to))})]}),a.jsx("div",{className:`flex-shrink-0 border-t border-t-[#e5e7eb] flex items-center bg-transparent ${r?"w-[15.9375rem] h-[4.5625rem] px-4":"w-full h-12 justify-center"}`,children:a.jsxs("button",{onClick:n,className:"flex items-center gap-2 text-gray-700 hover:text-gray-900 focus:outline-none","aria-label":"Logout",title:r?void 0:"Logout",children:[a.jsxs("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[a.jsx("path",{d:"M16 16H0V0H16V16Z",stroke:"#E5E7EB"}),a.jsx("path",{d:"M15.7063 8.70625C16.0969 8.31563 16.0969 7.68125 15.7063 7.29063L11.7063 3.29063C11.3156 2.9 10.6812 2.9 10.2906 3.29063C9.9 3.68125 9.9 4.31563 10.2906 4.70625L12.5844 7H6C5.44688 7 5 7.44688 5 8C5 8.55312 5.44688 9 6 9H12.5844L10.2906 11.2937C9.9 11.6844 9.9 12.3188 10.2906 12.7094C10.6812 13.1 11.3156 13.1 11.7063 12.7094L15.7063 8.70938V8.70625ZM5 3C5.55312 3 6 2.55313 6 2C6 1.44687 5.55312 1 5 1H3C1.34375 1 0 2.34375 0 4V12C0 13.6562 1.34375 15 3 15H5C5.55312 15 6 14.5531 6 14C6 13.4469 5.55312 13 5 13H3C2.44688 13 2 12.5531 2 12V4C2 3.44688 2.44688 3 3 3H5Z",fill:"#374151"})]}),r&&a.jsx("span",{className:"font-['Inter'] leading-6",children:"Logout"})]})})]})},Nr=({children:r})=>a.jsx($,{children:a.jsxs("div",{className:"relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden",children:[a.jsx(Rr,{}),a.jsxs("div",{className:"grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden",children:[a.jsx($e,{}),a.jsx(i.Suspense,{fallback:a.jsx("div",{className:"flex h-full max-h-full min-h-full w-full items-center justify-center",children:a.jsx(ne,{size:40,color:ge.primary})}),children:a.jsx("div",{className:"h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden",children:r})})]})]})}),H=i.memo(Nr),Dr=()=>{const{sdk:r}=we(),{authState:{profile:e},authDispatch:t,tokenExpireError:o}=z(),[s,n]=i.useState(null),[p,d]=i.useState(!1),l=M.useCallback(()=>{(async()=>{var c,m,g,b;try{if(!localStorage.getItem("role"))return;const f=await r.getProfile();console.log(f),f!=null&&f.error||(n(()=>{var v,w;return{...f==null?void 0:f.model,role:((v=f==null?void 0:f.model)==null?void 0:v.role)??((w=f==null?void 0:f.model)==null?void 0:w.role_id)}}),t({type:"UPDATE_PROFILE",payload:{...f==null?void 0:f.model,role:((c=f==null?void 0:f.model)==null?void 0:c.role)??((m=f==null?void 0:f.model)==null?void 0:m.role_id)}}))}catch(j){const f=j,v=((b=(g=f==null?void 0:f.response)==null?void 0:g.data)==null?void 0:b.message)??(f==null?void 0:f.message)??"An error occurred";o(v)}})()},[s]);return i.useEffect(()=>{!e||p?l():n(()=>e)},[e,p]),{profile:s,getProfile:l,refresh:p,setRefresh:d}},$r=({className:r,image:e})=>{const[t,o]=i.useState(!1);return a.jsxs(a.Fragment,{children:[e?a.jsx(a.Fragment,{children:a.jsx("div",{onClick:()=>o(!0),className:`h-[3rem] w-[3rem] cursor-pointer overflow-hidden rounded-full border ${r}`,children:a.jsx("img",{src:e,className:"object-contain h-full w-full",alt:""})})}):null,a.jsx($,{children:a.jsx(wr,{isOpen:t,modalCloseClick:()=>o(!1),title:"Image Preview",modalHeader:!0,classes:{modalDialog:"max-h-[90%] h-fit min-h-fit overflow-clip !w-full md:!w-[29.0625rem]",modal:"h-full",modalContent:"h-full w-full flex items-center"},children:t&&a.jsx($,{children:a.jsx("img",{className:"max-h-auto min-h-auto h-auto w-full",src:e,alt:"preview"})})})})]})},Ie=i.memo($r),fa=Object.freeze(Object.defineProperty({__proto__:null,default:Ie},Symbol.toStringTag,{value:"Module"})),Ir=[{to:"/doctor/projects",label:"View Projects",icon:a.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[a.jsx("path",{d:"M16 16H0V0H16V16Z",stroke:"#E5E7EB"}),a.jsx("path",{d:"M0 3C0 1.89688 0.896875 1 2 1H6.12813C6.725 1 7.29688 1.2375 7.71875 1.65937L9.05937 3H14C15.1031 3 16 3.89687 16 5V13C16 14.1031 15.1031 15 14 15H2C0.896875 15 0 14.1031 0 13V3ZM2 2.5C1.725 2.5 1.5 2.725 1.5 3V13C1.5 13.275 1.725 13.5 2 13.5H14C14.275 13.5 14.5 13.275 14.5 13V5C14.5 4.725 14.275 4.5 14 4.5H8.95625C8.625 4.5 8.30625 4.36875 8.07187 4.13438L6.65938 2.71875C6.51875 2.57812 6.32812 2.5 6.12813 2.5H2Z",fill:"#7C3AED"})]})},{to:"/doctor/profile",label:"My Profile",icon:a.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"16",viewBox:"0 0 14 16",fill:"none",children:[a.jsx("g",{"clip-path":"url(#clip0_1_1504)",children:a.jsx("path",{d:"M9.5 4C9.5 3.33696 9.23661 2.70107 8.76777 2.23223C8.29893 1.76339 7.66304 1.5 7 1.5C6.33696 1.5 5.70107 1.76339 5.23223 2.23223C4.76339 2.70107 4.5 3.33696 4.5 4C4.5 4.66304 4.76339 5.29893 5.23223 5.76777C5.70107 6.23661 6.33696 6.5 7 6.5C7.66304 6.5 8.29893 6.23661 8.76777 5.76777C9.23661 5.29893 9.5 4.66304 9.5 4ZM3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4ZM1.54062 14.5H12.4594C12.1813 12.5219 10.4813 11 8.42813 11H5.57188C3.51875 11 1.81875 12.5219 1.54062 14.5ZM0 15.0719C0 11.9937 2.49375 9.5 5.57188 9.5H8.42813C11.5063 9.5 14 11.9937 14 15.0719C14 15.5844 13.5844 16 13.0719 16H0.928125C0.415625 16 0 15.5844 0 15.0719Z",fill:"#4B5563"})}),a.jsx("defs",{children:a.jsx("clipPath",{id:"clip0_1_1504",children:a.jsx("path",{d:"M0 0H14V16H0V0Z",fill:"white"})})})]})}],Ur=()=>{i.useContext(oe);const{dispatch:r}=i.useContext(ae),{profile:e}=Dr(),t=te();console.log(e,"profile");const o=e!=null&&e.firstName&&(e!=null&&e.lastName)?`Dr. ${e.firstName} ${e.lastName}`:e==null?void 0:e.email,s=(e==null?void 0:e.specialty)||"Clinical Psychologist",n=(e==null?void 0:e.photo)||"https://randomuser.me/api/portraits/men/32.jpg",p=()=>{r({type:"LOGOUT"}),t("/doctor/login")};return a.jsxs("nav",{className:"flex flex-col flex-shrink-0 justify-between items-start w-64 h-screen border-r border-r-[#e5e7eb] bg-white","aria-label":"Sidebar",children:[a.jsxs("div",{className:"flex justify-center items-center w-full border-b border-b-[#e5e7eb] px-4 py-6",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(Ie,{image:n,className:"h-12 w-12"})}),a.jsxs("div",{className:"flex flex-col items-start text-base font-semibold text-gray-900 w-full truncate ml-2",children:[a.jsx("div",{className:"text-base font-semibold text-gray-900 w-full truncate",children:o}),a.jsx("div",{className:"text-xs text-gray-400 w-full truncate",children:s})]})]}),a.jsx("div",{className:"flex flex-col gap-1 w-full mt-4",children:Ir.map(d=>a.jsxs(be,{to:d.to,className:({isActive:l})=>`flex items-center mb-1 gap-3 py-3 px-4 rounded-lg transition-colors w-full text-sm font-medium ${l?"bg-violet-50 text-violet-700":"text-gray-700 hover:bg-gray-50"}`,"aria-label":d.label,children:[d.icon,a.jsx("span",{children:d.label})]},d.to))}),a.jsx("div",{className:"w-full border-t border-t-[#e5e7eb] flex items-center px-4 py-4 mt-auto",children:a.jsxs("button",{onClick:p,className:"flex items-center gap-2 text-gray-500 hover:text-gray-900 focus:outline-none text-sm font-medium","aria-label":"Logout",children:[a.jsxs("svg",{width:18,height:18,fill:"none",viewBox:"0 0 18 18",children:[a.jsx("path",{d:"M9 1.5A7.5 7.5 0 1 1 1.5 9",stroke:"#A1A1AA",strokeWidth:"1.2"}),a.jsx("path",{d:"M6.75 9l2.25 2.25L11.25 9",stroke:"#A1A1AA",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"})]}),a.jsx("span",{children:"Logout"})]})})]})},Mr=({children:r})=>a.jsx($,{children:a.jsxs("div",{className:"relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden",children:[a.jsx(Ur,{}),a.jsx("div",{className:"grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden",children:a.jsx(i.Suspense,{fallback:a.jsx("div",{className:"flex h-full max-h-full min-h-full w-full items-center justify-center",children:a.jsx(ne,{size:40,color:ge.primary})}),children:a.jsx("div",{className:"h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden",children:r})})})]})}),Q=i.memo(Mr),he=({isAuthenticated:r,role:e})=>{if(!r)return a.jsx(N,{children:a.jsx(Ee,{})});if(r&&e&&[C.ADMIN,C.SUPER_ADMIN].includes(e))return a.jsx(me,{children:a.jsx(Ee,{})})},qr=()=>{const{globalState:r,globalDispatch:e,authState:t,setGlobalState:o}=z(),s=(r==null?void 0:r.isOpen)??!1,n=(r==null?void 0:r.openRouteChangeModal)??!1,[p,d]=i.useState(window.innerWidth),l=i.useCallback(m=>{(m.ctrlKey||m.metaKey)&&m.shiftKey&&m.altKey&&["r","R"].includes(m.key)&&o("openRouteChangeModal",!0),["Escape","escape","ESCAPE","Esc","esc"].includes(m.key)&&o("openRouteChangeModal",!1)},[o]),c=m=>{s&&p<1024?e({type:"OPEN_SIDEBAR",payload:{isOpen:m}}):!s&&p>=1024&&e({type:"OPEN_SIDEBAR",payload:{isOpen:m}})};return i.useEffect(()=>{const m=new AbortController,g=m.signal;return window.addEventListener("resize",b=>{const j=b.currentTarget;j.innerWidth>=1024?c(!0):c(!1),d(j.innerWidth)},{signal:g}),()=>{m.abort()}},[p]),i.useEffect(()=>{const m=new AbortController,g=m.signal;return window.addEventListener("keydown",l,{signal:g}),()=>{m.abort()}},[]),a.jsxs("div",{onClick:()=>{s&&c(!1)},className:"h-svh grid grid-cols-1 grid-rows-[auto_1fr] min-h-svh max-h-svh overflow-y-hidden overflow-x-hidden bg-white",children:[a.jsxs(He,{children:[a.jsx(y,{path:"/admin/profile",element:a.jsx(D,{access:"admin",path:"/admin/profile",element:a.jsx(me,{children:a.jsx(Zt,{})})})}),a.jsx(y,{path:"/admin/edit-wireframe/:id",element:a.jsx(D,{access:["admin","super_admin"],path:"/admin/edit-wireframe/:id",element:a.jsx(me,{children:a.jsx(Or,{})})})}),a.jsx(y,{path:"/admin/login",element:a.jsx(S,{path:"/admin/login",element:a.jsx(N,{children:a.jsx(Kt,{})})})}),a.jsx(y,{path:"/admin/sign-up",element:a.jsx(S,{path:"/admin/sign-up",element:a.jsx(N,{children:a.jsx(Xt,{})})})}),a.jsx(y,{path:"/admin/forgot",element:a.jsx(S,{path:"/admin/forgot",element:a.jsx(N,{children:a.jsx(Wt,{})})})}),a.jsx(y,{path:"/admin/reset",element:a.jsx(S,{path:"/admin/reset",element:a.jsx(N,{children:a.jsx(Jt,{})})})}),a.jsx(y,{path:"/magic-login",element:a.jsx(S,{path:"/magic-login",element:a.jsx(N,{children:a.jsx(er,{})})})}),a.jsx(y,{path:"/magic-login/verify",element:a.jsx(S,{path:"/magic-login/verify",element:a.jsx(N,{children:a.jsx(Yt,{})})})}),a.jsx(y,{path:"/user/login",element:a.jsx(S,{path:"/user/login",element:a.jsx(N,{children:a.jsx(tr,{})})})}),a.jsx(y,{path:"/user/signup",element:a.jsx(S,{path:"/user/signup",element:a.jsx(N,{children:a.jsx(rr,{})})})}),a.jsx(y,{path:"/user/forgot-password",element:a.jsx(S,{path:"/user/forgot-password",element:a.jsx(N,{children:a.jsx(ar,{})})})}),a.jsx(y,{path:"/user/reset-password",element:a.jsx(S,{path:"/user/reset-password",element:a.jsx(N,{children:a.jsx(or,{})})})}),a.jsx(y,{path:"/test-components",element:a.jsx(S,{path:"/test-components",element:a.jsx(N,{children:a.jsx(pr,{})})})}),a.jsx(y,{path:"/projects",element:a.jsx(D,{access:C.USER,path:"/projects",element:a.jsx(H,{children:a.jsx(sr,{})})})}),a.jsx(y,{path:"/member/uploads",element:a.jsx(D,{access:C.USER,path:"/member/uploads",element:a.jsx(H,{children:a.jsx(nr,{})})})}),a.jsx(y,{path:"/member/analysis",element:a.jsx(D,{access:C.USER,path:"/member/analysis",element:a.jsx(H,{children:a.jsx(ir,{})})})}),a.jsx(y,{path:"/member/ai-report",element:a.jsx(D,{access:C.USER,path:"/member/ai-report",element:a.jsx(H,{children:a.jsx(lr,{})})})}),a.jsx(y,{path:"/member/document-section-editor",element:a.jsx(D,{access:C.USER,path:"/member/document-section-editor",element:a.jsx(H,{children:a.jsx(dr,{})})})}),a.jsx(y,{path:"/member/review-changes",element:a.jsx(D,{access:C.USER,path:"/member/review-changes",element:a.jsx(H,{children:a.jsx(cr,{})})})}),a.jsx(y,{path:"/member/profile",element:a.jsx(D,{access:C.USER,path:"/member/profile",element:a.jsx(H,{children:a.jsx(ur,{})})})}),a.jsx(y,{path:"/doctor/login",element:a.jsx(S,{path:"/doctor/login",element:a.jsx(N,{children:a.jsx(mr,{})})})}),a.jsx(y,{path:"/doctor/forgot-password",element:a.jsx(S,{path:"/doctor/forgot-password",element:a.jsx(N,{children:a.jsx(hr,{})})})}),a.jsx(y,{path:"/doctor/reset-password",element:a.jsx(S,{path:"/doctor/reset-password",element:a.jsx(N,{children:a.jsx(_r,{})})})}),a.jsx(y,{path:"/doctor/signup",element:a.jsx(S,{path:"/doctor/signup",element:a.jsx(N,{children:a.jsx(fr,{})})})}),a.jsx(y,{path:"/doctor/report",element:a.jsx(D,{access:C.DOCTOR,path:"/doctor/report",element:a.jsx(Q,{children:a.jsx(yr,{})})})}),a.jsx(y,{path:"/doctor/cheatsheet",element:a.jsx(D,{access:C.DOCTOR,path:"/doctor/cheatsheet",element:a.jsx(Q,{children:a.jsx(gr,{})})})}),a.jsx(y,{path:"/doctor/section-editor",element:a.jsx(D,{access:C.DOCTOR,path:"/doctor/section-editor",element:a.jsx(Q,{children:a.jsx(jr,{})})})}),a.jsx(y,{path:"/doctor/report-overview",element:a.jsx(D,{access:C.DOCTOR,path:"/doctor/report-overview",element:a.jsx(Q,{children:a.jsx(Er,{})})})}),a.jsx(y,{path:"/doctor/projects",element:a.jsx(D,{access:"doctor",path:"/doctor/projects",element:a.jsx(Q,{children:a.jsx(vr,{})})})}),a.jsx(y,{path:"/doctor/profile",element:a.jsx(D,{access:"doctor",path:"/doctor/profile",element:a.jsx(Q,{children:a.jsx(br,{})})})}),a.jsx(y,{path:"*",element:a.jsx(S,{path:"*",element:a.jsx(he,{isAuthenticated:t==null?void 0:t.isAuthenticated,role:t==null?void 0:t.role})})})]}),a.jsx(zt,{}),a.jsx(Vt,{}),a.jsx($,{children:a.jsx(Ar,{isOpen:n,onClose:()=>o("openRouteChangeModal",!1),options:[...t!=null&&t.isAuthenticated?[{name:`${t==null?void 0:t.role} Login`,route:`/${t==null?void 0:t.role}/login`},{name:"Test Components",route:"/test-components"}]:[{name:"Admin Login",route:"/admin/login"},{name:"User Login",route:"/user/login"},{name:"Test Components",route:"/test-components"}]],title:"Change Route"})})]})};class kr extends M.Component{constructor(t){super(t);I(this,"state");this.state={hasError:!1,error:"",errorStack:""}}static getDerivedStateFromError(t){return{hasError:!0}}componentDidCatch(t,o){this.setState(()=>({...this.state,error:t,errorStack:o.componentStack}))}render(){var t;if(this.state.hasError){const o=JSON.stringify(this.state.errorStack,null,"	").replaceAll("at",`
 at`);return a.jsx("div",{className:"scrollbar-hide m-5 flex h-screen w-full flex-col items-center justify-center gap-3 overflow-hidden p-20 text-red-600",children:a.jsxs("div",{className:"scrollbar-hide h-full w-full overflow-auto",children:[a.jsx("div",{children:(t=this.state.error)==null?void 0:t.message}),a.jsx("pre",{children:o})]})})}return a.jsx(a.Fragment,{children:this.props.children})}}const Vr=5128711,zr=6;Xe.init(Vr,zr);const Gr=We("pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"),Br=new Ze({defaultOptions:{queries:{experimental_prefetchInRender:!0}}});function Hr(){return a.jsx(kr,{fallback:a.jsx("div",{children:"Error"}),onError:()=>{},children:a.jsx($,{brand:!0,children:a.jsx(st,{children:a.jsx(lt,{children:a.jsx(jt,{children:a.jsx(Je,{client:Br,children:a.jsx(Fe,{children:a.jsx(Ke,{stripe:Gr,children:a.jsx(qr,{})})})})})})})})})}Ye.add(et,tt,rt);const Fr=pe.createRoot(document.getElementById("root"));Fr.render(a.jsx(Hr,{}));export{ae as A,ma as B,ua as C,R as E,pa as F,oe as G,$ as L,Ht as M,C as R,re as S,K as T,Sr as V,we as a,Y as b,wr as c,_a as d,ca as e,ha as f,Bt as g,Dr as h,fa as i,O as s,V as t,z as u};
