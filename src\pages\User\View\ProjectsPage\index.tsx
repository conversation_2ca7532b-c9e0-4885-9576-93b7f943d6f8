import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";
import { format } from "date-fns";

// Types
interface Project {
  clientName: string;
  projectName: string;
  date: string;
  diagnosis: string;
  status: string | number;
  userId: string;
  projectId: string;
  patientId: string;
  patientName: string;
  reportId: string;
  jobId: string;
}

interface ProjectsResponse {
  error: boolean;
  data: Project[];
}

// Icons
const ViewIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 4.37C13.76 4.37 17.13 6.89 18.54 10.5C17.13 14.11 13.76 16.63 10 16.63C6.24 16.63 2.87 14.11 1.46 10.5C2.87 6.89 6.24 4.37 10 4.37ZM10 2.5C5 2.5 0.73 5.86 0 10.5C0.73 15.14 5 18.5 10 18.5C15 18.5 19.27 15.14 20 10.5C19.27 5.86 15 2.5 10 2.5ZM10 7.5C11.38 7.5 12.5 8.62 12.5 10C12.5 11.38 11.38 12.5 10 12.5C8.62 12.5 7.5 11.38 7.5 10C7.5 8.62 8.62 7.5 10 7.5ZM10 5.5C7.52 5.5 5.5 7.52 5.5 10C5.5 12.48 7.52 14.5 10 14.5C12.48 14.5 14.5 12.48 14.5 10C14.5 7.52 12.48 5.5 10 5.5Z"
      fill="#4B5563"
    />
  </svg>
);

const EditIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.5 14.375V17.5H5.625L14.8417 8.28334L11.7167 5.15834L2.5 14.375ZM17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C14.9833 2.41667 14.4583 2.41667 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667Z"
      fill="#4B5563"
    />
  </svg>
);

const DeleteIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5 15.8333C5 16.75 5.75 17.5 6.66667 17.5H13.3333C14.25 17.5 15 16.75 15 15.8333V5.83333H5V15.8333ZM15.8333 3.33333H12.9167L12.0833 2.5H7.91667L7.08333 3.33333H4.16667V5H15.8333V3.33333Z"
      fill="#4B5563"
    />
  </svg>
);

const LoadingSpinner = () => (
  <div className="flex justify-center items-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
  </div>
);

const ErrorMessage = ({ message }: { message: string }) => (
  <div className="flex justify-center items-center p-8">
    <div className="text-red-600 text-center">
      <p className="font-medium">Error loading projects</p>
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ProjectsPage: React.FC = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();

  // State
  const [dateRange, setDateRange] = useState("");
  const [projectSearch, setProjectSearch] = useState("");
  const [clientSearch, setClientSearch] = useState("");
  const [selectedDiagnosis, setSelectedDiagnosis] = useState("All Diagnoses");
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Fetch projects
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page,
        limit,
        order: "created_at",
        direction: "desc" as const,
        ...(dateRange && { dateRange }),
        ...(projectSearch && { projectName: projectSearch }),
        ...(clientSearch && { clientName: clientSearch }),
        ...(selectedDiagnosis !== "All Diagnoses" && {
          diagnosis: selectedDiagnosis,
        }),
      };

      console.log(sdk, "here is where we use the sdk");

      const response = await sdk.getUserProjects(params);

      if (response.error) {
        throw new Error(response.message || "Failed to fetch projects");
      }

      setProjects(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  }, [
    sdk,
    page,
    limit,
    dateRange,
    projectSearch,
    clientSearch,
    selectedDiagnosis,
  ]);

  // Initial fetch
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // Handlers
  const handleView = async (projectId: string) => {
    const project = projects.find((p) => p.projectId === projectId);
    if (project) {
      localStorage.setItem("patientId", project.patientId);
      localStorage.setItem("patientName", project.patientName);
      localStorage.setItem("projectId", project.projectId);
      localStorage.setItem("jobId", project.jobId || "");
      localStorage.setItem("reportId", project.reportId);
      navigate(`/member/ai-report`);
    }
  };

  const handleEdit = (projectId: string) => {
    const project = projects.find((p) => p.projectId === projectId);
    if (project) {
      localStorage.setItem("patientId", project.patientId);
      localStorage.setItem("patientName", project.patientName);
      localStorage.setItem("reportId", project.reportId);
      localStorage.setItem("projectId", project.projectId);
    }
    navigate(`/member/document-section-editor`);
  };

  const handleDelete = async (projectId: string) => {
    if (!window.confirm("Are you sure you want to delete this project?")) {
      return;
    }

    try {
      setDeletingId(projectId);
      await sdk.deleteProject(projectId);
      await fetchProjects();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete project");
    } finally {
      setDeletingId(null);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch {
      return dateString;
    }
  };

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-semibold text-gray-900 mb-6">Projects</h1>

      {/* Filters */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="flex flex-col">
          <label className="text-sm text-gray-600 mb-1">Date Range</label>
          <input
            type="date"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
            placeholder="mm/dd/yyyy"
          />
        </div>

        <div className="flex flex-col">
          <label className="text-sm text-gray-600 mb-1">Project Name</label>
          <input
            type="text"
            value={projectSearch}
            onChange={(e) => setProjectSearch(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
            placeholder="Search projects..."
          />
        </div>

        <div className="flex flex-col">
          <label className="text-sm text-gray-600 mb-1">Client Name</label>
          <input
            type="text"
            value={clientSearch}
            onChange={(e) => setClientSearch(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
            placeholder="Search clients..."
          />
        </div>

        <div className="flex flex-col">
          <label className="text-sm text-gray-600 mb-1">Diagnosis</label>
          <select
            value={selectedDiagnosis}
            onChange={(e) => setSelectedDiagnosis(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md bg-white"
          >
            <option>All Diagnoses</option>
            <option>Anxiety</option>
            <option>Depression</option>
            <option>PTSD</option>
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow">
        {loading ? (
          <LoadingSpinner />
        ) : error ? (
          <ErrorMessage message={error} />
        ) : (
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">
                  Client Name
                </th>
                <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">
                  Project Name
                </th>
                <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">
                  Date
                </th>
                <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">
                  Diagnosis
                </th>
                <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">
                  Status
                </th>
                <th className="text-left px-6 py-4 text-sm font-medium text-gray-900">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {projects.length === 0 ? (
                <tr>
                  <td
                    colSpan={6}
                    className="px-6 py-8 text-center text-gray-500"
                  >
                    No projects found
                  </td>
                </tr>
              ) : (
                projects.map((project) => (
                  <tr
                    key={project.projectId}
                    className="border-b last:border-b-0 hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {project.patientName}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {project.projectName}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {formatDate(project.date)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {project.diagnosis}
                    </td>
                    <td className="px-6 py-4 text-sm">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        ${
                          project.status == "1"
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {project.status == "1" ? "Reviewed" : "Pending"}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex gap-2">
                        <button
                          className="p-1 hover:bg-gray-100 rounded-full"
                          title="View"
                          onClick={() => handleView(project.projectId)}
                        >
                          <ViewIcon />
                        </button>
                        <button
                          className="p-1 hover:bg-gray-100 rounded-full"
                          title="Edit"
                          onClick={() => handleEdit(project.projectId)}
                        >
                          <EditIcon />
                        </button>
                        <button
                          className="p-1 hover:bg-gray-100 rounded-full"
                          title="Delete"
                          onClick={() => handleDelete(project.projectId)}
                          disabled={deletingId === project.projectId}
                        >
                          {deletingId === project.projectId ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                          ) : (
                            <DeleteIcon />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default ProjectsPage;
