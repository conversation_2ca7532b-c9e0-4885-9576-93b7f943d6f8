import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, useEditor, Editor } from "@tiptap/react";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { useNavigate } from "react-router-dom";
import MkdSDK from "../../../utils/MkdSDK";
import { useContexts } from "@/hooks/useContexts";
import { showToast } from "@/context/Global/GlobalContext";
import { ToastStatusEnum } from "@/utils/Enums";
// Icons (reuse or import as needed)
// ... (reuse your provided icon components here) ...

interface Section {
  id: number;
  title: string;
  body: string;
  order?: number;
}

// SaveProjectModal (from your previous code)
interface SaveProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (projectName: string, assignedTo: string) => void;
  projectName: string;
  setProjectName: React.Dispatch<React.SetStateAction<string>>;
  projectDescription: string;
  setProjectDescription: React.Dispatch<React.SetStateAction<string>>;
  isSaving: boolean;
  doctors: Array<{ id: string; name: string; email: string }>;
  selectedDoctor: string;
  setSelectedDoctor: React.Dispatch<React.SetStateAction<string>>;
}

const SaveProjectModal: React.FC<SaveProjectModalProps> = ({
  isOpen,
  onClose,
  onSave,
  projectName,
  setProjectName,
  isSaving,
  doctors,
  selectedDoctor,
  setSelectedDoctor,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="flex flex-col flex-shrink-0 justify-center items-start gap-6 p-6 w-[400px] h-[19.875rem] rounded-lg border-0 border-gray-200 bg-white">
        <div className="flex justify-between w-full">
          <div className="flex-shrink-0 text-gray-800 font-['Inter'] text-xl leading-7">
            Save Project
          </div>
          <button
            onClick={onClose}
            className="flex items-center justify-center w-8 h-8"
          >
            <svg
              width={12}
              height={16}
              viewBox="0 0 12 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.7062 4.70664C11.0968 4.31602 11.0968 3.68164 10.7062 3.29102C10.3155 2.90039 9.68115 2.90039 9.29053 3.29102L5.9999 6.58477L2.70615 3.29414C2.31553 2.90352 1.68115 2.90352 1.29053 3.29414C0.899902 3.68477 0.899902 4.31914 1.29053 4.70977L4.58428 8.00039L1.29365 11.2941C0.903027 11.6848 0.903027 12.3191 1.29365 12.7098C1.68428 13.1004 2.31865 13.1004 2.70928 12.7098L5.9999 9.41602L9.29365 12.7066C9.68428 13.0973 10.3187 13.0973 10.7093 12.7066C11.0999 12.316 11.0999 11.6816 10.7093 11.291L7.41553 8.00039L10.7062 4.70664Z"
                fill="#4B5563"
              />
            </svg>
          </button>
        </div>

        <div className="flex flex-col gap-4 w-full">
          <div className="flex flex-col gap-1">
            <label className="text-gray-600 font-['Inter'] text-sm">
              Project Name
            </label>
            <input
              type="text"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              className="px-3 py-2 rounded-md border border-gray-300 w-full"
            />
          </div>

          <div className="flex flex-col gap-1">
            <label className="text-gray-600 font-['Inter'] text-sm">
              Assign to:
            </label>

            <select
              value={selectedDoctor}
              onChange={(e) => setSelectedDoctor(e.target.value)}
              className="px-3 py-2 rounded-md border border-gray-300 w-full"
            >
              <option value="">Select a Doctor</option>
              {doctors.map((doctor) => (
                <option key={doctor.id} value={doctor.id}>
                  {doctor.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 font-['Inter']"
            >
              Cancel
            </button>
            <button
              disabled={isSaving}
              onClick={() => {
                onSave(projectName, selectedDoctor);
              }}
              className="px-4 py-2 bg-gray-800 text-white rounded-md font-['Inter'] disabled:opacity-50"
            >
              Save and Assign
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Toolbar (reuse from DocumentSectionEditorPage, but with icons if desired)
const Toolbar: React.FC<{ editor: Editor | null }> = ({ editor }) => {
  const [showNewChanges, setShowNewChanges] = useState(false);
  const [showOriginalAI, setShowOriginalAI] = useState(false);
  if (!editor) return null;
  return (
    <div className="w-full">
      {/* Main Toolbar */}
      <div className="flex items-center gap-2 border-b px-2 py-2 bg-white">
        {/* Bold */}
        <button
          type="button"
          aria-label="Bold"
          aria-pressed={editor.isActive("bold")}
          className={`px-2 py-1 rounded ${editor.isActive("bold") ? "font-bold text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <b>B</b>
        </button>
        {/* Italic */}
        <button
          type="button"
          aria-label="Italic"
          aria-pressed={editor.isActive("italic")}
          className={`px-2 py-1 rounded ${editor.isActive("italic") ? "italic text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <i>I</i>
        </button>
        {/* Underline */}
        <button
          type="button"
          aria-label="Underline"
          aria-pressed={editor.isActive("underline")}
          className={`px-2 py-1 rounded ${editor.isActive("underline") ? "underline text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleUnderline?.().run()}
          disabled={!editor.can().toggleUnderline?.()}
        >
          <u>U</u>
        </button>
        {/* List Buttons */}
        <button
          type="button"
          aria-label="Bulleted List"
          aria-pressed={editor.isActive("bulletList")}
          className={`px-2 py-1 rounded ${editor.isActive("bulletList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <circle cx="4" cy="5" r="1.5" fill="currentColor" />
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="9" r="1.5" fill="currentColor" />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <circle cx="4" cy="13" r="1.5" fill="currentColor" />
            <rect
              x="7"
              y="12"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Numbered List"
          aria-pressed={editor.isActive("orderedList")}
          className={`px-2 py-1 rounded ${editor.isActive("orderedList") ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <text x="2" y="7" fontSize="6" fill="currentColor">
              1.
            </text>
            <rect x="7" y="4" width="8" height="2" rx="1" fill="currentColor" />
            <text x="2" y="13" fontSize="6" fill="currentColor">
              2.
            </text>
            <rect
              x="7"
              y="10"
              width="8"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        {/* Divider */}
        <span className="mx-2 border-l h-6" />
        {/* Alignment Buttons */}
        <button
          type="button"
          aria-label="Align Left"
          aria-pressed={editor.isActive({ textAlign: "left" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "left" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="3" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Center"
          aria-pressed={editor.isActive({ textAlign: "center" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "center" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="5" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Align Right"
          aria-pressed={editor.isActive({ textAlign: "right" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "right" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect x="7" y="8" width="8" height="2" rx="1" fill="currentColor" />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
        <button
          type="button"
          aria-label="Justify"
          aria-pressed={editor.isActive({ textAlign: "justify" })}
          className={`px-2 py-1 rounded ${editor.isActive({ textAlign: "justify" }) ? "text-gray-800 bg-gray-100" : "text-gray-600"}`}
          onClick={() => editor.chain().focus().setTextAlign("justify").run()}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <rect
              x="3"
              y="4"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="8"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="3"
              y="12"
              width="12"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      {/* Undo/Redo and Checkboxes */}
      <div className="flex items-center justify-between px-2 py-2">
        <div className="flex gap-2">
          <button
            type="button"
            aria-label="Undo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().undo().run()}
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M7 4L3 8L7 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M3 8H11C13.2091 8 15 9.79086 15 12C15 14.2091 13.2091 16 11 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Undo
          </button>
          <button
            type="button"
            aria-label="Redo"
            className="flex items-center gap-1 px-3 py-1 border rounded bg-white text-gray-700"
            onClick={() => editor.chain().focus().redo().run()}
          >
            <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
              <path
                d="M11 4L15 8L11 12"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M15 8H7C4.79086 8 3 9.79086 3 12C3 14.2091 4.79086 16 7 16H9"
                stroke="#4B5563"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Redo
          </button>
        </div>
        <div className="flex gap-6 items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-blue-100 border border-blue-300" />
              <span className="text-gray-500 text-sm">Original AI Text</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-sm bg-gray-200 border border-gray-400" />
              <span className="text-gray-500 text-sm">New Changes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ReviewChangesPage: React.FC = () => {
  const navigate = useNavigate();
  const { globalDispatch } = useContexts();
  const [sections, setSections] = useState<Section[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [projectName, setProjectName] = useState(
    "Case Report " + localStorage.getItem("patientId")
  );
  const [projectDescription, setProjectDescription] = useState("");
  const [doctors, setDoctors] = useState<
    Array<{ id: string; name: string; email: string }>
  >([]);
  const [selectedDoctor, setSelectedDoctor] = useState<string>("");

  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.getDoctors();
        if (response.data) {
          setDoctors(response.data);
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
      }
    };

    fetchDoctors();
  }, []);

  // Initialize TipTap editor
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: { levels: [1, 2, 3] },
        bulletList: { keepMarks: true, keepAttributes: true },
        orderedList: { keepMarks: true, keepAttributes: true },
      }),
      Underline,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Table,
      TableRow,
      TableCell,
      TableHeader,
    ],
    content: "",
    editable: false,
    editorProps: {
      attributes: {
        class:
          "prose prose-sm max-w-none min-h-[600px] outline-none text-gray-800 font-['Inter'] whitespace-pre-wrap",
        spellCheck: "true",
        "aria-label": "Review Changes Document",
      },
    },
    parseOptions: { preserveWhitespace: "full" },
  });

  // Load sections from localStorage and update editor content
  useEffect(() => {
    const savedSections = localStorage.getItem("reportSections");
    if (savedSections) {
      try {
        const parsedSections = JSON.parse(savedSections);
        setSections(parsedSections);

        // Combine all sections into one document
        const combinedContent = parsedSections
          .map((section: Section) => `${section.body}`)
          .join("");

        editor?.commands.setContent(combinedContent);
      } catch (err) {
        setError("Failed to load sections from storage");
        console.error("Error loading sections:", err);
      }
    } else {
      setError("No sections found in storage");
    }
    setIsLoading(false);
  }, [editor]);

  // More efficient editor update handling using TipTap's transaction system
  useEffect(() => {
    if (editor && sections) {
      const handleUpdate = ({ transaction }: { transaction: any }) => {
        // Only process if content actually changed
        if (!transaction.docChanged) return;

        const doc = transaction.doc;
        let currentSectionIndex = -1;
        let currentContent = "";
        const updatedSections = [...sections];

        // Process each node in the document
        doc.descendants((node: any) => {
          if (node.type.name === "heading" && node.attrs.level === 2) {
            // If we were processing a previous section, save it
            if (currentSectionIndex >= 0) {
              updatedSections[currentSectionIndex] = {
                ...updatedSections[currentSectionIndex],
                body: currentContent.trim(),
              };
            }
            // Start new section
            currentSectionIndex++;
            currentContent = "";
          } else if (currentSectionIndex >= 0) {
            // Add content to current section
            currentContent += node.textContent;
          }
        });

        // Save the last section
        if (currentSectionIndex >= 0) {
          updatedSections[currentSectionIndex] = {
            ...updatedSections[currentSectionIndex],
            body: currentContent.trim(),
          };
        }

        setSections(updatedSections);
      };

      editor.on("transaction", handleUpdate);
      return () => {
        editor.off("transaction", handleUpdate);
      };
    }
  }, [editor, sections]);

  const handleSaveProject = async () => {
    try {
      setIsSaving(true);
      const sdk = new MkdSDK();

      if (selectedDoctor === "") {
        showToast(
          globalDispatch,
          "Please select a doctor to save the project",
          3000,
          ToastStatusEnum.WARNING
        );
        setIsSaving(false);
        return;
      }

      // Update sections sequentially with delay
      for (const section of sections) {
        await sdk.updateReportSection({
          id: section.id,
          title: section.title,
          content: section.body,
        });
        // Add a 2.5 second delay between updates
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      const projectId = localStorage.getItem("projectId");

      if (!projectId) {
        // Create new project
        await sdk.createProject({
          name: projectName,
          diagnosis: projectDescription || "",
          user_id: selectedDoctor,
          patient_id: localStorage.getItem("patientId") || "",
          patient_name: localStorage.getItem("patientName") || "",
          status: 1,
        });

        // Clear localStorage
        localStorage.removeItem("reportSections");
        localStorage.removeItem("projectId");
        localStorage.removeItem("patientId");
        localStorage.removeItem("patientName");
        localStorage.removeItem("reportId");

        navigate("/projects");
      } else {
        await sdk.assignProject({
          project_name: projectName,
          project_id: projectId,
          doctor_id: selectedDoctor,
        });
        // Clear localStorage
        localStorage.removeItem("reportSections");
        localStorage.removeItem("projectId");
        localStorage.removeItem("patientId");
        localStorage.removeItem("patientName");
        localStorage.removeItem("reportId");
        // Navigate to projects page
        navigate("/projects");
      }
    } catch (error) {
      console.error("Error saving project:", error);
      setError("Failed to save project. Please try again.");
    } finally {
      setIsSaving(false);
      setIsSaveModalOpen(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex max-w-[95%] mx-auto flex-col flex-shrink-0 justify-center items-start gap-6 border-0 border-gray-200 bg-black/0">
        {/* Header */}
        <div className="flex flex-shrink-0  items-center p-4 w-full border h-16 rounded-lg border-0 border-gray-200 bg-white">
          <h1 className="text-2xl mt- text-gray-800 font-['Inter']">
            Review Changes
          </h1>
        </div>
        {/* Main Content Area */}
        <div className="w-full h-full">
          <Toolbar editor={editor} />
          <EditorContent editor={editor} />
        </div>
        {/* Footer */}
        <div className="flex justify-between items-center w-full p-4 border-t border-gray-200">
          <span className="text-sm text-gray-600">
            Last edited by You - {new Date().toLocaleDateString()}
          </span>
          <div className="flex gap-3">
            <button
              onClick={() => setIsSaveModalOpen(true)}
              disabled={isSaving}
              className="px-6 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
            >
              {isSaving ? "Saving..." : "Submit"}
            </button>
          </div>
        </div>
      </div>

      <SaveProjectModal
        isOpen={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        onSave={handleSaveProject}
        projectName={projectName}
        setProjectName={setProjectName}
        projectDescription={projectDescription}
        setProjectDescription={setProjectDescription}
        isSaving={isSaving}
        doctors={doctors}
        selectedDoctor={selectedDoctor}
        setSelectedDoctor={setSelectedDoctor}
      />
    </div>
  );
};

export default ReviewChangesPage;
