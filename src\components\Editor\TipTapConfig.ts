import { Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";

export const createEditorConfig = (
  content: string,
  onUpdate: (html: string) => void
) => ({
  extensions: [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3],
      },
      bulletList: {
        keepMarks: true,
        keepAttributes: false,
      },
      orderedList: {
        keepMarks: true,
        keepAttributes: false,
      },
    }),
    Underline,
    TextAlign.configure({ types: ["heading", "paragraph"] }),
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableCell,
    TableHeader,
  ],
  content,
  onBlur: ({ editor }: { editor: Editor }) => {
    onUpdate(editor.getHTML());
  },
  editorProps: {
    attributes: {
      class:
        "prose prose-sm max-w-none min-h-[400px] outline-none p-4 text-gray-800 font-['Inter'] whitespace-pre-wrap",
      spellCheck: "true",
    },
    handleKeyDown: (view: any, event: KeyboardEvent) => {
      // Allow all key presses including space
      console.log("event", event);
      console.log("view", view);
      return false;
    },
  },
  parseOptions: {
    preserveWhitespace: "full",
  },
});
