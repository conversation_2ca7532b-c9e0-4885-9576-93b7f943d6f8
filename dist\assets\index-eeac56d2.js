import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as V,d as P,u as H}from"./vendor-489b60f1.js";import{u as M}from"./react-hook-form-7e42b371.js";import{o as Z}from"./yup-fe85ba88.js";import{c as S,a as h,b as k}from"./yup-5d8330af.js";import{a as R,u as E}from"./index-95f0e460.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const re=()=>{const{sdk:u}=R(),{showToast:f,tokenExpireError:g}=E(),[t,a]=V.useState(!1),C=P(),w=H(),j=new URLSearchParams(w.search).get("token"),y=S({password:h().required(),confirmPassword:h().oneOf([k("password")],"Passwords must match").required()}).required(),{register:i,handleSubmit:b,setError:l,formState:{errors:r}}=M({resolver:Z(y)}),v=async N=>{var n,d,c,m;try{a(!0);const s=await u.reset(N.password,j||"","user");if(!s.error)f("Password Reset Successfully"),C("/user/login");else if(s.validation){const p=Object.keys(s.validation);for(let o=0;o<p.length;o++){const x=p[o];l(x,{type:"manual",message:s.validation[x]})}}a(!1)}catch(s){a(!1),l("password",{type:"manual",message:((d=(n=s.response)==null?void 0:n.data)==null?void 0:d.message)??s.message}),g(((m=(c=s.response)==null?void 0:c.data)==null?void 0:m.message)??s.message)}};return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("header",{className:"text-center",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-800 flex items-center justify-center",children:e.jsxs("svg",{width:30,height:30,viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[e.jsx("g",{clipPath:"url(#clip0_1_28)",children:e.jsx("path",{d:"M10.7812 0C12.5918 0 14.0625 1.4707 14.0625 3.28125V26.7188C14.0625 28.5293 12.5918 30 10.7812 30C9.08789 30 7.69336 28.7168 7.51758 27.0645C7.21289 27.1465 6.89062 27.1875 6.5625 27.1875C4.49414 27.1875 2.8125 25.5059 2.8125 23.4375C2.8125 23.0039 2.88867 22.582 3.02344 22.1953C1.25391 21.5273 0 19.8164 0 17.8125C0 15.9434 1.0957 14.3262 2.68359 13.5762C2.17383 12.9375 1.875 12.1289 1.875 11.25C1.875 9.45117 3.14063 7.95117 4.82812 7.58203C4.73438 7.25977 4.6875 6.91406 4.6875 6.5625C4.6875 4.81055 5.89453 3.33398 7.51758 2.92383C7.69336 1.2832 9.08789 0 10.7812 0ZM19.2188 0C20.9121 0 22.3008 1.2832 22.4824 2.92383C24.1113 3.33398 25.3125 4.80469 25.3125 6.5625C25.3125 6.91406 25.2656 7.25977 25.1719 7.58203C26.8594 7.94531 28.125 9.45117 28.125 11.25C28.125 12.1289 27.8262 12.9375 27.3164 13.5762C28.9043 14.3262 30 15.9434 30 17.8125C30 19.8164 28.7461 21.5273 26.9766 22.1953C27.1113 22.582 27.1875 23.0039 27.1875 23.4375C27.1875 25.5059 25.5059 27.1875 23.4375 27.1875C23.1094 27.1875 22.7871 27.1465 22.4824 27.0645C22.3066 28.7168 20.9121 30 19.2188 30C17.4082 30 15.9375 28.5293 15.9375 26.7188V3.28125C15.9375 1.4707 17.4082 0 19.2188 0Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_28",children:e.jsx("path",{d:"M0 0H30V30H0V0Z",fill:"white"})})})]})})}),e.jsx("h2",{className:"mt-6 text-2xl font-semibold text-gray-800",children:"Reset Password"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Enter your new password"})]}),e.jsx("form",{className:"mt-8 bg-white p-8 rounded-xl shadow-sm",onSubmit:b(v),children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"New Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:e.jsx("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 0C10.2091 0 12 1.79086 12 4V6H13C14.1046 6 15 6.89543 15 8V14C15 15.1046 14.1046 16 13 16H3C1.89543 16 1 15.1046 1 14V8C1 6.89543 1.89543 6 3 6H4V4C4 1.79086 5.79086 0 8 0ZM13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7ZM8 9C8.55228 9 9 9.44772 9 10V12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12V10C7 9.44772 7.44772 9 8 9ZM8 1C6.34315 1 5 2.34315 5 4V6H11V4C11 2.34315 9.65685 1 8 1Z",fill:"#9CA3AF"})})}),e.jsx("input",{...i("password"),type:"password",required:!0,placeholder:"••••••••",className:`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${r.password?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),r.password&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.password.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:e.jsx("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 0C10.2091 0 12 1.79086 12 4V6H13C14.1046 6 15 6.89543 15 8V14C15 15.1046 14.1046 16 13 16H3C1.89543 16 1 15.1046 1 14V8C1 6.89543 1.89543 6 3 6H4V4C4 1.79086 5.79086 0 8 0ZM13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7ZM8 9C8.55228 9 9 9.44772 9 10V12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12V10C7 9.44772 7.44772 9 8 9ZM8 1C6.34315 1 5 2.34315 5 4V6H11V4C11 2.34315 9.65685 1 8 1Z",fill:"#9CA3AF"})})}),e.jsx("input",{...i("confirmPassword"),type:"password",required:!0,placeholder:"••••••••",className:`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${r.confirmPassword?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),r.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.confirmPassword.message})]})]}),e.jsx("button",{type:"submit",disabled:t,className:`w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${t?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-white",children:t?"Resetting...":"Reset Password"})})]})}),e.jsxs("div",{className:"text-center mt-4",children:[e.jsx("a",{href:"#",className:"text-sm text-gray-500 hover:text-gray-700",children:"Need help?"}),e.jsx("div",{className:"mt-4 border-t border-gray-200 pt-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Contact your administrator for support"})})]})]})})};export{re as default};
