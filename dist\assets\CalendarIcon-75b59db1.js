import{j as o}from"./@react-google-maps/api-5b2d83cc.js";import"./vendor-489b60f1.js";const d=({onClick:i,className:n,fill:e="none",stroke:t="#1F1D1A"})=>o.jsxs("svg",{onClick:i&&i,className:n,width:"18",height:"18",viewBox:"0 0 18 18",fill:e,xmlns:"http://www.w3.org/2000/svg",children:[o.jsx("path",{d:"M6 1.5V3.75",stroke:t,strokeWidth:"1.125",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M12 1.5V3.75",stroke:t,strokeWidth:"1.125",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M2.625 6.8175H15.375",stroke:t,strokeWidth:"1.125",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M15.75 6.375V12.75C15.75 15 14.625 16.5 12 16.5H6C3.375 16.5 2.25 15 2.25 12.75V6.375C2.25 4.125 3.375 2.625 6 2.625H12C14.625 2.625 15.75 4.125 15.75 6.375Z",stroke:t,strokeWidth:"1.125",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M11.7693 10.275H11.7761",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M11.7693 12.525H11.7761",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M8.99588 10.275H9.00262",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M8.99588 12.525H9.00262",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M6.22244 10.275H6.22918",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M6.22244 12.525H6.22918",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]});export{d as default};
