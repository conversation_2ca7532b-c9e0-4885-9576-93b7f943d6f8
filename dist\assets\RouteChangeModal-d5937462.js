import{j as o}from"./@react-google-maps/api-5b2d83cc.js";import{L as t,c as l,d as p}from"./index-95f0e460.js";import"./vendor-489b60f1.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const v=({isOpen:r=!1,title:a="change route",onClose:m,options:i=[{name:"",route:""}],modalClasses:e={modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"}})=>o.jsx(t,{children:o.jsx(l,{isOpen:r,modalCloseClick:m,title:a,modalHeader:!0,classes:e,children:r&&o.jsx(t,{children:o.jsx(p,{onClose:m,options:i})})})});export{v as RouteChangeModal,v as default};
