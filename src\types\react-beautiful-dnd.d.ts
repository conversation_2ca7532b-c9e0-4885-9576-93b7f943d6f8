declare module "react-beautiful-dnd" {
  import * as React from "react";

  // Draggable
  export interface DraggableProps {
    draggableId: string;
    index: number;
    children: (
      provided: DraggableProvided,
      snapshot: DraggableStateSnapshot
    ) => React.ReactNode;
  }

  export interface DraggableProvided {
    innerRef: (element: HTMLElement | null) => void;
    draggableProps: DraggableProvidedDraggableProps;
    dragHandleProps: DraggableProvidedDragHandleProps | null;
  }

  export interface DraggableProvidedDraggableProps {
    style?: React.CSSProperties;
    "data-rbd-draggable-context-id": string;
    "data-rbd-draggable-id": string;
  }

  export interface DraggableProvidedDragHandleProps {
    "data-rbd-drag-handle-draggable-id": string;
    "data-rbd-drag-handle-context-id": string;
    "aria-describedby": string;
    role: string;
    tabIndex: number;
    draggable: boolean;
    onDragStart: (event: React.DragEvent<HTMLElement>) => void;
  }

  export interface DraggableStateSnapshot {
    isDragging: boolean;
    isDropAnimating: boolean;
    draggingOver: string | null;
  }

  // Droppable
  export interface DroppableProps {
    droppableId: string;
    children: (
      provided: DroppableProvided,
      snapshot: DroppableStateSnapshot
    ) => React.ReactNode;
    type?: string;
    direction?: "vertical" | "horizontal";
  }

  export interface DroppableProvided {
    innerRef: (element: HTMLElement | null) => void;
    droppableProps: DroppableProvidedProps;
    placeholder?: React.ReactNode;
  }

  export interface DroppableProvidedProps {
    "data-rbd-droppable-context-id": string;
    "data-rbd-droppable-id": string;
  }

  export interface DroppableStateSnapshot {
    isDraggingOver: boolean;
    draggingOverWith: string | null;
    draggingFromThisWith: string | null;
    isUsingPlaceholder: boolean;
  }

  // DragDropContext
  export interface DragDropContextProps {
    onDragEnd: (result: DropResult) => void;
    onDragStart?: (initial: DragStart) => void;
    onDragUpdate?: (update: DragUpdate) => void;
    children: React.ReactNode;
  }

  export interface DragStart {
    draggableId: string;
    type: string;
    source: DraggableLocation;
  }

  export interface DragUpdate extends DragStart {
    destination?: DraggableLocation | null;
  }

  export interface DropResult extends DragUpdate {
    reason: "DROP" | "CANCEL";
  }

  export interface DraggableLocation {
    droppableId: string;
    index: number;
  }

  // Components
  export const Draggable: React.ComponentType<DraggableProps>;
  export const Droppable: React.ComponentType<DroppableProps>;
  export const DragDropContext: React.ComponentType<DragDropContextProps>;
}
