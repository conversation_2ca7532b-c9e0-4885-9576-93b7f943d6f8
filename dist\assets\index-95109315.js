import{j as e}from"./@react-google-maps/api-5b2d83cc.js";import{r as y,d as w,L as N}from"./vendor-489b60f1.js";import{u as b}from"./react-hook-form-7e42b371.js";import{o as v}from"./yup-fe85ba88.js";import{c as V,a,b as H}from"./yup-5d8330af.js";import{a as q,u as P,R as M}from"./index-95f0e460.js";import"./@hookform/resolvers-6b9dee20.js";import"./html2pdf.js-82514bbc.js";import"./@headlessui/react-15af3249.js";import"./react-loading-skeleton-b6c0da5e.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-209b94d8.js";import"./@fortawesome/react-fontawesome-78cc4a29.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@tanstack/react-query-dc4b6186.js";/* empty css                          *//* empty css                                */import"./@hotjar/browser-b90112fa.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const W=()=>{const{sdk:p}=q(),{showToast:h}=P(),[i,l]=y.useState(!1),g=w(),f=V({firstName:a().required("First name is required"),lastName:a().required("Last name is required"),email:a().email().required("Email is required"),password:a().required("Password is required").min(8,"Password must be at least 8 characters"),confirmPassword:a().oneOf([H("password")],"Passwords must match").required("Please confirm your password")}).required(),{register:t,handleSubmit:C,setError:n,formState:{errors:s}}=b({resolver:v(f)}),j=async d=>{var m,c;try{l(!0);const r=await p.register(d.email||"",d.password||"",M.USER);if(!r.error)h("Registration Successful"),g("/user/login");else if(r.validation){const u=Object.keys(r.validation);for(let o=0;o<u.length;o++){const x=u[o];n(x,{type:"manual",message:r.validation[x]})}}l(!1)}catch(r){l(!1),n("email",{type:"manual",message:((c=(m=r.response)==null?void 0:m.data)==null?void 0:c.message)??r.message})}};return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md md:h-full h-screen overflow-y-auto w-full space-y-8",children:[e.jsxs("header",{className:"text-center",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"w-16 h-16 rounded-full bg-gray-800 flex items-center justify-center",children:e.jsxs("svg",{width:30,height:30,viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:[e.jsx("g",{clipPath:"url(#clip0_1_28)",children:e.jsx("path",{d:"M10.7812 0C12.5918 0 14.0625 1.4707 14.0625 3.28125V26.7188C14.0625 28.5293 12.5918 30 10.7812 30C9.08789 30 7.69336 28.7168 7.51758 27.0645C7.21289 27.1465 6.89062 27.1875 6.5625 27.1875C4.49414 27.1875 2.8125 25.5059 2.8125 23.4375C2.8125 23.0039 2.88867 22.582 3.02344 22.1953C1.25391 21.5273 0 19.8164 0 17.8125C0 15.9434 1.0957 14.3262 2.68359 13.5762C2.17383 12.9375 1.875 12.1289 1.875 11.25C1.875 9.45117 3.14063 7.95117 4.82812 7.58203C4.73438 7.25977 4.6875 6.91406 4.6875 6.5625C4.6875 4.81055 5.89453 3.33398 7.51758 2.92383C7.69336 1.2832 9.08789 0 10.7812 0ZM19.2188 0C20.9121 0 22.3008 1.2832 22.4824 2.92383C24.1113 3.33398 25.3125 4.80469 25.3125 6.5625C25.3125 6.91406 25.2656 7.25977 25.1719 7.58203C26.8594 7.94531 28.125 9.45117 28.125 11.25C28.125 12.1289 27.8262 12.9375 27.3164 13.5762C28.9043 14.3262 30 15.9434 30 17.8125C30 19.8164 28.7461 21.5273 26.9766 22.1953C27.1113 22.582 27.1875 23.0039 27.1875 23.4375C27.1875 25.5059 25.5059 27.1875 23.4375 27.1875C23.1094 27.1875 22.7871 27.1465 22.4824 27.0645C22.3066 28.7168 20.9121 30 19.2188 30C17.4082 30 15.9375 28.5293 15.9375 26.7188V3.28125C15.9375 1.4707 17.4082 0 19.2188 0Z",fill:"white"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_1_28",children:e.jsx("path",{d:"M0 0H30V30H0V0Z",fill:"white"})})})]})})}),e.jsx("h2",{className:"mt-6 text-2xl font-semibold text-gray-800",children:"Create Account"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Sign up for a psychometrist account"})]}),e.jsx("form",{className:"mt-8 bg-white p-8 rounded-xl shadow-sm",onSubmit:C(j),children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("input",{...t("firstName"),type:"text",placeholder:"Enter your first name",required:!0,className:`w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-1 ${s.firstName?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),s.firstName&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.firstName.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("input",{...t("lastName"),type:"text",placeholder:"Enter your last name",required:!0,className:`w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-1 ${s.lastName?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),s.lastName&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.lastName.message})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:e.jsx("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2 3.5C1.725 3.5 1.5 3.725 1.5 4V4.69063L6.89062 9.11563C7.5375 9.64688 8.46562 9.64688 9.1125 9.11563L14.5 4.69063V4C14.5 3.725 14.275 3.5 14 3.5H2ZM1.5 6.63125V12C1.5 12.275 1.725 12.5 2 12.5H14C14.275 12.5 14.5 12.275 14.5 12V6.63125L10.0625 10.275C8.8625 11.2594 7.13438 11.2594 5.9375 10.275L1.5 6.63125ZM0 4C0 2.89688 0.896875 2 2 2H14C15.1031 2 16 2.89688 16 4V12C16 13.1031 15.1031 14 14 14H2C0.896875 14 0 13.1031 0 12V4Z",fill:"#9CA3AF"})})}),e.jsx("input",{...t("email"),type:"email",required:!0,placeholder:"<EMAIL>",className:`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${s.email?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),s.email&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.email.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:e.jsx("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 0C10.2091 0 12 1.79086 12 4V6H13C14.1046 6 15 6.89543 15 8V14C15 15.1046 14.1046 16 13 16H3C1.89543 16 1 15.1046 1 14V8C1 6.89543 1.89543 6 3 6H4V4C4 1.79086 5.79086 0 8 0ZM13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7ZM8 9C8.55228 9 9 9.44772 9 10V12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12V10C7 9.44772 7.44772 9 8 9ZM8 1C6.34315 1 5 2.34315 5 4V6H11V4C11 2.34315 9.65685 1 8 1Z",fill:"#9CA3AF"})})}),e.jsx("input",{...t("password"),type:"password",required:!0,placeholder:"••••••••",className:`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${s.password?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),s.password&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.password.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:e.jsx("svg",{width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 0C10.2091 0 12 1.79086 12 4V6H13C14.1046 6 15 6.89543 15 8V14C15 15.1046 14.1046 16 13 16H3C1.89543 16 1 15.1046 1 14V8C1 6.89543 1.89543 6 3 6H4V4C4 1.79086 5.79086 0 8 0ZM13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7ZM8 9C8.55228 9 9 9.44772 9 10V12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12V10C7 9.44772 7.44772 9 8 9ZM8 1C6.34315 1 5 2.34315 5 4V6H11V4C11 2.34315 9.65685 1 8 1Z",fill:"#9CA3AF"})})}),e.jsx("input",{...t("confirmPassword"),type:"password",required:!0,placeholder:"••••••••",className:`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${s.confirmPassword?"border-red-500 focus:ring-red-500":"focus:ring-gray-500"}`}),s.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.confirmPassword.message})]})]}),e.jsx("button",{type:"submit",disabled:i,className:`w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${i?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-white",children:i?"Creating Account...":"Create Account"})}),e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",e.jsx(N,{to:"/user/login",className:"text-gray-800 hover:text-gray-900 font-medium",children:"Sign in"})]})})]})}),e.jsxs("div",{className:"text-center mt-4",children:[e.jsx("a",{href:"#",className:"text-sm text-gray-500 hover:text-gray-700",children:"Need help?"}),e.jsx("div",{className:"mt-4 border-t border-gray-200 pt-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Contact your administrator for support"})})]})]})})};export{W as default};
