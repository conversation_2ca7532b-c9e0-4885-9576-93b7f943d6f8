// import { Extension } from "@tiptap/core";
// import { Plug<PERSON>, Plugin<PERSON><PERSON> } from "@tiptap/pm/state";
// import { Decoration, DecorationSet } from "@tiptap/pm/view";

// export interface ChangeHighlightOptions {
//   showNewChanges: boolean;
//   showOriginalAI: boolean;
//   originalContent: string;
// }

// declare module "@tiptap/core" {
//   interface Commands<ReturnType> {
//     changeHighlight: {
//       setShowNewChanges: (show: boolean) => ReturnType;
//       setShowOriginalAI: (show: boolean) => ReturnType;
//       setOriginalContent: (content: string) => ReturnType;
//     };
//   }
// }

// export const ChangeHighlight = Extension.create<ChangeHighlightOptions>({
//   name: "changeHighlight",

//   addOptions() {
//     return {
//       showNewChanges: false,
//       showOriginalAI: false,
//       originalContent: "",
//     };
//   },

//   addCommands() {
//     return {
//       setShowNewChanges:
//         (show: boolean) =>
//         ({ commands }) => {
//           this.options.showNewChanges = show;
//           return true;
//         },
//       setShowOriginalAI:
//         (show: boolean) =>
//         ({ commands }) => {
//           this.options.showOriginalAI = show;
//           return true;
//         },
//       setOriginalContent:
//         (content: string) =>
//         ({ commands }) => {
//           this.options.originalContent = content;
//           return true;
//         },
//     };
//   },

//   addProseMirrorPlugins() {
//     return [
//       new Plugin({
//         key: new PluginKey("changeHighlight"),
//         props: {
//           decorations: (state) => {
//             if (!this.options.showNewChanges && !this.options.showOriginalAI) {
//               return DecorationSet.empty;
//             }

//             const { doc } = state;
//             const decorations: Decoration[] = [];

//             // Compare current content with original content
//             const currentContent = doc.textContent;
//             const originalContent = this.options.originalContent;

//             if (this.options.showNewChanges) {
//               // Find differences and highlight new changes
//               const diff = this.findDifferences(
//                 currentContent,
//                 originalContent
//               );
//               diff.forEach(({ from, to }) => {
//                 decorations.push(
//                   Decoration.inline(from, to, {
//                     class: "bg-green-100",
//                   })
//                 );
//               });
//             }

//             if (this.options.showOriginalAI) {
//               // Find original AI content and highlight it
//               const originalDiff = this.findDifferences(
//                 originalContent,
//                 currentContent
//               );
//               originalDiff.forEach(({ from, to }) => {
//                 decorations.push(
//                   Decoration.inline(from, to, {
//                     class: "bg-yellow-100",
//                   })
//                 );
//               });
//             }

//             return DecorationSet.create(doc, decorations);
//           },
//         },
//       }),
//     ];
//   },

//   findDifferences(newText: string, oldText: string) {
//     const differences: { from: number; to: number }[] = [];
//     let currentPos = 0;

//     // Simple diff implementation - can be improved with more sophisticated diffing
//     const newWords = newText.split(/\s+/);
//     const oldWords = oldText.split(/\s+/);

//     let i = 0;
//     while (i < newWords.length) {
//       if (i >= oldWords.length || newWords[i] !== oldWords[i]) {
//         const start = currentPos;
//         let end = currentPos + newWords[i].length;

//         // Try to find the next matching word
//         let j = i + 1;
//         while (
//           j < newWords.length &&
//           j < oldWords.length &&
//           newWords[j] !== oldWords[j]
//         ) {
//           end += newWords[j].length + 1; // +1 for the space
//           j++;
//         }

//         differences.push({ from: start, to: end });
//         i = j;
//       } else {
//         currentPos += newWords[i].length + 1; // +1 for the space
//         i++;
//       }
//     }

//     return differences;
//   },
// });
