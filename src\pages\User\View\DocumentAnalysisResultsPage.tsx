import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";

// Types for document analysis results
interface DocumentMatch {
  title: string;
  file: string;
  match: number;
  type: "success" | "warning";
}

interface DocumentCategory {
  category: string;
  items: DocumentMatch[];
  icon: React.ReactNode;
}

// API response type
interface AnalysisResponse {
  error: boolean;
  message?: string;
  data?: Array<{
    category: string;
    items: Array<{
      title: string;
      file: string;
      match: number;
    }>;
  }>;
}

const DocumentAnalysisResults: React.FC = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [documentCategories, setDocumentCategories] = useState<
    DocumentCategory[]
  >([]);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  // Icons for different categories
  const categoryIcons = {
    "Cognitive Tests": (
      <svg
        width={16}
        height={16}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1_320)">
          <path
            d="M5.75 0C6.71562 0 7.5 0.784375 7.5 1.75V14.25C7.5 15.2156 6.71562 16 5.75 16C4.84688 16 4.10313 15.3156 4.00938 14.4344C3.84688 14.4781 3.675 14.5 3.5 14.5C2.39687 14.5 1.5 13.6031 1.5 12.5C1.5 12.2688 1.54062 12.0437 1.6125 11.8375C0.66875 11.4812 0 10.5688 0 9.5C0 8.50313 0.584375 7.64062 1.43125 7.24062C1.15937 6.9 1 6.46875 1 6C1 5.04063 1.675 4.24062 2.575 4.04375C2.525 3.87187 2.5 3.6875 2.5 3.5C2.5 2.56562 3.14375 1.77813 4.00938 1.55938C4.10313 0.684375 4.84688 0 5.75 0ZM10.25 0C11.1531 0 11.8938 0.684375 11.9906 1.55938C12.8594 1.77813 13.5 2.5625 13.5 3.5C13.5 3.6875 13.475 3.87187 13.425 4.04375C14.325 4.2375 15 5.04063 15 6C15 6.46875 14.8406 6.9 14.5688 7.24062C15.4156 7.64062 16 8.50313 16 9.5C16 10.5688 15.3313 11.4812 14.3875 11.8375C14.4594 12.0437 14.5 12.2688 14.5 12.5C14.5 13.6031 13.6031 14.5 12.5 14.5C12.325 14.5 12.1531 14.4781 11.9906 14.4344C11.8969 15.3156 11.1531 16 10.25 16C9.28438 16 8.5 15.2156 8.5 14.25V1.75C8.5 0.784375 9.28438 0 10.25 0Z"
            fill="#374151"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_320">
            <path d="M0 0H16V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    "Academic Tests": (
      <svg
        width={14}
        height={16}
        viewBox="0 0 14 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1_326)">
          <path
            d="M3 0C1.34375 0 0 1.34375 0 3V13C0 14.6562 1.34375 16 3 16H12H13C13.5531 16 14 15.5531 14 15C14 14.4469 13.5531 14 13 14V12C13.5531 12 14 11.5531 14 11V1C14 0.446875 13.5531 0 13 0H12H3ZM3 12H11V14H3C2.44688 14 2 13.5531 2 13C2 12.4469 2.44688 12 3 12ZM4 4.5C4 4.225 4.225 4 4.5 4H10.5C10.775 4 11 4.225 11 4.5C11 4.775 10.775 5 10.5 5H4.5C4.225 5 4 4.775 4 4.5ZM4.5 6H10.5C10.775 6 11 6.225 11 6.5C11 6.775 10.775 7 10.5 7H4.5C4.225 7 4 6.775 4 6.5C4 6.225 4.225 6 4.5 6Z"
            fill="#374151"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_326">
            <path d="M0 0H14V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    Inventories: (
      <svg
        width={12}
        height={16}
        viewBox="0 0 12 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1_332)">
          <path
            d="M6 0C4.69375 0 3.58125 0.834375 3.17188 2H2C0.896875 2 0 2.89687 0 4V14C0 15.1031 0.896875 16 2 16H10C11.1031 16 12 15.1031 12 14V4C12 2.89687 11.1031 2 10 2H8.82812C8.41875 0.834375 7.30625 0 6 0ZM6 2C6.26522 2 6.51957 2.10536 6.70711 2.29289C6.89464 2.48043 7 2.73478 7 3C7 3.26522 6.89464 3.51957 6.70711 3.70711C6.51957 3.89464 6.26522 4 6 4C5.73478 4 5.48043 3.89464 5.29289 3.70711C5.10536 3.51957 5 3.26522 5 3C5 2.73478 5.10536 2.48043 5.29289 2.29289C5.48043 2.10536 5.73478 2 6 2ZM2.25 8.5C2.25 8.30109 2.32902 8.11032 2.46967 7.96967C2.61032 7.82902 2.80109 7.75 3 7.75C3.19891 7.75 3.38968 7.82902 3.53033 7.96967C3.67098 8.11032 3.75 8.30109 3.75 8.5C3.75 8.69891 3.67098 8.88968 3.53033 9.03033C3.38968 9.17098 3.19891 9.25 3 9.25C2.80109 9.25 2.61032 9.17098 2.46967 9.03033C2.32902 8.88968 2.25 8.69891 2.25 8.5ZM5.5 8H9.5C9.775 8 10 8.225 10 8.5C10 8.775 9.775 9 9.5 9H5.5C5.225 9 5 8.775 5 8.5C5 8.225 5.225 8 5.5 8ZM2.25 11.5C2.25 11.3011 2.32902 11.1103 2.46967 10.9697C2.61032 10.829 2.80109 10.75 3 10.75C3.19891 10.75 3.38968 10.829 3.53033 10.9697C3.67098 11.1103 3.75 11.3011 3.75 11.5C3.75 11.6989 3.67098 11.8897 3.53033 12.0303C3.38968 12.171 3.19891 12.25 3 12.25C2.80109 12.25 2.61032 12.171 2.46967 12.0303C2.32902 11.8897 2.25 11.6989 2.25 11.5ZM5 11.5C5 11.225 5.225 11 5.5 11H9.5C9.775 11 10 11.225 10 11.5C10 11.775 9.775 12 9.5 12H5.5C5.225 12 5 11.775 5 11.5Z"
            fill="#374151"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_332">
            <path d="M0 0H12V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    "Behavioral Observation Scales": (
      <svg
        width={18}
        height={16}
        viewBox="0 0 18 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1_338)">
          <path
            d="M8.99995 1C6.47495 1 4.45308 2.15 2.9812 3.51875C1.5187 4.875 0.540576 6.5 0.0780762 7.61562C-0.0250488 7.8625 -0.0250488 8.1375 0.0780762 8.38437C0.540576 9.5 1.5187 11.125 2.9812 12.4812C4.45308 13.85 6.47495 15 8.99995 15C11.525 15 13.5468 13.85 15.0187 12.4812C16.4812 11.1219 17.4593 9.5 17.9249 8.38437C18.0281 8.1375 18.0281 7.8625 17.9249 7.61562C17.4593 6.5 16.4812 4.875 15.0187 3.51875C13.5468 2.15 11.525 1 8.99995 1ZM4.49995 8C4.49995 6.80653 4.97406 5.66193 5.81797 4.81802C6.66188 3.97411 7.80648 3.5 8.99995 3.5C10.1934 3.5 11.338 3.97411 12.1819 4.81802C13.0258 5.66193 13.5 6.80653 13.5 8C13.5 9.19347 13.0258 10.3381 12.1819 11.182C11.338 12.0259 10.1934 12.5 8.99995 12.5C7.80648 12.5 6.66188 12.0259 5.81797 11.182C4.97406 10.3381 4.49995 9.19347 4.49995 8ZM8.99995 6C8.99995 7.10313 8.10308 8 6.99995 8C6.77808 8 6.56558 7.9625 6.36558 7.89687C6.1937 7.84062 5.9937 7.94688 5.99995 8.12813C6.00933 8.34375 6.04058 8.55937 6.09995 8.775C6.52808 10.375 8.17495 11.325 9.77495 10.8969C11.375 10.4688 12.325 8.82188 11.8968 7.22188C11.55 5.925 10.4031 5.05312 9.12808 5C8.94683 4.99375 8.84058 5.19062 8.89683 5.36562C8.96245 5.56562 8.99995 5.77812 8.99995 6Z"
            fill="#374151"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_338">
            <path d="M0 0H18V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    "Background Info Sources": (
      <svg
        width={18}
        height={16}
        viewBox="0 0 18 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.77187 6.99375L0 11.7437V3C0 1.89688 0.896875 1 2 1H5.67188C6.20312 1 6.7125 1.20938 7.0875 1.58438L7.91563 2.4125C8.29062 2.7875 8.8 2.99688 9.33125 2.99688H13C14.1031 2.99688 15 3.89375 15 4.99687V5.99687H4.5C3.7875 5.99687 3.13125 6.375 2.77187 6.99062V6.99375ZM3.63438 7.49687C3.81563 7.1875 4.14375 7 4.5 7H17C17.3594 7 17.6875 7.19063 17.8656 7.50313C18.0438 7.81563 18.0437 8.19688 17.8625 8.50625L14.3625 14.5063C14.1844 14.8125 13.8562 15 13.5 15H1C0.640625 15 0.3125 14.8094 0.134375 14.4969C-0.04375 14.1844 -0.04375 13.8031 0.1375 13.4937L3.6375 7.49375L3.63438 7.49687Z"
          fill="#374151"
        />
      </svg>
    ),
  };

  useEffect(() => {
    const fetchAnalysisResults = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await sdk?.analyzeDocument(
          localStorage.getItem("patientId") || ""
        );
        if (!response || response.error) {
          throw new Error(
            response?.message || "Failed to fetch analysis results"
          );
        }

        // Map API response to our UI data structure
        const categories =
          (response as AnalysisResponse).data?.map((category) => {
            const mappedCategory: DocumentCategory = {
              category: category.category,
              icon:
                categoryIcons[
                  category.category as keyof typeof categoryIcons
                ] || categoryIcons["Background Info Sources"],
              items: category.items.map((item) => ({
                title: item.title,
                file: item.file,
                match: item.match,
                type: item.match >= 90 ? "success" : "warning",
              })),
            };
            return mappedCategory;
          }) || [];

        setDocumentCategories(categories);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch analysis results"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysisResults();
  }, [sdk]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <p className="text-gray-800 font-medium mb-2">
            Error Loading Results
          </p>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex  justify-center bg-gray-50 py-8 px-2">
      <div className="w-full max-w-[95%] bg-white rounded-xl shadow p-8 flex flex-col gap-y-8">
        {/* Header Section */}
        <div className="flex flex-col gap-2 mb-4">
          <h1 className="text-2xl font-semibold text-gray-800">
            Document Analysis Results
          </h1>
          <p className="text-gray-600 text-base">
            We've analyzed your uploaded files and detected the following
            assessments
          </p>
        </div>
        {/* Document Categories */}
        {documentCategories.map((category, index) => (
          <div
            key={index}
            className="flex flex-col gap-4 p-6 rounded-lg border border-gray-200 bg-white"
          >
            {/* Category Header */}
            <div className="flex items-center gap-2 mb-2">
              <div className="mt-3">{category.icon}</div>
              <h2 className="text-lg flex-1 font-semibold text-gray-800">
                {category.category}
              </h2>
            </div>
            {/* Document List */}
            {category.items.map((doc, docIndex) => (
              <div
                key={docIndex}
                className="flex items-center justify-between p-4 rounded-lg border border-gray-100 bg-gray-50"
              >
                <div>
                  <div className="font-medium text-gray-800">{doc.title}</div>
                  <div className="text-sm text-gray-600">File: {doc.file}</div>
                </div>
                <div className="flex items-center gap-4">
                  <div
                    className={`flex items-center gap-1 px-2 py-1 rounded-full ${doc.type === "success" ? "bg-emerald-100" : "bg-amber-100"}`}
                  >
                    <span
                      className={`text-xs ${doc.type === "success" ? "text-emerald-800" : "text-amber-800"}`}
                    >
                      {doc.match}% Match
                    </span>
                  </div>
                  <button
                    type="button"
                    className="p-1"
                    aria-label="More options"
                  >
                    <svg
                      width={4}
                      height={16}
                      viewBox="0 0 4 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M4 16H0V0H4V16Z" stroke="#E5E7EB" />
                      <path
                        d="M2 11.25C1.53587 11.25 1.09075 11.4344 0.762563 11.7626C0.434374 12.0908 0.25 12.5359 0.25 13C0.25 13.4641 0.434374 13.9092 0.762563 14.2374C1.09075 14.5656 1.53587 14.75 2 14.75C2.46413 14.75 2.90925 14.5656 3.23744 14.2374C3.56563 13.9092 3.75 13.4641 3.75 13C3.75 12.5359 3.56563 12.0908 3.23744 11.7626C2.90925 11.4344 2.46413 11.25 2 11.25ZM2 6.25C1.53587 6.25 1.09075 6.43437 0.762563 6.76256C0.434374 7.09075 0.25 7.53587 0.25 8C0.25 8.46413 0.434374 8.90925 0.762563 9.23744C1.09075 9.56563 1.53587 9.75 2 9.75C2.46413 9.75 2.90925 9.56563 3.23744 9.23744C3.56563 8.90925 3.75 8.46413 3.75 8C3.75 7.53587 3.56563 7.09075 3.23744 6.76256C2.90925 6.43437 2.46413 6.25 2 6.25ZM3.75 3C3.75 2.53587 3.56563 2.09075 3.23744 1.76256C2.90925 1.43437 2.46413 1.25 2 1.25C1.53587 1.25 1.09075 1.43437 0.762563 1.76256C0.434374 2.09075 0.25 2.53587 0.25 3C0.25 3.46413 0.434374 3.90925 0.762563 4.23744C1.09075 4.56563 1.53587 4.75 2 4.75C2.46413 4.75 2.90925 4.56563 3.23744 4.23744C3.56563 3.90925 3.75 3.46413 3.75 3Z"
                        fill="#6B7280"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        ))}
        {/* Action Buttons */}
        <div className="flex justify-between gap-4 mt-8">
          <button
            type="button"
            className="flex items-center gap-2 px-6 py-3 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
            onClick={() => navigate(-1)}
          >
            <svg
              width={14}
              height={16}
              viewBox="0 0 14 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.293701 7.29365C-0.0969238 7.68428 -0.0969238 8.31865 0.293701 8.70928L5.2937 13.7093C5.68433 14.0999 6.3187 14.0999 6.70933 13.7093C7.09995 13.3187 7.09995 12.6843 6.70933 12.2937L3.41245 8.9999H13C13.5531 8.9999 14 8.55303 14 7.9999C14 7.44678 13.5531 6.9999 13 6.9999H3.41558L6.7062 3.70615C7.09683 3.31553 7.09683 2.68115 6.7062 2.29053C6.31558 1.8999 5.6812 1.8999 5.29058 2.29053L0.290576 7.29053L0.293701 7.29365Z"
                fill="#374151"
              />
            </svg>
            <span>Go Back</span>
          </button>
          <button
            type="button"
            disabled={isGeneratingReport}
            className={`flex items-center gap-2 px-6 py-3 rounded-lg ${
              isGeneratingReport
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-gray-800 hover:bg-gray-700"
            } text-white transition-colors duration-200`}
            onClick={async () => {
              try {
                setIsGeneratingReport(true);
                const patientId = localStorage.getItem("patientId");
                if (!patientId) {
                  throw new Error("Patient ID not found");
                }

                const response = await sdk?.generateAIReport({
                  patient_id: patientId,
                  user_id: Number(localStorage.getItem("user")),
                  project_id: Number(localStorage.getItem("projectId")),
                });

                if (!response || response.error) {
                  throw new Error(
                    response?.message || "Failed to generate AI report"
                  );
                }

                // Store the job_id for status polling
                localStorage.setItem("patientId", patientId);
                localStorage.setItem("jobId", response.job_id);
                localStorage.setItem(
                  "projectId",
                  localStorage.getItem("projectId") || ""
                );
                localStorage.removeItem("reportId")

                // Navigate directly to AI report page which will handle polling
                navigate("/member/ai-report");
              } catch (error) {
                console.error("Error generating AI report:", error);
                // You may want to show an error message to the user here
              } finally {
                setIsGeneratingReport(false);
              }
            }}
          >
            {isGeneratingReport ? (
              <>
                <svg
                  className="animate-spin h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Generating Report...</span>
              </>
            ) : (
              <>
                <span>Generate AI Report</span>
                <svg
                  width={14}
                  height={16}
                  viewBox="0 0 14 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.7063 8.70615C14.0969 8.31553 14.0969 7.68115 13.7063 7.29053L8.70625 2.29053C8.31563 1.8999 7.68125 1.8999 7.29063 2.29053C6.9 2.68115 6.9 3.31553 7.29063 3.70615L10.5875 6.9999H1C0.446875 6.9999 0 7.44678 0 7.9999C0 8.55303 0.446875 8.9999 1 8.9999H10.5844L7.29375 12.2937C6.90312 12.6843 6.90312 13.3187 7.29375 13.7093C7.68437 14.0999 8.31875 14.0999 8.70938 13.7093L13.7094 8.70928L13.7063 8.70615Z"
                    fill="white"
                  />
                </svg>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DocumentAnalysisResults;
