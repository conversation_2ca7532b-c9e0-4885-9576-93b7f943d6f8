import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useNavigate, useLocation } from "react-router-dom";
import { useContexts } from "@/hooks/useContexts";
import { useSDK } from "@/hooks/useSDK";

const UserResetPage = () => {
  const { sdk } = useSDK();
  const { showToast, tokenExpireError } = useContexts();
  const [submitLoading, setSubmitLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const token = searchParams.get("token");

  const schema = yup
    .object({
      password: yup.string().required(),
      confirmPassword: yup
        .string()
        .oneOf([yup.ref("password")], "Passwords must match")
        .required(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.reset(data.password, token || "", "user");

      if (!result.error) {
        showToast("Password Reset Successfully");
        navigate("/user/login");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field as "password" | "confirmPassword", {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      setSubmitLoading(false);
    } catch (error: any) {
      setSubmitLoading(false);
      setError("password", {
        type: "manual",
        message: error.response?.data?.message ?? error.message,
      });
      tokenExpireError(error.response?.data?.message ?? error.message);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header Section */}
        <header className="text-center">
          <div className="flex justify-center">
            <div className="w-16 h-16 rounded-full bg-gray-800 flex items-center justify-center">
              <svg
                width={30}
                height={30}
                viewBox="0 0 30 30"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
              >
                <g clipPath="url(#clip0_1_28)">
                  <path
                    d="M10.7812 0C12.5918 0 14.0625 1.4707 14.0625 3.28125V26.7188C14.0625 28.5293 12.5918 30 10.7812 30C9.08789 30 7.69336 28.7168 7.51758 27.0645C7.21289 27.1465 6.89062 27.1875 6.5625 27.1875C4.49414 27.1875 2.8125 25.5059 2.8125 23.4375C2.8125 23.0039 2.88867 22.582 3.02344 22.1953C1.25391 21.5273 0 19.8164 0 17.8125C0 15.9434 1.0957 14.3262 2.68359 13.5762C2.17383 12.9375 1.875 12.1289 1.875 11.25C1.875 9.45117 3.14063 7.95117 4.82812 7.58203C4.73438 7.25977 4.6875 6.91406 4.6875 6.5625C4.6875 4.81055 5.89453 3.33398 7.51758 2.92383C7.69336 1.2832 9.08789 0 10.7812 0ZM19.2188 0C20.9121 0 22.3008 1.2832 22.4824 2.92383C24.1113 3.33398 25.3125 4.80469 25.3125 6.5625C25.3125 6.91406 25.2656 7.25977 25.1719 7.58203C26.8594 7.94531 28.125 9.45117 28.125 11.25C28.125 12.1289 27.8262 12.9375 27.3164 13.5762C28.9043 14.3262 30 15.9434 30 17.8125C30 19.8164 28.7461 21.5273 26.9766 22.1953C27.1113 22.582 27.1875 23.0039 27.1875 23.4375C27.1875 25.5059 25.5059 27.1875 23.4375 27.1875C23.1094 27.1875 22.7871 27.1465 22.4824 27.0645C22.3066 28.7168 20.9121 30 19.2188 30C17.4082 30 15.9375 28.5293 15.9375 26.7188V3.28125C15.9375 1.4707 17.4082 0 19.2188 0Z"
                    fill="white"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1_28">
                    <path d="M0 0H30V30H0V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <h2 className="mt-6 text-2xl font-semibold text-gray-800">
            Reset Password
          </h2>
          <p className="mt-2 text-sm text-gray-500">Enter your new password</p>
        </header>

        {/* Form Section */}
        <form
          className="mt-8 bg-white p-8 rounded-xl shadow-sm"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="space-y-6">
            {/* Password Field */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                New Password
              </label>
              <div className="mt-1 relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2">
                  <svg
                    width={16}
                    height={16}
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8 0C10.2091 0 12 1.79086 12 4V6H13C14.1046 6 15 6.89543 15 8V14C15 15.1046 14.1046 16 13 16H3C1.89543 16 1 15.1046 1 14V8C1 6.89543 1.89543 6 3 6H4V4C4 1.79086 5.79086 0 8 0ZM13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7ZM8 9C8.55228 9 9 9.44772 9 10V12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12V10C7 9.44772 7.44772 9 8 9ZM8 1C6.34315 1 5 2.34315 5 4V6H11V4C11 2.34315 9.65685 1 8 1Z"
                      fill="#9CA3AF"
                    />
                  </svg>
                </span>
                <input
                  {...register("password")}
                  type="password"
                  required
                  placeholder="••••••••"
                  className={`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${
                    errors.password
                      ? "border-red-500 focus:ring-red-500"
                      : "focus:ring-gray-500"
                  }`}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
              </div>
            </div>

            {/* Confirm Password Field */}
            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700"
              >
                Confirm Password
              </label>
              <div className="mt-1 relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2">
                  <svg
                    width={16}
                    height={16}
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8 0C10.2091 0 12 1.79086 12 4V6H13C14.1046 6 15 6.89543 15 8V14C15 15.1046 14.1046 16 13 16H3C1.89543 16 1 15.1046 1 14V8C1 6.89543 1.89543 6 3 6H4V4C4 1.79086 5.79086 0 8 0ZM13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7ZM8 9C8.55228 9 9 9.44772 9 10V12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12V10C7 9.44772 7.44772 9 8 9ZM8 1C6.34315 1 5 2.34315 5 4V6H11V4C11 2.34315 9.65685 1 8 1Z"
                      fill="#9CA3AF"
                    />
                  </svg>
                </span>
                <input
                  {...register("confirmPassword")}
                  type="password"
                  required
                  placeholder="••••••••"
                  className={`pl-10 pr-3 py-2 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-1 ${
                    errors.confirmPassword
                      ? "border-red-500 focus:ring-red-500"
                      : "focus:ring-gray-500"
                  }`}
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={submitLoading}
              className={`w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${
                submitLoading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              <span className="text-white">
                {submitLoading ? "Resetting..." : "Reset Password"}
              </span>
            </button>
          </div>
        </form>

        {/* Help Section */}
        <div className="text-center mt-4">
          <a href="#" className="text-sm text-gray-500 hover:text-gray-700">
            Need help?
          </a>
          <div className="mt-4 border-t border-gray-200 pt-4">
            <p className="text-sm text-gray-600">
              Contact your administrator for support
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserResetPage;
