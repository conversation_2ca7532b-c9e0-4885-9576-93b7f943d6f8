import React, { useContext } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import { GlobalContext } from "@/context/Global";
import { AuthContext } from "@/context/Auth";

// Define navigation items for the member sidebar
const MEMBER_NAV_ITEMS = [
  {
    to: "/member/uploads",
    label: "Create New",
    icon: (
      <svg
        width={14}
        height={16}
        viewBox="0 0 14 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path d="M14 16H0V0H14V16Z" stroke="#E5E7EB" />
        <path
          d="M8 2.5C8 1.94687 7.55312 1.5 7 1.5C6.44688 1.5 6 1.94687 6 2.5V7H1.5C0.946875 7 0.5 7.44688 0.5 8C0.5 8.55312 0.946875 9 1.5 9H6V13.5C6 14.0531 6.44688 14.5 7 14.5C7.55312 14.5 8 14.0531 8 13.5V9H12.5C13.0531 9 13.5 8.55312 13.5 8C13.5 7.44688 13.0531 7 12.5 7H8V2.5Z"
          fill="#374151"
        />
      </svg>
    ),
  },
  {
    to: "/projects",
    label: "Saved Projects",
    icon: (
      <svg
        width={16}
        height={16}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path d="M16 16H0V0H16V16Z" stroke="#E5E7EB" />
        <path
          d="M0 3C0 1.89688 0.896875 1 2 1H6.12813C6.725 1 7.29688 1.2375 7.71875 1.65937L9.05937 3H14C15.1031 3 16 3.89687 16 5V13C16 14.1031 15.1031 15 14 15H2C0.896875 15 0 14.1031 0 13V3ZM2 2.5C1.725 2.5 1.5 2.725 1.5 3V13C1.5 13.275 1.725 13.5 2 13.5H14C14.275 13.5 14.5 13.275 14.5 13V5C14.5 4.725 14.275 4.5 14 4.5H8.95625C8.625 4.5 8.30625 4.36875 8.07187 4.13438L6.65938 2.71875C6.51875 2.57812 6.32812 2.5 6.12813 2.5H2Z"
          fill="#374151"
        />
      </svg>
    ),
  },
  {
    to: "/member/profile",
    label: "My Account",
    icon: (
      <svg
        width={14}
        height={16}
        viewBox="0 0 14 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <g clipPath="url(#clip0_1_290)">
          <path
            d="M9.5 4C9.5 3.33696 9.23661 2.70107 8.76777 2.23223C8.29893 1.76339 7.66304 1.5 7 1.5C6.33696 1.5 5.70107 1.76339 5.23223 2.23223C4.76339 2.70107 4.5 3.33696 4.5 4C4.5 4.66304 4.76339 5.29893 5.23223 5.76777C5.70107 6.23661 6.33696 6.5 7 6.5C7.66304 6.5 8.29893 6.23661 8.76777 5.76777C9.23661 5.29893 9.5 4.66304 9.5 4ZM3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4ZM1.54062 14.5H12.4594C12.1813 12.5219 10.4813 11 8.42813 11H5.57188C3.51875 11 1.81875 12.5219 1.54062 14.5ZM0 15.0719C0 11.9937 2.49375 9.5 5.57188 9.5H8.42813C11.5063 9.5 14 11.9937 14 15.0719C14 15.5844 13.5844 16 13.0719 16H0.928125C0.415625 16 0 15.5844 0 15.0719Z"
            fill="#374151"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_290">
            <rect width="14" height="16" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  },
];

export const MemberHeader: React.FC = () => {
  const {
    state: { isOpen },
    dispatch: globalDispatch,
  } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const navigate = useNavigate();

  // Toggle sidebar open/close
  const toggleOpen = () => {
    globalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: !isOpen },
    });
  };

  // Handle logout (placeholder)
  const handleLogout = () => {
    // TODO: Implement actual logout logic
    authDispatch({ type: "LOGOUT" });
    navigate("/user/login");
  };

  return (
    <nav
      className={`flex flex-col flex-shrink-0 justify-between items-start pr-px min-h-screen border-r border-r-[#e5e7eb] bg-white transition-all duration-300 ease-in-out ${
        isOpen ? "w-64" : "w-16"
      }`}
      aria-label="Sidebar"
    >
      <div className="w-full">
        {/* Header Section */}
        <div className={`flex relative flex-col justify-center items-start pb-7 p-6 border-b border-b-[#e5e7eb] bg-transparent ${
          isOpen ? "pr-20 w-[15.9375rem]" : "w-full h-14"
        }`}>
          <div className="flex justify-between items-center w-full">
            <div className={`flex justify-center items-center p-3 w-12 h-12 rounded-full bg-gray-800 ${isOpen ? "" : "hidden"}`}>
              <div className="flex flex-shrink-0 justify-center items-center w-6 h-6">
                <svg
                  width={24}
                  height={24}
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  aria-hidden="true"
                >
                  <g clipPath="url(#clip0_1_285)">
                    <path
                      d="M8.625 0C10.0734 0 11.25 1.17656 11.25 2.625V21.375C11.25 22.8234 10.0734 24 8.625 24C7.27031 24 6.15469 22.9734 6.01406 21.6516C5.77031 21.7172 5.5125 21.75 5.25 21.75C3.59531 21.75 2.25 20.4047 2.25 18.75C2.25 18.4031 2.31094 18.0656 2.41875 17.7562C1.00312 17.2219 0 15.8531 0 14.25C0 12.7547 0.876562 11.4609 2.14687 10.8609C1.73906 10.35 1.5 9.70312 1.5 9C1.5 7.56094 2.5125 6.36094 3.8625 6.06562C3.7875 5.80781 3.75 5.53125 3.75 5.25C3.75 3.84844 4.71563 2.66719 6.01406 2.33906C6.15469 1.02656 7.27031 0 8.625 0ZM15.375 0C16.7297 0 17.8406 1.02656 17.9859 2.33906C19.2891 2.66719 20.25 3.84375 20.25 5.25C20.25 5.53125 20.2125 5.80781 20.1375 6.06562C21.4875 6.35625 22.5 7.56094 22.5 9C22.5 9.70312 22.2609 10.35 21.8531 10.8609C23.1234 11.4609 24 12.7547 24 14.25C24 15.8531 22.9969 17.2219 21.5812 17.7562C21.6891 18.0656 21.75 18.4031 21.75 18.75C21.75 20.4047 20.4047 21.75 18.75 21.75C18.4875 21.75 18.2297 21.7172 17.9859 21.6516C17.8453 22.9734 16.7297 24 15.375 24C13.9266 24 12.75 22.8234 12.75 21.375V2.625C12.75 1.17656 13.9266 0 15.375 0Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_1_285">
                      <rect width="24" height="24" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
            
            {/* Toggle Button: only show when sidebar is open */}
            {isOpen && (
              <button
                onClick={toggleOpen}
                className={"flex absolute top-4 right-4 items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 transition-colors"}
                aria-label="Collapse sidebar"
              >
            <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
              <rect x="4" y="6" width="16" height="2" rx="1" fill="currentColor"/>
              <rect x="4" y="11" width="16" height="2" rx="1" fill="currentColor"/>
              <rect x="4" y="16" width="16" height="2" rx="1" fill="currentColor"/>
            </svg>
              </button>
            )}
          </div>
          
          {/* Title - only show when expanded */}
          {isOpen && (
            <div className="w-[8.8125rem] h-7 text-gray-800 font-['Inter'] text-xl font-semibold leading-7 mt-2">
              Psychometrist
            </div>
          )}
        </div>



        {/* Navigation Section */}
        <div className={`flex flex-col flex-shrink-0 items-start gap-x-8 gap-y-3 p-4 bg-transparent ${
          isOpen ? "w-[15.9375rem]" : "w-full"
        }`}>
          {MEMBER_NAV_ITEMS.map((item) => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `flex items-center gap-3 py-2 px-2 rounded-lg transition-colors w-full ${
                  isActive ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                }`
              }
              aria-label={item.label}
              title={!isOpen ? item.label : undefined}
            >
              {item.icon}
              {isOpen && (
                <span className="ml-2 font-['Inter'] leading-6">
                  {item.label}
                </span>
              )}
            </NavLink>
          ))}
        </div>
      </div>

      {/* Footer/Logout Section */}
      <div className={`flex-shrink-0 border-t border-t-[#e5e7eb] flex items-center bg-transparent ${
        isOpen ? "w-[15.9375rem] h-[4.5625rem] px-4" : "w-full h-12 justify-center"
      }`}>
        <button
          onClick={handleLogout}
          className="flex items-center gap-2 text-gray-700 hover:text-gray-900 focus:outline-none"
          aria-label="Logout"
          title={!isOpen ? "Logout" : undefined}
        >
          <svg
            width={16}
            height={16}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
          >
            <path d="M16 16H0V0H16V16Z" stroke="#E5E7EB" />
            <path
              d="M15.7063 8.70625C16.0969 8.31563 16.0969 7.68125 15.7063 7.29063L11.7063 3.29063C11.3156 2.9 10.6812 2.9 10.2906 3.29063C9.9 3.68125 9.9 4.31563 10.2906 4.70625L12.5844 7H6C5.44688 7 5 7.44688 5 8C5 8.55312 5.44688 9 6 9H12.5844L10.2906 11.2937C9.9 11.6844 9.9 12.3188 10.2906 12.7094C10.6812 13.1 11.3156 13.1 11.7063 12.7094L15.7063 8.70938V8.70625ZM5 3C5.55312 3 6 2.55313 6 2C6 1.44687 5.55312 1 5 1H3C1.34375 1 0 2.34375 0 4V12C0 13.6562 1.34375 15 3 15H5C5.55312 15 6 14.5531 6 14C6 13.4469 5.55312 13 5 13H3C2.44688 13 2 12.5531 2 12V4C2 3.44688 2.44688 3 3 3H5Z"
              fill="#374151"
            />
          </svg>
          {isOpen && (
            <span className="font-['Inter'] leading-6">Logout</span>
          )}
        </button>
      </div>
    </nav>
  );
};

export default MemberHeader;
