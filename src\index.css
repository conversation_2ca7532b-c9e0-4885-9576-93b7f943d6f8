@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* transition: all 0.5s ease-in-out; */
}

.tiptap table,
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

.tiptap table td,
.tiptap table th,
.ProseMirror table td,
.ProseMirror table th {
  min-width: 10em;
  border: 2px solid #ced4da;
  padding: 3px 5px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.tiptap table td > *,
.tiptap table th > *,
.ProseMirror table td > *,
.ProseMirror table th > * {
  margin-bottom: 0;
}

.tiptap table th,
.ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #f1f3f5;
}

.tiptap table .selectedCell:after,
.ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.tiptap table .column-resize-handle,
.ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.tiptap table p,
.ProseMirror table p {
  margin: 0 0 0.5rem 0;
}

.custom h1,
h2 {
  font-weight: bold !important;
  margin-top: 10px !important;
}
.custom table {
  border-collapse: collapse !important;
}

.custom table,
th,
td {
  padding: 10px !important;
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.custom ul,
.custom ol {
  padding-left: 2rem !important;
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}
.custom ul {
  list-style-type: disc !important;
}
.custom ol {
  list-style-type: decimal !important;
}
.custom li {
  margin-bottom: 0.25em !important;
  font-size: 1rem;
  color: #374151;
}

html {
  font-size: 0.875rem;
}
::-webkit-scrollbar {
  width: 0.625rem;
  border-radius: 0.75rem;
}

::-webkit-scrollbar-thumb {
  background-color: #a8a8a8;
  border-radius: 0.75rem;
}
body {
  position: relative;
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family:
    source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}
.react-toggle {
  touch-action: pan-x;

  display: inline-block;
  position: relative;
  cursor: pointer;
  background-color: transparent;
  border: 0;
  padding: 0;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
}

.react-toggle-screenreader-only {
  border: 0;
  clip: rect(0 0 0 0);
  height: 0.0625rem;
  margin: -0.0625rem;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 0.0625rem;
}

.react-toggle--disabled {
  cursor: not-allowed;
  opacity: 0.5;
  -webkit-transition: opacity 0.25s;
  transition: opacity 0.25s;
}

.react-toggle-track {
  width: 3.125rem;
  height: 1.5rem;
  padding: 0;
  border-radius: 1.875rem;
  background-color: #e4f1f7;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.react-toggle:hover:not(.react-toggle--disabled) .react-toggle-track {
  background-color: #eeeeee;
}

.react-toggle--checked .react-toggle-track,
.react-toggle--checked .react-toggle-track:hover {
  background-color: #4f46e5;
}

.react-toggle--checked:hover:not(.react-toggle--disabled) .react-toggle-track {
  background-color: #4f46e5;
}

.react-toggle-track-check {
  position: absolute;
  width: 0.875rem;
  height: 0.625rem;
  top: 0rem;
  bottom: 0rem;
  margin-top: auto;
  margin-bottom: auto;
  line-height: 0;
  left: 0.5rem;
  opacity: 0;
  -webkit-transition: opacity 0.25s ease;
  -moz-transition: opacity 0.25s ease;
  transition: opacity 0.25s ease;
}

.react-toggle--checked .react-toggle-track-check {
  opacity: 1;
  -webkit-transition: opacity 0.25s ease;
  -moz-transition: opacity 0.25s ease;
  transition: opacity 0.25s ease;
}

.react-toggle-track-x {
  position: absolute;
  width: 0.625rem;
  height: 0.625rem;
  top: 0rem;
  bottom: 0rem;
  margin-top: auto;
  margin-bottom: auto;
  line-height: 0;
  right: 0.625rem;
  opacity: 1;
  -webkit-transition: opacity 0.25s ease;
  -moz-transition: opacity 0.25s ease;
  transition: opacity 0.25s ease;
}

.react-toggle--checked .react-toggle-track-x {
  opacity: 0;
}

.react-toggle-thumb {
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1) 0ms;
  position: absolute;
  top: 0.0625rem;
  left: 0.0625rem;
  width: 1.375rem;
  height: 1.375rem;
  border: 0.0625rem solid #fafafa;
  border-radius: 50%;
  background-color: #fafafa;

  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;

  -webkit-transition: all 0.25s ease;
  -moz-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

.react-toggle--checked .react-toggle-thumb {
  left: 1.6875rem;
  border-color: #4f46e5;
}

.react-toggle--focus .react-toggle-thumb {
  -webkit-box-shadow: 0rem 0rem 0.1875rem 0.125rem #4f46e5;
  -moz-box-shadow: 0rem 0rem 0.1875rem 0.125rem #4f46e5;
  box-shadow: 0rem 0rem 0.125rem 0.1875rem #4f46e5;
}

.react-toggle:active:not(.react-toggle--disabled) .react-toggle-thumb {
  -webkit-box-shadow: 0rem 0rem 0.3125rem 0.3125rem #4f46e5;
  -moz-box-shadow: 0rem 0rem 0.3125rem 0.3125rem #4f46e5;
  box-shadow: 0rem 0rem 0.3125rem 0.3125rem #4f46e5;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.custom-tooltip {
  max-width: 200px; /* Set a fixed width */
  white-space: pre-wrap; /* Allow text to wrap to the next line */
  word-wrap: break-word; /* Break long words */
}
.sidebar-holder {
  width: 100%;
  min-width: 15rem;
  max-width: 15rem;
  position: relative;
  background: #151515;
  color: #fff;
  z-index: 2;
  /* transition: all 0.3s; */
  min-height: 100vh;
  overflow: hidden;
  transition: 0.2s;
}
.open-nav {
  min-width: 0rem !important;
  max-width: 0rem !important;
  width: 0 !important;
  transition: 0.2s;
  opacity: 0;
}

.sidebar-list ul li a {
  padding: 0.625rem;
  display: block;
  width: 100%;
  font-size: 0.875rem;
  font-weight: 600;
  transition: 0.2s ease-in;
  text-transform: capitalize;
}

.sidebar-list .active-nav {
  padding: 0.75rem;
  color: #262626;
  border-radius: 0.375rem;
  background: #f4f4f4;
}

.sidebar-list .active-nav:hover {
  background: #f4f4f4;
}

.sidebar-list ul li a:hover {
  color: #262626;
}

.page-header {
  width: 100%;
  padding: 1.25rem;
  background: white;
}
.page-header span {
  cursor: pointer;
  display: block;
  width: fit-content;
  font-size: 1.25rem;
}

.center-svg {
  aspect-ratio: 1/1;
  align-items: center;
  justify-content: center;
  line-height: 1.2em !important;
}

.uppy-Dashboard-inner {
  width: 100% !important;
}

@media screen and (max-width: 47.9375rem) {
  .sidebar-holder {
    width: 100%;
    min-width: 12.5rem;
    max-width: 12.5rem;
    position: fixed;
    top: 0;
    left: 0;
  }
  .page-header span {
    margin-left: auto;
  }
}

.tiptap ul,
.tiptap ol,
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 2rem;
  margin-top: 10px;
  margin-bottom: 10px;
}
.tiptap ul,
.ProseMirror ul {
  list-style-type: disc;
}
.tiptap ol,
.ProseMirror ol {
  list-style-type: decimal;
}
.tiptap li,
.ProseMirror li {
  margin-bottom: 0.25em;
  font-size: 1rem;
  color: #374151;
}

/* Headings for .custom, .tiptap, and .ProseMirror
.custom h1, .tiptap h1, .ProseMirror h1 {
  font-size: 2rem;
  font-weight: bold;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: #1f2937;
}
.custom h2, .tiptap h2, .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1.2em;
  margin-bottom: 0.5em;
  color: #1f2937;
}
.custom h3, .tiptap h3, .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
  color: #1f2937;
}
.custom h4, .tiptap h4, .ProseMirror h4 {
  font-size: 1.1rem;
  font-weight: bold;
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  color: #1f2937;
}
.custom h5, .tiptap h5, .ProseMirror h5 {
  font-size: 1rem;
  font-weight: bold;
  margin-top: 0.7em;
  margin-bottom: 0.3em;
  color: #1f2937;
}
.custom h6, .tiptap h6, .ProseMirror h6 {
  font-size: 0.8rem;
  font-weight: bold;
  margin-top: 0.6em;
  margin-bottom: 0.2em;
  color: #1f2937;
} */
